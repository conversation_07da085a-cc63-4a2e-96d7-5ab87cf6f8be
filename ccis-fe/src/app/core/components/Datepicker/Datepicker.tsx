import React, { FC } from 'react';

type DatePickerProps = {
    label: string;
    value: Date | null;
    onChange: (value: Date | null) => void;
};

const DatePicker: FC<DatePickerProps> = ({ label, value, onChange }) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const date = e.target.value ? new Date(e.target.value) : null;
        onChange(date);
    };

    return (
        <div className="mb-2">
            <label className="block text-sm font-medium text-gray">{label}</label>
            <div className="relative">
                <input
                    type="date"
                    className="input bg-white border border-primary w-full rounded-md"
                    value={value ? value.toISOString().split('T')[0] : ''}
                    onChange={handleChange}
                />
            </div>
        </div>
    );
};

export default DatePicker;
