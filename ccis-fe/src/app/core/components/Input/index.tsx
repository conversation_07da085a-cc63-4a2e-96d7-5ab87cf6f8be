import React, { ChangeEvent } from "react";

interface ReusableInputProps {
  type: string;
  placeholder?: string;
  value: string | number;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  label?: string;
  className?: string;
}

const Input: React.FC<ReusableInputProps> = ({ type, placeholder, value, onChange, name, label, className }) => {
  return (
    <div className={`input-container w-full ${className}`}>
      {label && <label htmlFor={name}>{label}</label>}
      <input type={type} placeholder={placeholder} value={value} onChange={onChange} name={name} id={name} className="input input-bordered w-full bg-zinc-50 text-slate-950" />
    </div>
  );
};

export default Input;
