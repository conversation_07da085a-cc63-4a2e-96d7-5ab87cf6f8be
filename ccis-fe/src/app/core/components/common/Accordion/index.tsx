import React, { createContext, useContext, useState } from "react";

type AccordionContextType = {
  openItems: Record<string, boolean>;
  toggleItem: (id: string) => void;
};

const AccordionContext = createContext<AccordionContextType | undefined>(
  undefined
);

export interface AccordionProps {
  children: React.ReactNode;
  type?: "single" | "multiple";
  defaultValue?: string | string[];
  className?: string;
}

export const Accordion: React.FC<AccordionProps> = ({
  children,
  type = "single",
  defaultValue = [],
  className = "",
}) => {
  const initialState: Record<string, boolean> = {};

  if (type === "single" && typeof defaultValue === "string") {
    initialState[defaultValue] = true;
  } else if (type === "multiple" && Array.isArray(defaultValue)) {
    defaultValue.forEach((id) => {
      initialState[id] = true;
    });
  }

  const [openItems, setOpenItems] =
    useState<Record<string, boolean>>(initialState);

  const toggleItem = (id: string) => {
    if (type === "single") {
      setOpenItems((prev) => {
        const newState: Record<string, boolean> = {};
        newState[id] = !prev[id];
        return newState;
      });
    } else {
      setOpenItems((prev) => ({
        ...prev,
        [id]: !prev[id],
      }));
    }
  };

  return (
    <AccordionContext.Provider value={{ openItems, toggleItem }}>
      <div className={`accordion ${className}`}>{children}</div>
    </AccordionContext.Provider>
  );
};

export interface AccordionItemProps {
  children: React.ReactNode;
  value: string;
  className?: string;
}

export const AccordionItem: React.FC<AccordionItemProps> = ({
  children,
  value,
  className = "",
}) => {
  const context = useContext(AccordionContext);

  if (!context) {
    throw new Error("AccordionItem must be used within an Accordion");
  }

  const isOpen = context.openItems[value] || false;

  return (
    <div
      className={`accordion-item ${isOpen ? "open" : "closed"} ${className}`}
      data-state={isOpen ? "open" : "closed"}
      data-value={value}
    >
      {children}
    </div>
  );
};

export interface AccordionTriggerProps {
  children: React.ReactNode;
  className?: string;
}

export const AccordionTrigger: React.FC<AccordionTriggerProps> = ({
  children,
  className = "",
}) => {
  const context = useContext(AccordionContext);

  if (!context) {
    throw new Error("AccordionTrigger must be used within an AccordionItem");
  }

  // Get the parent AccordionItem's value to determine if it's open
  const triggerRef = React.useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = React.useState(false);

  React.useEffect(() => {
    if (triggerRef.current) {
      const item = triggerRef.current.closest("[data-value]");
      if (item) {
        const value = item.getAttribute("data-value");
        if (value) {
          setIsOpen(context.openItems[value] || false);
        }
      }
    }
  }, [context.openItems]);

  const handleClick = (e: React.MouseEvent) => {
    const item = (e.currentTarget as HTMLElement).closest("[data-value]");
    if (item) {
      const value = item.getAttribute("data-value");
      if (value) {
        context.toggleItem(value);
      }
    }
  };

  return (
    <div
      ref={triggerRef}
      className={`accordion-trigger ${className}`}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-expanded={isOpen}
    >
      {children}
    </div>
  );
};

export interface AccordionContentProps {
  children: React.ReactNode;
  className?: string;
}

export const AccordionContent: React.FC<AccordionContentProps> = ({
  children,
  className = "",
}) => {
  const context = useContext(AccordionContext);

  if (!context) {
    throw new Error("AccordionContent must be used within an AccordionItem");
  }

  // Get the closest AccordionItem parent to determine if it's open
  const itemElement = React.useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = React.useState(false);

  React.useEffect(() => {
    if (itemElement.current) {
      const item = itemElement.current.closest("[data-value]");
      if (item) {
        const value = item.getAttribute("data-value");
        if (value) {
          setIsOpen(context.openItems[value] || false);
        }
      }
    }
  }, [context.openItems]);

  return (
    <div
      ref={itemElement}
      className={`accordion-content ${className}`}
      data-state={isOpen ? "open" : "closed"}
      style={{ display: isOpen ? "block" : "none" }}
    >
      {children}
    </div>
  );
};

export default Accordion;
