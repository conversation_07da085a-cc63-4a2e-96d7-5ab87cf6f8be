// Import dependencies
import { IActions } from "@interface/common.interface";
import Tooltip from "../Tooltip";

// Define the props type for the ActionButton component
type TActionButton<T> = {
  actions?: IActions<T>[];
  data: any;
  rowIndex: number;
};

/**
 * @param actions - Array of action items implementing the IActions interface (default: empty array)
 * @param data - Data associated with the actions implement IUser interface
 */
const ActionButtons = <T,>({
  actions = [],
  data,
  rowIndex,
}: TActionButton<T>) => {
  return (
    <div className="flex flex-1 flex-row ">
      {actions.map((value, index) => (
        <Tooltip
          key={`btn-actions-${index}`}
          text={value.name}
          position="top"
          className={`${
            value.disabled ? "cursor-not-allowed hover:bg-white" : ""
          }`}
        >
          <button
            type={value.buttonType || "submit"}
            className={`btn btn-ghost btn-sm flex justify-center hover:bg-transparent text-${value.color} 
            `}
            onClick={() => !value.disabled && value.event(data, rowIndex)}
            disabled={value.disabled}
          >
            <value.icon size={30} className="p-1" />
          </button>
        </Tooltip>
      ))}
    </div>

    // -- Original implementation
    // <div className="flex flex-1 flex-row">
    //   {actions.map((value, index) => (
    //     <Tooltip key={`btn-actions-${index}`} text={value.name} position="top">
    //       <button
    //         className={`btn btn-ghost btn-xs flex justify-center hover:bg-transparent text-${value.color}`}
    //         onClick={() => value.event(data, rowIndex)}
    //       >
    //         <value.icon size={30} className="p-1" />
    //       </button>
    //     </Tooltip>
    //   ))}
    // </div>
  );
};

// Exporting ActionButtons component as default
export default ActionButtons;
