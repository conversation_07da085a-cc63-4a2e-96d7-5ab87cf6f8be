import { IActions } from "@interface/common.interface"
import { ReactNode } from "react";
import { BsThreeDots } from "react-icons/bs"

// Define the props type for the ActionDropdown component
type TActionDropdown<T> = {
  label?: string;
  actions?: IActions<T>[];
  rowIndex: number;
  data: any;
  icon?: ReactNode;
}

/**
 * @param label - Label for the actions section (default: "Actions")
 * @param actions - Array of action items implementing the IActions interface(default: empty array)
 */
const ActionDropdown = <T,>({ label = "", actions = [], rowIndex, data, icon = <BsThreeDots /> }: TActionDropdown<T>) => {
  return (
    <div className="flex flex-1 justify-center">
      <div className="dropdown dropdown-bottom dropdown-end">
        <div tabIndex={0} role="button" className="btn btn-ghost btn-circle btn-sm m-1">{label}<div>{icon}</div></div>
        <ul tabIndex={0} className="dropdown-content z-10 menu bg-slate-100 p-0 min-w-52 hover:cursor-pointer ">
          {
            actions.map((value, index) => {
              return <div key={`action-menu-${index}`} className={`p-1 flex flex-row items-center shadow hover:bg-${value.color} hover:text-white`} onClick={() => value.event(data, rowIndex)}>
                {value.icon && <div className="mx-2"><value.icon /></div>}
                <div className="mt-1">
                  {value.name}
                </div>
              </div>
            })
          }
        </ul>
      </div>
    </div>
  );
};

// Exporting ActionDropdown component as default
export default ActionDropdown;
