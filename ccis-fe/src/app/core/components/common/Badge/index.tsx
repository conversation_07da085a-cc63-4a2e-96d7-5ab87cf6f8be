// Import necessary modules and types from classNames library and React
import classNames from "classnames";
import { FC, ReactNode } from "react";

// Define the type for the Badge component props
type TBadgeProps = {
    text: string;                         // Text to be displayed inside the badge
    variant?: "primary" | "secondary" | "accent" | "success" | "error"; // Optional variant for badge styling
    size?: "xs" | "sm" | "md" | "lg" | "xl"; // Optional size for the badge
    outline?: boolean;                    // Optional flag to render the badge with an outline style
    rightIcon?: ReactNode;                // Optional icon to be displayed on the right side of the badge
    leftIcon?: ReactNode;                 // Optional icon to be displayed on the left side of the badge
    className?: string;                   // Optional additional class names for the badge
};

// Define the Badge component using the functional component syntax
const Badge: FC<TBadgeProps> = ({
    text,                                 // Text to be displayed inside the badge
    variant = "primary",                  // Default variant is 'primary' if not provided
    size = "xs",                          // Default size is 'xs' if not provided
    outline = false,                      // Default outline is 'false' if not provided
    rightIcon,                            // Icon to be displayed on the right side of the badge
    leftIcon,                             // Icon to be displayed on the left side of the badge
    className                             // Additional class names for the badge
}) => {
    // Combine class names for the badge based on variant, size, outline, and additional classes
    const classes = classNames(
        "badge p-2", 
        `badge-${variant}`, 
        `badge-${size}`, 
        { 'badge-outline': outline }, 
        className
    );

    return (
        // Render the badge with combined class names
        <div className={classes}>
            {/* Render left icon if provided */}
            {leftIcon && <div className="mr-2">{leftIcon}</div>}
            {/* Render badge text */}
            <span>{text}</span>
            {/* Render right icon if provided */}
            {rightIcon && <div className="ml-2">{rightIcon}</div>}
        </div>
    );
};

// Export the Badge component as the default export
export default Badge;
