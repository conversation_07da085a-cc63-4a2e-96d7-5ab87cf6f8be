import ccisLogo from "@assets/logo.svg";
import climbsLogo from "@assets/logo2.png";
import climbsLogo2 from "@assets/logo3.jpg";
import crystal_connect from "@assets/login/logo/crystal_connect_logo.svg";

import { FC } from "react";

export interface IBrandProps {
  width: number;
  height: number;
  type?: number;
}

const Brand: FC<IBrandProps> = ({ width = 100, height = 100, type = 2 }) => {
  return (
    <>
      {type === 1 && <img src={climbsLogo} width={width} height={height} alt="CCIS logo" />}
      {type === 2 && <img src={ccisLogo} width={width} height={height} alt="Climbs logo" />}
      {type === 3 && <img src={climbsLogo2} width={width} height={height} alt="Climbs logo" />}
      {type === 4 && <img src={crystal_connect} width={width} height={height} alt="Climbs logo" />}
    </>
  );
};

export default Brand;
