import React from "react";
import { Link } from "react-router-dom";

interface BreadcrumbItem {
  label: string;
  link?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
  return (
    <div className="breadcrumbs text-sm border-b border-gray-200 pb-4 mb-8">
      <ul>
        {items.map((item, index) => (
          <li key={index}>
            {item.link ? <Link to={item.link}>{item.label}</Link> : item.label}
          </li>
        ))}
      </ul>
    </div>
  );
};
