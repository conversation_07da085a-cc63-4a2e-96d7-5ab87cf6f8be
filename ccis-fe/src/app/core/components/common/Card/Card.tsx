// user-acceptance-test/components/Card/Card.tsx

import  { FC } from 'react';

type CardProps = {
    number: number;
    label: string;
};

const Card: FC<CardProps> = ({ number, label }) => {
    return (
        <div className="bg-white shadow-md rounded-lg p-4 flex items-center gap-4">
            <div className="flex items-center justify-center h-12 w-12 bg-blue-500 text-white rounded-lg">
                <div className="text-2xl font-bold">{number}</div>
            </div>
            <div className="text-gray-600 text-center">{label}</div>
        </div>
    );
};

export default Card;
