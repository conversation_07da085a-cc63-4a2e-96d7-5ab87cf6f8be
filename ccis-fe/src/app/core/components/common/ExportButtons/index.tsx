import { FC, useState } from 'react';
import { FiDownload } from 'react-icons/fi';
import httpClient from '@clients/httpClient';

interface Props {
  pdfApi?: string; // API endpoint for the PDF export
  pdfQueryParams?: string; // Optional query parameters for PDF export (as a query string)
  csvApi?: string; // API endpoint for the CSV export
  csvQueryParams?: string; // Optional query parameters for CSV export (as a query string)
}

const ExportDropdown: FC<Props> = ({ pdfApi, pdfQueryParams, csvApi, csvQueryParams }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

  const toggleDropdown = () => {
    setIsDropdownOpen((prev) => !prev);
  };

  //handle export logic
  const handleExport = async (fileType: 'pdf' | 'csv', apiUrl: string, queryParams?: string) => {
    try {
      const endpoint = queryParams ? `${apiUrl}?${queryParams}` : apiUrl;
      const response: any = await httpClient.get(endpoint, {
        responseType: 'blob',
      });

      const blobType = fileType === 'pdf' ? 'application/pdf' : 'text/csv';
      const blob = new Blob([response], { type: blobType });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (error) {
      console.error(`${fileType.toUpperCase()} export failed:`, error);
    }
  };

  return (
    <div className="dropdown dropdown-end">
      <button className="text-zinc-950 border border-zinc-200 btn btn-sm bg-white" onClick={toggleDropdown}>
        <FiDownload />
      </button>
      <ul
        tabIndex={0}
        className={`dropdown-content menu bg-base-100 pl-0 rounded-box z-[100] w-52 shadow transition-opacity duration-300 ${
          isDropdownOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
      >
        {pdfApi && (
          <li className='pl-0 pr-1'>
            <button className='text-primary' onClick={() => handleExport('pdf', pdfApi, pdfQueryParams)}>Export PDF</button>
          </li>
        )}
        {csvApi && (
          <li className='pl-0 pr-1'>
            <button className='text-primary' onClick={() => handleExport('csv', csvApi, csvQueryParams)}>Export CSV</button>
          </li>
        )}
      </ul>
    </div>
  );
};

export default ExportDropdown;
