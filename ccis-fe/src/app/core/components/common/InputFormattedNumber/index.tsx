import React, { useState, useEffect, useRef } from "react";

interface FormattedNumberInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  number?: boolean;
  isNegative?: boolean;
  dataList?: string[];
  sizeClass?: "xs" | "sm" | "md" | "lg" | "xl"; // Rename size prop to sizeClass
}

const formatWithCommas = (value: string): string => {
  const numericValue = value.replace(/,/g, "");
  if (!numericValue) return "";
  const [integer, decimal] = numericValue.split(".");
  const formatted = Number(integer).toLocaleString();
  return decimal !== undefined ? `${formatted}.${decimal}` : formatted;
};

const calculateCursorPosition = (oldValue: string, newValue: string, oldPos: number): number => {
  const oldCommasBeforeCursor = (oldValue.substring(0, oldPos).match(/,/g) || []).length;
  const rawPosition = oldPos - oldCommasBeforeCursor;
  let newPos = 0;
  let rawCount = 0;
  for (let i = 0; i < newValue.length && rawCount < rawPosition; i++) {
    if (newValue[i] !== ",") {
      rawCount++;
    }
    newPos = i + 1;
  }
  return newPos;
};

const FormattedNumberInput: React.FC<FormattedNumberInputProps> = ({
  value,
  onChange,
  className = "",
  number = true,
  isNegative = false,
  sizeClass = "", // Default sizeClass is "md"
  ...props
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [internal, setInternal] = useState<string>("");
  const [lastRawValue, setLastRawValue] = useState<string>("");

  useEffect(() => {
    const stringVal = typeof value === "number" ? value.toString() : value || "";
    const rawValue = stringVal.replace(/,/g, "");
    if (rawValue !== lastRawValue) {
      setInternal(formatWithCommas(stringVal));
      setLastRawValue(rawValue);
    }
  }, [value, lastRawValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const raw = inputValue.replace(/,/g, "");
    if (raw !== "" && !/^\d*\.?\d*$/.test(raw)) return;
    const cursorPos = e.target.selectionStart || 0;
    const oldFormatted = internal;
    const formatted = formatWithCommas(raw);
    setInternal(formatted);
    setLastRawValue(raw);
    if (inputRef.current && cursorPos !== null) {
      const newCursorPos = calculateCursorPosition(oldFormatted, formatted, cursorPos);
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
        }
      }, 0);
    }
    const synthetic = {
      ...e,
      target: {
        ...e.target,
        value: raw,
      },
    };
    onChange(synthetic as React.ChangeEvent<HTMLInputElement>);
  };

  // Map size prop to Tailwind CSS classes
  const sizeMap: { [key: string]: string } = {
    xs: "text-xs p-1",
    sm: "text-sm p-2",
    md: "text-base p-2.5",
    lg: "text-lg p-4",
    xl: "text-xl p-5",
  };

  return (
    <input
      {...props}
      ref={inputRef}
      type="text"
      value={internal}
      onChange={handleChange}
      data-number={number}
      className={`${className} ${sizeMap[sizeClass]} w-full p-2 px-4 border-2 border-zinc-300 rounded-md focus:outline-none focus:ring-2 focus:ring-zinc-400`} // Apply size class
    />
  );
};

export default FormattedNumberInput;
