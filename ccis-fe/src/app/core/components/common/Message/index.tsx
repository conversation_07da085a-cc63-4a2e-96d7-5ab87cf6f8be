import { FC, ReactNode } from "react";
import { HiOutlineEnvelope } from "react-icons/hi2";

type TMessageProps = {
  icon?: ReactNode;
  count?: number;
};

const Message: FC<TMessageProps> = ({ icon = <HiOutlineEnvelope size={25} />, count = 0 }) => {
  return (
    <div tabIndex={0} role="button" className="btn btn-ghost btn-circle">
      <div className="indicator">
        <div className=" p-2">
          {icon && icon}
          <span className="badge badge-sm indicator-item bg-red-600 text-white">{count}</span>
        </div>
      </div>
    </div>
  );
};

export default Message;
