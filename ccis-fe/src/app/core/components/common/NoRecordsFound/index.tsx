import { Fragment } from 'react'
import Typography from '../Typography'
import { TbDatabaseSearch } from 'react-icons/tb'

type Props = {
    text?: string
}

export default function NoRecordsFound({ text = "No records found" }: Props) {
    return (
        <Fragment>
            <div className="flex flex-col justify-center items-center">
                <TbDatabaseSearch className="text-5xl text-gray-400" />
                <Typography className='mt-4 font-semibold' size='lg'>
                    {text}
                </Typography>
            </div>
        </Fragment>
    )
}