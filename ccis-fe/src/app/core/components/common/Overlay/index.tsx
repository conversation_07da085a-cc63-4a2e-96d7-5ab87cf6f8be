import { FC } from "react";
import { Blocks } from "react-loader-spinner";

type Props = {
  header?: string;
  bodyText?: string;
};

const Overlay: FC<Props> = ({
  header = "Loading...",
  bodyText = "We are preparing your request. This may take a few seconds, please don't close this page.",
}) => {
  return (
    <div className="fixed top-0 left-0 right-0 bottom-0 w-full h-full z-50 overflow-hidden bg-slate-900 opacity-75 flex flex-col items-center justify-center">
      <Blocks color="#3266DC" />
      <h2 className="text-center text-white text-xl font-semibold">{header}</h2>
      <p className="w-1/3 text-center text-white">{bodyText}</p>
    </div>
  );
};

export default Overlay;
