import classNames from 'classnames'
import { FC, Fragment } from 'react'

type TProgressProps = {
  value?: string | number,
  max?: string | number,
  variant?: "primary" | "secondary" | "accent" | "success" | "warning" | "error",
  className?: string,
}

const Progress: FC<TProgressProps> = ({
  value = "0",
  max = "100",
  variant = "primary",
  className,
}) => {

  const classes = classNames(`progress !progress-${variant}`, className);

  return (
    <Fragment>
      <progress className={classes} value={value} max={max}></progress>
    </Fragment>
  )
}

export default Progress