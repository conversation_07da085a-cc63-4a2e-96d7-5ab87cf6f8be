import classNames from 'classnames';
import { FC, Fragment, useRef } from 'react'
import SignaturePad from 'react-signature-pad-wrapper';
import Typography from '../Typography';
import { formatDataURLToBlob } from '@helpers/file';
import { useSelector } from 'react-redux';
import { RootState } from '@state/store';
import LoadingButton from '../LoadingButton';

type TSignaturePadDrawProps = {
  className?: string;
  onSave?: (blob: Blob) => void;
}

const SignaturePadDraw: FC<TSignaturePadDrawProps> = ({
  className,
  onSave
}) => {

  const { loading } = useSelector((state: RootState) => state.profile.uploadUserSignature);
  const classes = classNames(className)
  const padRef = useRef<SignaturePad | null>(null);

  const handleSave = () => {
    if (padRef.current?.isEmpty()) return;

    const dataURL = padRef?.current?.toDataURL() ?? "";
    const blob = formatDataURLToBlob(dataURL);

    if (onSave) {
      onSave(blob);
    }
  }

  return (
    <Fragment>
      <Typography className='text-center'>Draw your signature below</Typography>
      <div className={classes}>
        <SignaturePad ref={padRef} height={200} />
      </div>
      <div className='flex flex-1 justify-end mt-2'>
        <LoadingButton type='button' isLoading={loading} className='btn !bg-slate-400 hover:!bg-primary !w-32 mr-2' onClick={() => padRef.current?.clear()}>Clear</LoadingButton>
        <LoadingButton type='button' isLoading={loading} className='btn !bg-slate-400 hover:!bg-primary !w-32' onClick={handleSave}>Save</LoadingButton>
      </div>
    </Fragment>

  )
}

export default SignaturePadDraw;