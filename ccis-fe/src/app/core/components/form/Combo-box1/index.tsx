import { FC, MouseEvent, ChangeEvent, FocusEvent, Fragment } from "react";
import ErrorText from "@components/common/ErrorText";
import classNames from "classnames";

type TOptions = {
  value: string | number;
  text: string;
};

type TSelectProps = {
  options?: TOptions[];
  value?: string | number;
  placeholder?: string;
  name?: string;
  id?: string;
  required?: boolean;
  readOnly?: boolean;
  className?: string;
  variant?: "primary" | "secondary" | "danger" | "default";
  size?: "xs" | "sm" | "md" | "lg";
  disabled?: boolean;
  error?: boolean;
  errorText?: string;
  errorIcon?: boolean;
  onClick?: (e: MouseEvent<HTMLSelectElement>) => void;
  onChange?: (e: ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: FocusEvent<HTMLSelectElement>) => void;
};

const ComboBox: FC<TSelectProps> = ({
  options = [],
  value = "",
  placeholder = "Select an option",
  name,
  id,
  required = false,
  readOnly = false,
  className = "",
  variant = "primary",
  size = "md",
  disabled = false,
  error = false,
  errorText,
  errorIcon = false,
  onChange = () => {},
  onClick = () => {},
  onBlur = () => {},
}) => {
  // Generate the CSS classes for the select element based on props
  const selectClass = classNames("select select-bordered w-full", `select-${variant}`, `select-${size}`, { "!select-error": error }, className);

  return (
    <Fragment>
      <select
        name={name}
        id={id}
        value={value} // Use value prop for controlled component
        className={selectClass}
        onChange={onChange}
        onClick={onClick}
        onBlur={onBlur}
        disabled={disabled || readOnly}
        required={required}
      >
        {/* Default disabled placeholder option */}
        {/* magamit pani later */}
        <option disabled value="">
          {placeholder}
        </option>

        {/* Maps through options array to generate option elements */}
        {options.map((opt, index) => (
          <option key={`opt-${index}`} value={opt.value}>
            {opt.text}
          </option>
        ))}
      </select>
      {/* Displays error message if error prop is true */}
      {error && <ErrorText text={errorText} withIcon={errorIcon} />}
    </Fragment>
  );
};

export default ComboBox;
