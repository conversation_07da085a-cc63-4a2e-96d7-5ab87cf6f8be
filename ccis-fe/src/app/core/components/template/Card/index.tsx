import { ArrowUpRight } from "lucide-react"

export interface InventoryMetric {
  title: string
  value: number
  variant?: "primary" | "default"
  showIcon?: boolean
}

interface InventoryMetricCardProps {
  metric: InventoryMetric
  loading?: boolean
}

export function InventoryMetricCard({ metric }: InventoryMetricCardProps) {
  return (
    <div
      className={`card ${metric.variant === "primary" ? "rounded-lg bg-gradient-to-t from-primary3 to-primary" : "rounded-lg bg-base-100 border border-base-300"} shadow-sm`}
    >
      <div className="card-body p-[1px]">
        <div className="flex items-start justify-between">
          <div className="space-y-8">
            <p
              className={`text-sm font-normal ${
                metric.variant === "primary" ? "text-white" : "text-base-content/60"
              }`}
            >
              {metric.title}
            </p>
            <p
              className={`text-4xl font-extrabold ${metric.variant === "primary" ? "text-white" : "text-base-content"}`}
            >
              {metric.value}
            </p>
          </div>
          {metric.showIcon && (
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full border border-base-300 flex items-center justify-center">
                <ArrowUpRight className="w-4 h-4 text-base-content/60" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
