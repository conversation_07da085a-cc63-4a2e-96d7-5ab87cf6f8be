import React, { ChangeEvent, useEffect, useState, useMemo } from "react";
import { useDebouncedCallback } from "use-debounce";
import Table from "@components/common/Table";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { TableColumn } from "react-data-table-component";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { FormStatus, RoleType } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
// import { IIncomingReceivedForm } from "@interface/form-inventory.interface";
import { InventoryMetric } from "@components/template/Card";

// Import our reusable components and hooks
import FilterPanel from "@components/template/FilterPanel";
import MetricsGrid from "@components/template/MetricsGrid";
import { useRoleBasedInventoryData } from "@hooks/useRoleBasedInventoryData";
import { getAdminOutgoingLogic, getHead<PERSON><PERSON>erLogic, getIocLogic, getTransmittalInventoryLogic } from "@hooks/useStatusLogic";
import { useRoleBasedActions } from "@hooks/useRoleBasedActions";
import { getRoleBasedColumns } from "../TableColumns";

interface GenericInventoryProps {
  userRole: RoleType;
  title?: string;
  description?: string;
  additionalFilters?: any;
  customColumns?: TableColumn<any>[];
  customMetrics?: InventoryMetric[];
}

const GenericInventoryTab: React.FC<GenericInventoryProps> = ({
  userRole,
  title = "INVENTORY (ON-HAND)",
  description = "This page lists all forms across all received series. Use filters to narrow down results by status, division, or type.",
  additionalFilters = {},
  customColumns,
  customMetrics,
}) => {
  const [filters, setFilters] = useState<IDefaultParams>({
    filter: "",
    divisionFilter: undefined,
    type: undefined,
    statusFilter: undefined,
    dateFrom: "",
    dateTo: "",
    page: 1,
    pageSize: 10,
    ...additionalFilters,
  });

  const [resetCounter, setResetCounter] = useState(0);

  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);

  const { allFormsData, isDataLoading, fetchForms, userId } = useRoleBasedInventoryData({
    filters,
    userRole,
    additionalFilters,
  });

  const { getIocStatus: iocStatus } = getIocLogic();
  const { getHeadCashierStatus: headCashierStatus } = getHeadCashierLogic();
  const { getTransmittalInventoryStatus: transmittalInventoryStatus } = getTransmittalInventoryLogic();
  const { getAdminOutgoingStatus: adminOutgoingStatus } = getAdminOutgoingLogic();
  const { getActionEvents: getRoleBasedActions } = useRoleBasedActions();

  

  const updateFilter = (key: keyof IDefaultParams, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const divisionOptions = [
        { value: 0, text: "Select Division" },
    ...divisions.map((value) => {
          return { value: value.id, text: value.divisionName };
        }),
  ];

  const typeOptions = [
    { value: 0, text: "Select Type" },
    ...formTypes.map((value) => {
      return { value: value.id, text: value.formTypeName };
    }),
  ];

  const statusOptions = [
    { value: "", text: "All Statuses" },
    { value: FormStatus.FOR_RECEIVING, text: "For Receiving" },
    { value: FormStatus.ON_HAND, text: "On-Hand" },
    { value: FormStatus.RELEASED, text: "Released" },
    { value: FormStatus.NOT_YET_RECEIVED, text: "Not Yet Received" },
  ];

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("filter", event.target.value);
  }, 500);

  const handleDivisionChange = (event: ChangeEvent<HTMLSelectElement>) => {
    updateFilter("divisionFilter", parseInt(event.target.value));
  };

  const handleTypeChange = (event: ChangeEvent<HTMLSelectElement>) => {
    updateFilter("type", parseInt(event.target.value));
  };

  const handleStatusChange = (event: ChangeEvent<HTMLSelectElement>) => {
    updateFilter("statusFilter", event.target.value || undefined);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("dateFrom", event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    updateFilter("dateTo", event.target.value);
  };

  const handleClearAll = () => {
    setFilters({
      filter: "",
      divisionFilter: undefined,
      type: undefined,
      statusFilter: undefined,
      dateFrom: "",
      dateTo: "",
      page: 1,
      pageSize: 10,
      ...additionalFilters,
    });
    setResetCounter((prev) => prev + 1);
  };

  const handleRowsChange = (newPerPage: number, pagination: number) => {
    updateFilter("pageSize", newPerPage);
    updateFilter("page", pagination);
  };

  const handlePaginate = (page: number) => {
    updateFilter("page", page);
  };

  const getDisplayStatus = (row: any) => {
    // Role-specific status logic
    switch (userRole) {
      case RoleType.IOC:
        return iocStatus(row);
      case RoleType.CHIEFCASHIER:
        return headCashierStatus(row);
      case RoleType.CLIFSA:
        return transmittalInventoryStatus(row, userId);
      case RoleType.ADMINOUTGOING:
        return adminOutgoingStatus(row, userId);
      case RoleType.GAM:
        return row.status === FormStatus.RECEIVED ? FormStatus.ON_HAND : row.status || "Unknown";
      default:
        return row.status || "Unknown";
    }
  };

  const inventoryMetrics: InventoryMetric[] = useMemo(() => {
    // if (customMetrics) return customMetrics;

    if (isDataLoading) {
      // Return default
      if (userRole === RoleType.IOC) {
        return [
          { title: "Total Pad", value: 0, variant: "primary" as const },
          { title: "Incoming Received", value: 0, showIcon: true },
          { title: "Verified", value: 0, showIcon: true },
          { title: "Denied", value: 0, showIcon: true },
        ];
      } else if (userRole === RoleType.CHIEFCASHIER) {
        return [
          { title: "Total Pads", value: 0, variant: "primary" as const },
          { title: "Verify List", value: 0, showIcon: true },
          { title: "Verified", value: 0, showIcon: true },
          { title: "Rejected", value: 0, showIcon: true },
          { title: "Released", value: 0, showIcon: true },
        ]
      }else {
        return [
          { title: "Total Pads", value: 0, variant: "primary" as const },
          { title: "For Receiving", value: 0, showIcon: true },
          { title: "On-Hand", value: 0, showIcon: true },
          { title: "Released", value: 0, showIcon: true },
        ];
      }
    }

    // IC/OG
    if (userRole === RoleType.IOC) {
      let incomingReceived = 0;
      let verified = 0;
      let denied = 0;
      let totalPads = 0;

      allFormsData.forEach((form: IFormTransmittal) => {
        const displayStatus = getDisplayStatus(form);
        const formPadCount = form.padAssignments?.length || 0;
        totalPads += formPadCount;

        // IOC-specific status counting
        if (displayStatus === FormStatus.INCOMING_RECEIVED) {
          incomingReceived += formPadCount;
        } else if (displayStatus === FormStatus.VERIFIED) {
          verified += formPadCount;
        } else if (displayStatus === FormStatus.DENIED) {
          denied += formPadCount;
        }
      });

      return [
        { title: "Total Pad", value: totalPads, variant: "primary" as const },
        { title: "Incoming Received", value: incomingReceived, showIcon: true },
        { title: "Verified", value: verified, showIcon: true },
        { title: "Denied", value: denied, showIcon: true },
      ];
    }


    // Chief Cashier
    if (userRole === RoleType.CHIEFCASHIER) {
      let verify_list = 0;
      let verified = 0;
      let rejected = 0;
      let released = 0;
      let totalPads = 0;

      allFormsData.forEach((form: IFormTransmittal) => {
        const displayStatus = getDisplayStatus(form);
        const formPadCount = form.padAssignments?.length || 0;
        totalPads += formPadCount;

        if (displayStatus === FormStatus.VERIFY_LIST) {
          verify_list += formPadCount;
        } else if (displayStatus === FormStatus.VERIFIED) {
          verified += formPadCount;
        } else if (displayStatus === FormStatus.REJECTED) {
          rejected += formPadCount;
        }
        
        // Count released pads individually (not equal to PENDING)
        form.padAssignments?.forEach((padAssignment) => {
          const padStatus = padAssignment.status;
          if (padStatus !== FormStatus.PENDING) {
            released++;
            verified--;
          }
          
        });
      });

      return [
        { title: "Total Pad", value: totalPads, variant: "primary" as const },
        { title: "Verify List", value: verify_list, showIcon: true },
        { title: "Verified", value: verified, showIcon: true },
        { title: "Denied", value: rejected, showIcon: true },
        { title: "Released", value: released, showIcon: true },
      ];
    }

    // Default metrics for other roles
    let forReceiving = 0;
    let onHand = 0;
    let released = 0;
    let totalPads = 0;

    allFormsData.forEach((form: IFormTransmittal) => {
      const displayStatus = getDisplayStatus(form);
      const formPadCount = form.padAssignments?.length || 0;
      totalPads += formPadCount;

      form.padAssignments?.forEach((padAssignment) => {
      const padStatus = padAssignment.status;
      
      if (padStatus !== FormStatus.PENDING) {
          released++;
        }
      });
      
      if (displayStatus === FormStatus.FOR_RECEIVING || displayStatus === FormStatus.NOT_YET_RECEIVED) {
        forReceiving += formPadCount;
      } else if (displayStatus === FormStatus.ON_HAND) {
        onHand += formPadCount;
      } 
    });

    return [
      { title: "Total Pads", value: totalPads, variant: "primary" as const },
      { title: "For Receiving", value: forReceiving, showIcon: true },
      { title: "On-Hand", value: onHand, showIcon: true },
      { title: "Released", value: released, showIcon: true },
    ];
  }, [allFormsData, isDataLoading, customMetrics, userRole]);


  const getActionEvents = (Transmittal: IFormTransmittal): IActions<any>[] => {
    return getRoleBasedActions(Transmittal, userRole, getDisplayStatus);
  };

  const roleBasedColumns = useMemo(() => 
    getRoleBasedColumns(
      userRole,
      // getDisplayStatus,
      getActionEvents,
      divisions,
      formTypes,
      area
    ), 
    [userRole, getDisplayStatus, getActionEvents, divisions, formTypes, area]
  );

  // Use customColumns if provided, otherwise use role-based columns
  const finalColumns = customColumns || roleBasedColumns;

  const sortedTransmittal = useMemo(() => {
    if (!allFormsData || allFormsData.length === 0) return [];

    return allFormsData.slice().sort((a: IFormTransmittal, b: IFormTransmittal) => {
      const aStatus = getDisplayStatus(a);
      const bStatus = getDisplayStatus(b);

      // ON_HAND has highest priority (1)
      if (aStatus === FormStatus.ON_HAND && bStatus !== FormStatus.ON_HAND) return -1;
      if (bStatus === FormStatus.ON_HAND && aStatus !== FormStatus.ON_HAND) return 1;

      // FOR_RECEIVING has second priority (2)
      if (aStatus === FormStatus.FOR_RECEIVING && bStatus !== FormStatus.FOR_RECEIVING) return -1;
      if (bStatus === FormStatus.FOR_RECEIVING && aStatus !== FormStatus.FOR_RECEIVING) return 1;

      // All other statuses sorted by ID (newest first)
      return Number(b.id) - Number(a.id);
    });
  }, [allFormsData, userId, userRole]);

  const filteredData = useMemo(() => {
    let data = sortedTransmittal;

    // Apply filters
    if (filters.statusFilter) {
      data = data.filter((form) => getDisplayStatus(form) === filters.statusFilter);
    }

    if (filters.divisionFilter && filters.divisionFilter !== 0) {
      data = data.filter((form) => form.padAssignments?.some((assignment: { form?: { divisionId?: number } }) => assignment.form?.divisionId === filters.divisionFilter));
    }

    if (filters.type && filters.type !== "0") {
      const typeNumber = Number(filters.type);
      data = data.filter((form) => form.padAssignments?.some((assignment: { form?: { formTypeId?: number } }) => assignment.form?.formTypeId === typeNumber));
    }

    if (filters.dateFrom) {
      data = data.filter((form) => new Date(form.createdAt) >= new Date(filters.dateFrom ?? ""));
    }

    if (filters.dateTo) {
      data = data.filter((form) => new Date(form.createdAt) <= new Date(filters.dateTo ?? ""));
    }

    if (filters.filter) {
      const search = filters.filter.toLowerCase();
      data = data.filter((form) => form.transmittalNumber?.toLowerCase().includes(search) || form.padAssignments?.some((p: any) => p.form?.atpNumber?.toLowerCase().includes(search)));
    }

    return data;
  }, [sortedTransmittal, filters, userId, userRole]);

  useEffect(() => {
    fetchForms();
  }, []);

  return (
    <div className="p-4">
      <div className="text-xl font-semibold uppercase mt-4 tracking-wider text-[#042781]">{title}</div>
      <p className="pt-6 pb-4 font-poppins text-base text-[#01081C]">{description}</p>

      <MetricsGrid metrics={inventoryMetrics} isLoading={isDataLoading} />

      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <FilterPanel
            filters={filters}
            resetCounter={resetCounter}
            onSearch={handleSearch}
            onDateFromChange={handleDateFromChange}
            onDateToChange={handleDateToChange}
            onDivisionChange={handleDivisionChange}
            onTypeChange={handleTypeChange}
            onStatusChange={handleStatusChange}
            onClearAll={handleClearAll}
            divisionOptions={divisionOptions}
            typeOptions={typeOptions}
            statusOptions={statusOptions}
          />
        </div>
        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={finalColumns}
          data={filteredData || []}
          paginationServer={false}
          paginationTotalRows={filteredData?.length || 0}
          loading={isDataLoading}
          onChangeRowsPerPage={handleRowsChange}
          onPaginate={handlePaginate}
          searchable={false}
          multiSelect={false}
        />
      </div>
    </div>
  );
};

export default GenericInventoryTab;
