import { InventoryMetric } from "@components/template/Card";
import { useMemo } from "react";

export const useInventoryMetrics = () => {
  const returnedMetrics: InventoryMetric[] = useMemo(
    () => [
      {
        title: "Total Pad",
        value: 0,
        variant: "primary" as const,
        showIcon: undefined,
      },
      {
        title: "For Receiving",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "On-Hand",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "Returned",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
    ],
    []
  );

  const iocMetrics: InventoryMetric[] = useMemo(
    () => [
      {
        title: "Total Pad",
        value: 0,
        variant: "primary" as const,
        showIcon: undefined,
      },
      {
        title: "Incoming Received",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "Verified",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "Denied",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
    ],
    []
  );

  const headCashierMetrics: InventoryMetric[] = useMemo(
    () => [
      {
        title: "Total Pad",
        value: 0,
        variant: "primary" as const,
        showIcon: undefined,
      },
      {
        title: "Verify List",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "Verified",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "Rejected",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
      {
        title: "Released",
        value: 0,
        variant: undefined,
        showIcon: true,
      },
    ],
    []
  );

  return { returnedMetrics, iocMetrics, headCashierMetrics };
};
