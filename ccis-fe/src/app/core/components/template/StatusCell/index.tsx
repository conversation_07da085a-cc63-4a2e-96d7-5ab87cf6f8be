import React from "react";
import Typography from "@components/common/Typography";
import { getTextStatusColor } from "@helpers/text";

interface StatusCellProps {
  row: any;
  getDisplayStatus: (row: any) => string;
}

const StatusCell: React.FC<StatusCellProps> = ({ row, getDisplayStatus }) => {
  const displayStatus = getDisplayStatus(row);

  return (
    <Typography size="xs" className={getTextStatusColor(displayStatus)}>
      {displayStatus
        ?.replace(/_/g, " ")
        .toLowerCase()
        .replace(/\b\w/g, (char) => char.toUpperCase()) || "N/A"}
    </Typography>
  );
};

export default StatusCell;
