// src/modules/sales/helpers/ageRangeValidators.ts

export type AgeBand = { ageFrom: number | string; ageTo: number | string };

export const toNum = (v: number | string) => Number(v);

/** exact same (from,to) */
export const isDuplicateRange = <T extends AgeBand>(rows: T[], from: number, to: number) => rows.some((r) => toNum(r.ageFrom) === from && toNum(r.ageTo) === to);

/** same ageFrom appears anywhere */
export const isDuplicateAgeFrom = <T extends AgeBand>(rows: T[], from: number) => rows.some((r) => toNum(r.ageFrom) === from);

/** same ageTo appears anywhere */
export const isDuplicateAgeTo = <T extends AgeBand>(rows: T[], to: number) => rows.some((r) => toNum(r.ageTo) === to);

/** inclusive overlap: blocks if any part intersects an existing band */
export const findOverlap = <T extends AgeBand>(rows: T[], from: number, to: number) => rows.find((r) => from <= toNum(r.ageTo) && to >= toNum(r.ageFrom));

/**
 * Returns a human-readable error string if invalid; otherwise null.
 * Checks: numeric, order, same endpoints, dup (from/to/range), overlap.
 */
export const validateAgeBand = <T extends AgeBand>(rows: T[], from: number, to: number): string | null => {
  if (Number.isNaN(from) || Number.isNaN(to) || from > to) return "Please enter a valid age range.";
  if (from === to) return "Age From and Age To cannot be the same.";
  if (isDuplicateRange(rows, from, to)) return `Age range ${from}-${to} already exists.`;
  if (isDuplicateAgeFrom(rows, from)) return `Age From ${from} is already used in another range.`;
  if (isDuplicateAgeTo(rows, to)) return `Age To ${to} is already used in another range.`;
  const overlap = findOverlap(rows, from, to);
  if (overlap) return `Age range ${from}-${to} overlaps with existing ${overlap.ageFrom}-${overlap.ageTo}.`;
  return null;
};
