import { toast } from "react-toastify";

/**
 * @param key - The key under which the value is stored
 * @param value - The value to store (will be stringified)
 */
export const saveData = <T>(key: string, value: T): void => {
  try {
    const serialized = JSON.stringify(value);
    localStorage.setItem(key, serialized);
  } catch (err) {
    toast.error(`Error saving "${key}" to storage: ${err}`);
  }
};

/**
 * @param key - The key of the value to retrieve
 * @returns The parsed value, or null if not found or invalid
 */
export const getData = <T>(key: string): T | null => {
  try {
    const serialized = localStorage.getItem(key);
    return serialized ? (JSON.parse(serialized) as T) : null;
  } catch (err) {
    toast.error(`Error reading "${key}" to storage: ${err}`);
    return null;
  }
};

/**
 * @param key - The key to remove from localStorage
 */
export const clearData = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (err) {
    toast.error(`Error removing "${key}" to storage: ${err}`);
  }
};

/**
 * @param key - The key to check in localStorage
 * @returns True if the key exists, false otherwise
 */
export const hasKey = (key: string): boolean => {
  return localStorage.getItem(key) !== null;
};
