import { useEffect } from "react";
import { IDefaultParams } from "@interface/common.interface";

/*
  NOTE: The fetchFunction must accept either { params: IDefaultParams } or IDefaultParams in reducer.
*/
type FetchFunction = (args: any) => void;
type FetchFunctionOrArray = FetchFunction | FetchFunction[];

type ValidationFn = () => boolean;

export const useFetchWithParams = (
  fetchFns: FetchFunctionOrArray,
  params: IDefaultParams = { filter: "" },
  deps: React.DependencyList = [],
  withParams: boolean = true,
  validationFn: ValidationFn = () => true // Default: always run
) => {
  useEffect(() => {
    if (!validationFn()) return; // Only proceed if validation passes

    const payload = withParams ? { params } : params;
    // Check if it is an array of functions
    if (Array.isArray(fetchFns)) {
      fetchFns.forEach((fn) => fn(payload));
    } else {
      fetchFns(payload);
    }
  }, deps);
};
