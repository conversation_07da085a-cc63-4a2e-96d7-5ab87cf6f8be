import { useState, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form"; // Add this import
import { IDefaultParams } from "@interface/common.interface";
import { IFormTransmittal, IIncomingReceivedForm } from "@interface/form-inventory.interface"; // Add IIncomingReceivedForm
import { FormStatus, RoleType } from "@enums/form-status";
import { toast } from "react-toastify";
import { UserRoles } from "@interface/routes.interface";

interface UseRoleBasedInventoryDataProps {
  filters: IDefaultParams;
  userRole: RoleType;
  additionalFilters?: any;
}

export const useRoleBasedInventoryData = ({ filters, userRole, additionalFilters = {} }: UseRoleBasedInventoryDataProps) => {
  const [dataLoadingStates, setDataLoadingStates] = useState({
    primaryData: false,
    secondaryData: false,
    tertiaryData: false,
    quaternaryData: false, // Add new state for incoming forms
    allLoaded: false,
  });

  const { getTransmittalFormsTrail, getCurrentUserFormTransmittalTrail, getLatestTransmittalForm } = useTransmittalFormActions();
  const { getIncomingReceivedForms } = useIncomingReceivedFormActions(); // Add this action
  // const { getTransmittalForms } = useTransmittalFormActions();

  // Get data from different sources based on user role
  const { data: FORMS } = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms);

  const { data: USER_TRANSMITTALS } = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail);

  const RECEIVED_FORMS = useSelector((state: RootState) => state.formInventoryTransmittal.transmittalForms) as IFormTransmittal[];
  // IC/OG & CHIEF CASHIER
  const { data: INCOMING_FORMS } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getIncomingReceivedForms);

  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getTransmittalForms.loading);

  // Add loading state for incoming forms
  const incomingLoading = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getIncomingReceivedForms.loading);

  const isDataLoading = loading || incomingLoading || !dataLoadingStates.allLoaded;

  useEffect(() => {
    // Update loading states based on user role and available data
    let primaryLoaded = false;
    let secondaryLoaded = false;
    let tertiaryLoaded = false;
    let quaternaryLoaded = false;

    switch (userRole) {
      case RoleType.CLIFSA:
        primaryLoaded = FORMS !== undefined;
        secondaryLoaded = USER_TRANSMITTALS !== undefined;
        tertiaryLoaded = RECEIVED_FORMS !== undefined;
        quaternaryLoaded = true;
        break;
      case RoleType.ADMINOUTGOING:
        primaryLoaded = FORMS !== undefined;
        secondaryLoaded = USER_TRANSMITTALS !== undefined;
        tertiaryLoaded = RECEIVED_FORMS !== undefined;
        quaternaryLoaded = true;
        break;
      case RoleType.GAM:
        primaryLoaded = USER_TRANSMITTALS !== undefined;
        secondaryLoaded = true;
        tertiaryLoaded = RECEIVED_FORMS !== undefined;
        quaternaryLoaded = true;
        break;
      case RoleType.IOC:
        primaryLoaded = true;
        secondaryLoaded = true;
        tertiaryLoaded = true;
        quaternaryLoaded = INCOMING_FORMS !== undefined;
        break;
      case RoleType.CHIEFCASHIER:
        primaryLoaded = true;
        secondaryLoaded = true;
        tertiaryLoaded = true;
        quaternaryLoaded = INCOMING_FORMS !== undefined;
        break;
      default:
        primaryLoaded = FORMS !== undefined;
        secondaryLoaded = USER_TRANSMITTALS !== undefined;
        tertiaryLoaded = RECEIVED_FORMS !== undefined;
        quaternaryLoaded = INCOMING_FORMS !== undefined;
    }

    setDataLoadingStates({
      primaryData: primaryLoaded,
      secondaryData: secondaryLoaded,
      tertiaryData: tertiaryLoaded,
      quaternaryData: quaternaryLoaded,
      allLoaded: primaryLoaded && secondaryLoaded && tertiaryLoaded && quaternaryLoaded,
    });
  }, [FORMS, USER_TRANSMITTALS, RECEIVED_FORMS, INCOMING_FORMS, userRole]);

  // Combine data based on user role
  const allFormsData = useMemo(() => {
    if (!dataLoadingStates.allLoaded) return [];

    let dataSources: (IFormTransmittal | IIncomingReceivedForm)[] = [];

    switch (userRole) {
      case RoleType.CLIFSA:
        dataSources = [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RECEIVED_FORMS || [])];
        break;
      case RoleType.ADMINOUTGOING:
        dataSources = [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RECEIVED_FORMS || [])];
        break;
      case RoleType.GAM:
        dataSources = [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RECEIVED_FORMS || [])];
        break;
      case RoleType.IOC:
        dataSources = [
          // ...(FORMS?.data || []),
          // ...(USER_TRANSMITTALS?.data || []),
          // ...(RECEIVED_FORMS || []),
          ...(INCOMING_FORMS?.data || []),
        ];
        break;
      case RoleType.CHIEFCASHIER:
        dataSources = [...(INCOMING_FORMS?.data || [])];
        break;
      default:
        dataSources = [...(FORMS?.data || []), ...(USER_TRANSMITTALS?.data || []), ...(RECEIVED_FORMS || [])];
    }

    // Remove duplicates - you might need a more sophisticated approach if combining different types
    const formsMap = new Map();
    dataSources.forEach((form) => {
      formsMap.set(form.id, form);
    });

    return Array.from(formsMap.values());
  }, [FORMS, USER_TRANSMITTALS, RECEIVED_FORMS, INCOMING_FORMS, dataLoadingStates.allLoaded, userRole]);

  const fetchForms = async () => {
    try {
      setDataLoadingStates((prev) => ({ ...prev, allLoaded: false }));

      // Base payload
      const basePayload = {
        filter: filters.filter,
        divisionFilter: filters.divisionFilter,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
        page: filters.page,
        pageSize: filters.pageSize,
        ...additionalFilters,
      } as IDefaultParams;

      const promises = [];

      // Role-specific API calls
      switch (userRole) {
        case RoleType.IOC:
          promises.push(
            getIncomingReceivedForms({
              ...basePayload,
              page: 1,
              pageSize: 100,
              type: filters.type,
            })
          );
          break;

        case RoleType.CHIEFCASHIER:
          promises.push(
            getIncomingReceivedForms({
              ...basePayload,
              page: 1,
              pageSize: 100,
              type: filters.type,
            })
          );
          break;

        case RoleType.CLIFSA:
          promises.push(
            getTransmittalFormsTrail({
              ...basePayload,
              type: RoleType.CLIFSA,
            }),
            getTransmittalFormsTrail({
              ...basePayload,
              page: 1,
              pageSize: 1000,
              type: RoleType.ADMINOUTGOING,
              statusFilter: FormStatus.RELEASED,
            }),
            getCurrentUserFormTransmittalTrail({
              filter: filters.filter,
              dateFrom: filters.dateFrom,
              dateTo: filters.dateTo,
            }),
            getLatestTransmittalForm({
              ...basePayload,
              type: RoleType.ADMINOUTGOING,
              statusFilter: FormStatus.RECEIVED,
              page: 1,
              pageSize: 1000,
            })
          );
          break;

        case RoleType.ADMINOUTGOING:
          promises.push(
            getTransmittalFormsTrail({
              ...basePayload,
              type: RoleType.ADMINOUTGOING,
            }),
            getTransmittalFormsTrail({
              ...basePayload,
              page: 1,
              pageSize: 1000,
              type: UserRoles.ioc,
              statusFilter: FormStatus.RELEASED,
            }),
            getCurrentUserFormTransmittalTrail({
              filter: filters.filter,
              dateFrom: filters.dateFrom,
              dateTo: filters.dateTo,
            }),
            getLatestTransmittalForm({
              ...basePayload,
              type: UserRoles.ioc,
              statusFilter: FormStatus.RECEIVED,
              page: 1,
              pageSize: 1000,
            })
          );
          break;

        case RoleType.GAM:
          promises.push(
            getTransmittalFormsTrail({
              page: 1,
              pageSize: 1000,
              type: RoleType.CLIFSA,
              statusFilter: FormStatus.RELEASED,
            }),
            getCurrentUserFormTransmittalTrail({
              filter: filters.filter,
              dateFrom: filters.dateFrom,
              dateTo: filters.dateTo,
            }),
            getLatestTransmittalForm({
              ...basePayload,
              statusFilter: FormStatus.RECEIVED,
              page: 1,
              pageSize: 1000,
            })
          );
          break;

        default:
          promises.push(
            getTransmittalFormsTrail(basePayload),
            getLatestTransmittalForm({
              ...basePayload,
              statusFilter: FormStatus.RECEIVED,
              page: 1,
              pageSize: 1000,
            })
          );
      }

      await Promise.allSettled(promises);
    } catch (error) {
      toast.error(`Error fetching forms: ${String(error)}`);
    }
  };

  return {
    allFormsData,
    isDataLoading,
    dataLoadingStates,
    fetchForms,
    userId,
  };
};
