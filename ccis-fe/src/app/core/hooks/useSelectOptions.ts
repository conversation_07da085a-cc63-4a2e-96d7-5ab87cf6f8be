import { useMemo } from "react";

type Option = { value: any; text: string };

// Utility to access nested properties like "user.firstname"
const getNestedValue = (obj: any, path: string, fallback: string | number = "N/A"): any => {
  const value = path.split(".").reduce((acc, key) => acc?.[key], obj);
  return value !== undefined && value !== null && value !== "" ? value : fallback;
};

type UseSelectOptionsProps<T> = {
  data: T[] | undefined;
  firstOptionText?: string;
  valueKey?: keyof T | string | string[]; // support nested value
  textKey?: keyof T | Array<keyof T>; // Accepts single or multiple keys
  textPath?: string | string[]; // support "user.firstname" or ["user", "firstname"]
  formatText?: (item: T) => string; // custom formatter
  separator?: string; // Text separator for display
  fallbackText?: string;
};

export const useSelectOptions = <T extends Record<string, any>>({
  data,
  firstOptionText = "Select",
  valueKey = "id" as keyof T,
  textKey,
  textPath,
  formatText,
  separator = " ",
  fallbackText = "N/A",
}: UseSelectOptionsProps<T>): Option[] => {
  const options = useMemo(() => {
    if (!data || data.length === 0) {
      return [{ value: 0, text: firstOptionText }];
    }

    return [
      { value: 0, text: firstOptionText },
      ...data.map((item) => {
        // Handle nested valueKey
        const valuePath = Array.isArray(valueKey) ? valueKey.join(".") : String(valueKey);
        const value = getNestedValue(item, valuePath, 0); // fallback to 0 for invalid value

        // Handle text formatting
        let text: string;

        if (formatText) {
          text = formatText(item);
        } else if (textPath) {
          const path = Array.isArray(textPath) ? textPath.join(".") : textPath;
          text = String(getNestedValue(item, path, fallbackText));
        } else if (Array.isArray(textKey)) {
          const combined = textKey.map((key) => item[key]).join(separator);
          text = combined.trim() || fallbackText;
        } else if (textKey) {
          text = String(item[textKey] ?? fallbackText);
        } else {
          text = fallbackText;
        }

        return { value, text };
      }),
    ];
  }, [data, firstOptionText, valueKey, textKey, textPath, formatText, separator, fallbackText]);

  return options;
};
