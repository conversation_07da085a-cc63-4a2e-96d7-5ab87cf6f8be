import { FormStatus } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { IIncomingReceivedForm } from "@interface/form-inventory.interface";

export const getIocLogic = () => {
  const getIocStatus = (row: IIncomingReceivedForm) => {
    if (row) {
      const status = row.status || "No Status";
      if (status === FormStatus.PENDING) return FormStatus.INCOMING_RECEIVED;
      if (status === FormStatus.VERIFIED) return FormStatus.VERIFIED;
      if (status === FormStatus.REJECTED) return FormStatus.DENIED;
      return status;
    }else {
      // If no trail and no latest trail, return NOT_RECEIVED
        return FormStatus.FOR_RECEIVING; 
    }
  };

  return { getIocStatus };
};

export const getHeadCashierLogic = () => {
  const getHeadCashierStatus = (row: IIncomingReceivedForm) => {
    if (row) {
      const status = row.status || "No Status";
      if (status === FormStatus.PENDING) return FormStatus.VERIFY_LIST;
      if (status === FormStatus.VERIFIED) return FormStatus.VERIFIED;
      if (status === FormStatus.REJECTED) return FormStatus.REJECTED;
      if (status === FormStatus.RELEASED) return FormStatus.RELEASED;
      return status;
    }else {
      // If no trail and no latest trail, return NOT_RECEIVED
        return FormStatus.FOR_RECEIVING; 
    }
  };

  return { getHeadCashierStatus };
};

export const getTransmittalInventoryLogic = () => {
  const getTransmittalInventoryStatus = (row: IFormTransmittal, userId: number) => {
    const trail = row?.formTransmittalTrails?.find(
      (item: any) => item.createdBy?.id === userId
    );
    
    if (trail) {
      const status = trail.status || "No Status";
      if (status === FormStatus.APPROVED) return FormStatus.FOR_RECEIVING;
      if (status === FormStatus.RELEASED) return FormStatus.RELEASED;
      if (status === FormStatus.RECEIVED) return FormStatus.RELEASED;
      return status;
    
    } else if (!trail) {
        return FormStatus.ON_HAND;
    
    }else {
      // If no trail and no latest trail, return NOT_RECEIVED
        return FormStatus.FOR_RECEIVING; 
    }
  };

  return { getTransmittalInventoryStatus };
};


export const getAdminOutgoingLogic = () => {
  const getAdminOutgoingStatus = (row: IFormTransmittal, userId: number) => {
    const trail = row?.formTransmittalTrails?.find(
      (item: any) => item.createdBy?.id === userId
    );
    
    const hasLatestTrail = row?.latestFormTransmittalTrail !== null && row?.latestFormTransmittalTrail !== undefined;

    if (trail) {
      const status = trail.status || "No Status";
      if (status === FormStatus.APPROVED) return FormStatus.FOR_RECEIVING;
      if (status === FormStatus.RELEASED) return FormStatus.RELEASED;
      if (status === FormStatus.RECEIVED) return FormStatus.RELEASED;
      return status;
    
    } else if (hasLatestTrail) {
        return FormStatus.ON_HAND;
    
    }else {
      // If no trail and no latest trail, return NOT_RECEIVED
        return FormStatus.FOR_RECEIVING; 
    }
  };

  return { getAdminOutgoingStatus };
};

export const getReturnedInventoryStatus = () => {
  const getReturnedInventoryStatus = (row: IFormTransmittal) => {
    return row.status || "Unknown";
  };

  return { getReturnedInventoryStatus };
};

export const useGamStatusLogic = () => {
  const getGamDisplayStatus = (row: IFormTransmittal) => {
    return row.status === FormStatus.RECEIVED ? FormStatus.ON_HAND : row.status || "Unknown";
  };

  return { getGamDisplayStatus };
};