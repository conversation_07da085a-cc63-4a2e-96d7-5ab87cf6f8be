// Core type definitions
type TSelectedCategory = "Sales Invoice" | "Official Receipt" | "Provisional Receipt";
type TSelectedPeriod = "Weekly" | "Monthly" | "Quarterly" | "Annually";
type TChartType = "area" | "line";

// User interface (from Redux state)
export interface IUser {
  firstname: string;
  // Add other user properties as needed
}

// Chart data structures
export interface IPeriodData {
  released: number[];
  returned: number[];
}

export interface ICategoryPeriods {
  Weekly: IPeriodData;
  Monthly: IPeriodData;
  Quarterly: IPeriodData;
  Annually: IPeriodData;
}

export interface IAreaChartData {
  "Sales Invoice": ICategoryPeriods;
  "Official Receipt": ICategoryPeriods;
  "Provisional Receipt": ICategoryPeriods;
}

export interface IChartSeries {
  name: string;
  data: number[];
}

export interface IColumnChartData {
  categories: string[];
  series: IChartSeries[];
  colors: string[];
}

export interface IDonutChartData {
  series: number[];
  labels: string[];
  colors: string[];
}

export interface IXAxisLabels {
  Weekly: string[];
  Monthly: string[];
  Quarterly: string[];
  Annually: string[];
}

export interface IXAxis {
  categories: string[] | undefined;
  type: "category";
}

// Table data structures
export interface ITableRow {
  id: number;
  createdAt: string;
  type: string;
  status: string;
}

// Status and type definitions from data.json
export interface IStatus {
  value: string;
  text: string;
}

export interface IStatusSameVal {
  value: string;
  text: string;
}

export interface IPosition {
  value: string;
  text: string;
}

export interface IGender {
  value: string;
  text: string;
}

export interface ISuffix {
  value: string;
  text: string;
}

export interface ICoopMembershipType {
  requirementTemplateName: string;
}

export interface IPaymentTypes {
  value: string;
  text: string;
}

export interface ICommissionStructure {
  id: number;
  shareType: string;
  percentage: string;
}

export interface IRangeTypes {
  value: string;
  text: string;
}

export interface IType {
  incoming: string;
  outgoing: string;
}

export interface IStatusTable {
  approved: string;
  pending: string;
  rejected: string;
}

export interface IPremiumBasis {
  [key: string]: string;
}

export interface IRelationship {
  [key: string]: string;
}

export interface IAgeType {
  [key: string]: string;
}

// Main data structure from data.json
export interface IClifsaAdminData {
  status: IStatus[];
  statusSameVal: IStatusSameVal[];
  position: IPosition[];
  gender: IGender[];
  suffix: ISuffix[];
  coopMembershipType: ICoopMembershipType[];
  paymentTypes: IPaymentTypes[];
  commissionStructure: ICommissionStructure[];
  rangeTypes: IRangeTypes[];
  "Sales Invoice": ICategoryPeriods;
  "Official Receipt": ICategoryPeriods;
  "Provisional Receipt": ICategoryPeriods;
  xAxisLabels: IXAxisLabels;
  chartData: {
    dataX: number[];
    dataY: number[];
  };
  chartDonutData: IDonutChartData;
  type: IType;
  statusTable: IStatusTable;
  premiumBasis: IPremiumBasis;
  relationship: IRelationship;
  ageType: IAgeType;
}

// Component props interfaces (if needed for child components)
export interface IAreaChartProps {
  chartType: TChartType;
  chartHeight: number | string;
  enableDataLabels: boolean;
  selectedCategory: TSelectedCategory;
  setSelectedCategory: (category: TSelectedCategory) => void;
  selectedPeriod: TSelectedPeriod;
  setSelectedPeriod: (period: TSelectedPeriod) => void;
  chartSeries: IChartSeries[];
  xaxis: IXAxis;
}

export interface IDonutChartProps {
  series: number[];
  labels: string[];
  colors: string[];
  total: number;
}

export interface IColumnChartProps {
  categories: string[];
  series: IChartSeries[];
  colors: string[];
}

export interface IClifsaAdminDashboardParams {
  interval?: number;
}