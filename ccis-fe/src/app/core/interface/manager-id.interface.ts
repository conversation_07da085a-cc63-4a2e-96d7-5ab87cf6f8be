import { LoadingResult } from "./common.interface";

//Main Payload for Manager
export interface IManagerIdInterface {
  id: number;
  key?: string;
  value?: string;
  createdAt: string | null;
  updatedAt: string | null;
  deletedAt: string | null;
}

export interface IManagerId {
  id?: number;
  key?: string;
  value?: string;
  createdAt: string | null;
  updatedAt: string | null;
  deletedAt: string | null;
}

export interface IManagerIdsResponse {
  data: IManagerId[];
}

export interface TManagerIdsState {
  managerIds: number[];
  getManagerIds: LoadingResult & {
    data?: IManagerIdsResponse;
  };
}
