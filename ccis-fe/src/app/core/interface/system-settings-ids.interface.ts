import { LoadingResult } from "./common.interface";

export interface ISystemSettingsIdsResponse {
  data: TSystemSettingId[];
}

export interface TSystemSettingId {
  id: number;
  key: string;
  value: string;
  createdAt: string | null;
  updatedAt: string | null;
  deletedAt: string | null;
}

export interface TSystemSettingsIdsState {
  getSystemSettingsIds: LoadingResult & {
    data?: ISystemSettingsIdsResponse;
  };
}
