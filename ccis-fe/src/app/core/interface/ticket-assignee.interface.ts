import { IUser } from "./user.interface";



export interface IPosition {
  id: number;
  positionName: string;
  positionCode: string;
  description: string;
  createdBy: number;
  updatedBy: number;
  deletedBy: number | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IDepartment {
  id: number;
  departmentName: string;
  departmentCode: string;
  description: string;
  createdBy: number;
  updatedBy: number;
  deletedBy: number | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IRequestType {
  id: number;
  name: string;
  description: string;
  departmentId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IAttachment {
  id: number;
  label: string;
  description: string;
  filename: string;
  filepath: string;
  mimeType: string;
  tag: string | null;
  size: number;
  createdAt: string;
  updatedAt: string;
}

export interface ITicketAssignee {
  id: number;
  ticketId: number;
  userId: number;
  role: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ITicketComment {
  id: number;
  ticketId: number;
  userId: number;
  comment: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user: IUser;
}

export interface ITicketApiResponse {
  data: ITicketDetails[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    links: {
      url: string | null;
      label: string;
      active: boolean;
    }[];
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface ITicketDetails {
  id?: number;
  ticketNumber?: string | null;
  subject?: string;
  description?: string;
  assignedToId?: number;
  fromDepartmentId?: number;
  toDepartmentId?: number;
  requestTypeId?: number;
  issueType?: string | null;
  coopId?: string | null;
  device?: string | null;
  operatingSystem?: string | null;
  applicationName?: string | null;
  isUrgent?: boolean | null;
  priorityLevel?: string;
  closureStatus?: string;
  status?: string;
  startDate?: string | null;
  completionDate?: string | null;
  expectedCompletionDate?: string;
  extensionDate?: string | null;
  extensionRemarks?: string | null;
  formType?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
  createdBy?: IUser;
  assignedTo?: IUser;
  fromDepartment?: IDepartment;
  toDepartment?: IDepartment;
  requestType?: IRequestType;
  attachments?: IAttachment[];
  ticketAssignees?: ITicketAssignee[];
  ticketComments?: ITicketComment[];
}

export interface ITicketDetailsApiResponse {
  data: ITicketDetails[];
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    links: {
      url: string | null;
      label: string;
      active: boolean;
    }[];
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface ITicketExtendDateResponse {
  status: string;
  message: string | null;
  data: string;
}


