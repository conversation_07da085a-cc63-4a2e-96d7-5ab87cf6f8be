import DashboardLayout from "@layouts/DashboardLayout";
import { vicePresidentSalesRoutes } from "@services/routes/vice-president-sales";
import { vicePresidentOperationRoutes } from "@services/routes/vice-president-operations";
export const VicePresidentSalesLayout = ({ children }: { children: React.ReactNode }) => <DashboardLayout routes={vicePresidentSalesRoutes}>{children}</DashboardLayout>;
export const VicePresidentOperationLayout = ({ children }: { children: React.ReactNode }) => <DashboardLayout routes={vicePresidentOperationRoutes}>{children}</DashboardLayout>;
