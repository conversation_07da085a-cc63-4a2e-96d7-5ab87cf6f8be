import httpClient from "@clients/httpClient";
import { AttachableTypes } from "@enums/attachable-types";
import { IAttachment } from "@interface/products.interface";

export const uploadAttachments = async (attachableType: AttachableTypes, attachableId: string, attachments: IAttachment[]) => {
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  };

  const formData = new FormData();
  formData.append("attachableId", attachableId);
  formData.append("attachableType", attachableType);
  attachments.forEach((attachment, index) => {
    if (attachment?.file) {
      formData.append(`attachments[${index}][file]`, attachment.file);
    }
    formData.append(`attachments[${index}][description]`, attachment.description ?? "");
    formData.append(`attachments[${index}][tag]`, attachment.tag ?? "");
    formData.append(`attachments[${index}][label]`, attachment.label ?? "");
  });

  return httpClient.post(`shared/attachments`, formData, config);
};
