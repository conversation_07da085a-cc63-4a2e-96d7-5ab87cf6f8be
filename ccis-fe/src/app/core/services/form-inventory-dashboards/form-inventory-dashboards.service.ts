import httpClient from "@clients/httpClient";
import { AxiosRequestConfig } from "axios";

const dashboardApiResource = "dashboard";

export const getAdminDashboardMetricsService = async (
  metricType: string,
  payload: { interval?: number } = {}
) => {
  const config: AxiosRequestConfig = {
    params: payload, // Pass payload as query parameters
  };
  return httpClient.get(`${dashboardApiResource}/${metricType}`, config);
};

export const getSpecificRoleDashboardMetricsService = async (
  metricType: string,
  payload: { interval?: number } = {}
) => {
  const config: AxiosRequestConfig = {
    params: payload, // Pass payload as query parameters
  };
  return httpClient.get(`${dashboardApiResource}/my/${metricType}`, config);
};