import httpClient from "@clients/httpClient";
import { IPadRequestPayload } from "@state/types/form-inventory-request-pads";

const apiResouce = "pad-request";

export const getPadRequestByIdService = async (id: number) => {
    return httpClient.get(
        `${apiResouce}/${id}?relations=padAssignments.form|padAssignments.formType|padAssignments.form.division|padAssignments.form.marketArea`,
    );
}
export const getPadRequestsService = async (params: any) => {
    return httpClient.get(
        `${apiResouce}?relations=padAssignments.form|padAssignments.formType|padAssignments.form.division|padAssignments.form.marketArea&sort=id,desc&pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}`,
    );
};
export const postPadRequestService = async (payload: IPadRequestPayload ) => {
    return httpClient.post(`${apiResouce}`, payload);
};