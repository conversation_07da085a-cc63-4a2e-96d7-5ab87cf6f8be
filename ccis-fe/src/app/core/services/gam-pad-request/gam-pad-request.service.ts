import httpClient from "@clients/httpClient";
import { IDefaultParams } from "@interface/common.interface";
import { ICreatePadRequest, IUpdatePadRequest } from "@interface/gam-request-pads";

const apiResource = "pad-request";

// Function to fetch Pad Request
export const getGamPadRequestService = async (params: IDefaultParams) => {
  const relations = "relations=releasedTo|createdBy|padAssignments";

  // Perform a GET request to fetch Pad Request with filters
  let query = `${apiResource}?pageSize=${params.pageSize ?? 10}&page=${params.page ?? 1}&${relations}&sort=createdAt,desc`;

  //Filter by division
  if (params?.divisionFilter) {
    query += `&divisionId[eq]=${params.divisionFilter}`;
  }

  //Filter by form type
  if (params?.formtypeFilter) {
    query += `&formTypeId[eq]=${params.formtypeFilter}`;
  }

  //Filter by area
  if (params?.areaFilter) {
    query += `&areaId[eq]=${params.areaFilter}`;
  }

  // Filter by Date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  // Status
  if (params?.statusFilter) {
    query += `&status[in]=${params?.statusFilter}`;
  }

  // For chief cashier filter status
  if (params?.parentFilter) {
    query += `&padAssignments.status[in]=${params?.parentFilter}`;
  }

  // Filter by user
  if (params?.user) {
    query += `&createdBy.id[eq]=${params?.user}`;
  }

  return httpClient.get(`${query}&${relations}&sort=createdAt,desc`);
};

export const getGamPadRequestDetailsService = async (id: number) => {
  const relations = "relations=createdBy|formType|division|area|releasedTo|padAssignments";

  // Perform a GET request to fetch Pad request details
  let query = `${apiResource}/${id}`;

  return httpClient.get(`${query}?${relations}`);
};

export const getRemainingPadsService = async (id: number) => {
  // Perform a GET request to fetch Remaining Pads
  let query = `${apiResource}/user-remaining-pads/${id}`;

  return httpClient.get(`${query}`);
};

export const getLastSeriesNumberService = async (
  params: {
    filter?: string;
    filterStatus?: string;
    page?: number;
    pageSize?: number;
    rows?: number;
    sort?: string;
    divisionFilter?: number;
    areaFilter?: number;
    formtypeFilter?: number;
  } = {}
) => {
  const api = "pad-series";
  const relations = "cooperative|product|userIssuedBy|padAssignment";

  // Perform a GET request to fetch last pad series
  let query = `${api}?pageSize=${params.pageSize ?? 10}&page=${params.page ?? 1}`;

  return httpClient.get(`${query}&${relations}&sort=id,asc`);
};

export const getNextSeriesService = async (params: IDefaultParams) => {
  const payload = {
    formTypeId: params.formtypeFilter,
    divisionId: params.divisionFilter,
  };

  return httpClient.post(`${apiResource}/next-series`, payload);
};

export const postGamPadRequestService = async (payload: ICreatePadRequest) => {
  return httpClient.post(`${apiResource}`, payload);
};

export const putGamPadRequestService = async (payload: IUpdatePadRequest) => {
  const { id, ...updatePayload } = payload;
  return httpClient.put(`${apiResource}/${id}`, updatePayload);
};
