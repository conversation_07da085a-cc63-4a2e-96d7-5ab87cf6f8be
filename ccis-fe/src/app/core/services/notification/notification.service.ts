import httpClient from "@clients/httpClient";

const apiResource = "shared/notifications";
const relations = "notifiable|createdBy";

export const getNotificationsService = async () => {
  return httpClient.get(`${apiResource}/me?relations=${relations}&sort=id,desc`);
};

export const patchMarkAllAsReadNotificationService = async () => {
  return httpClient.patch(`${apiResource}/mark/read/all`);
};

export const patchReadNotificationService = async (id?: number) => {
  return httpClient.patch(`${apiResource}/${id}/read`);
};
