import * as Yup from "yup";

export const cancelReceiptSchema = Yup.object().shape({
  paymentDetail: Yup.object().shape({
    attachments: Yup.array()
      .of(
        Yup.object().shape({
          file: Yup.mixed()
            .required("Attachment is required")
            .test("fileType", "Unsupported file format. Only PDF, JPG, PNG allowed.", (value) => {
              if (!value) return false;
              if (value instanceof File) {
                const allowedTypes = ["application/pdf", "image/jpeg", "image/png"];
                return allowedTypes.includes(value.type);
              }
              return false;
            })
            .test("fileSize", "File size too large. Max allowed size is 15MB.", (value) => {
              if (!value) return false;
              if (value instanceof File) {
                const maxSize = 15 * 1024 * 1024; // 15MB in bytes
                return value.size <= maxSize;
              }
              return false;
            }),
        })
      )
      .min(1, "You must upload at least one attachment")
      .required("Attachments are required"),
  }),
});

export const issuePRFormSchema = Yup.object().shape({
  issuedBy: Yup.string().required("Issued by is required"),
  remitTo: Yup.string().required("Remit To is required"),
  releasedAt: Yup.string().required("Released at is required"),
  division: Yup.string().required("Division is required"),
  type: Yup.string().required("Type is required"), // fixed repeated text
  transmittalNumber: Yup.string().required("Transmittal Number is required"),
  atpNumber: Yup.string().required("ATP Number is required"),
  cooperative: Yup.string().required("Cooperative is required"),
  receivedDate: Yup.string().required("Received Date is required"),
  product: Yup.string().required("Product is required"),
  formTypeId: Yup.string().required("Form Type is required"),
 
});
