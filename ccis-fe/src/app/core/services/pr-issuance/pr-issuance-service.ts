import httpClient from "@clients/httpClient";
import { IPRIssuancePayload, IPRIssuanceWithIndexPayload } from "@state/types/form-inventory-pr-issuance";
// FORM INVENTORY  DIVISIONS

const prIssuanceApiResource = "pad-series";

export const getPRIssuanceService = async (id?: number) => {
  return httpClient.get(
    `${prIssuanceApiResource}/${id}?relations=cooperative|product|userRemitTo|userIssuedBy|paymentDetail.attachments|padAssignment.form.division|padAssignment.form.area|padAssignment.formTransmittal`
  );
};

export const getPRIssuanceDetailsService = async (id?: number) => {
  const relations = "relations=cooperative|product|userRemitTo|userIssuedBy|paymentDetail.attachments|padAssignment.form.division|padAssignment.form.area|padAssignment.formTransmittal";

  return httpClient.get(`${prIssuanceApiResource}/${id}?${relations}`);
};

export const getCoopDetails = async (filter = "") => {
  return httpClient.get(`/cooperatives?coopName[like]=${filter}&relations=cooperativeType|cooperativeAffiliations.affiliation|cooperativeOfficers.position|cooperativeCategory`);
};

export const getPRIssuancesService = async (filter: string) => {
  return httpClient.get(`${prIssuanceApiResource}?prIssuanceName[like]=${filter}`);
};

export const postPRIssuanceService = async (payload: IPRIssuancePayload) => {
  return httpClient.post(prIssuanceApiResource, payload);
};

export const putPRIssuanceService = async (payload: IPRIssuancePayload) => {
  return httpClient.put(`${prIssuanceApiResource}/${payload.id}`, payload);
};

export const destroyPRIssuanceService = async (payload: IPRIssuanceWithIndexPayload) => {
  return httpClient.delete(`${prIssuanceApiResource}/${payload.id}`);
};

export const patchPRIssuanceStatus = async (id: number, status: string) => {
  return httpClient.patch(`${prIssuanceApiResource}/${id}`, { status });
};
