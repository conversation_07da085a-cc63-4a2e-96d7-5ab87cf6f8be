import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AREASALESMANAGER.dashboard.key,
  path: ROUTES.AREASALESMANAGER.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.AREASALESMANAGER.commissionAndRequirements.key,
  path: ROUTES.AREASALESMANAGER.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.AREASALESMANAGER.myApprovals.key,
  path: ROUTES.AREASALESMANAGER.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AREASALESMANAGER.notification.key,
  path: ROUTES.AREASALESMANAGER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const areaSalesManagerRoutes = [overview, myApprovals, commissionAndRequirements, notification];
