import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import ClaimsDashboard from "@modules/dashboard/ClaimsDashboard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CLAIMSMANAGER.dashboard.key,
  path: ROUTES.CLAIMSMANAGER.dashboard.key,
  component: ClaimsDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CLAIMSMANAGER.requestDashboard.key,
  path: ROUTES.CLAIMSMANAGER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claimsManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CLAIMSMANAGER.requestForm.key,
  path: ROUTES.CLAIMSMANAGER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsManager],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CLAIMSMANAGER.viewRequestForm.key,
  path: ROUTES.CLAIMSMANAGER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsManager],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CLAIMSMANAGER.notification.key,
  path: ROUTES.CLAIMSMANAGER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.claimsManager],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const claimsManagerRoutes = [overview, requestDashboard, requestForm, viewRequest, notification];
