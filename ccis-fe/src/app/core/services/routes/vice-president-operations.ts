import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VICEPRESIDENTFOROPERATION.dashboard.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.VICEPRESIDENTFOROPERATION.commissionAndRequirements.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.VICEPRESIDENTFOROPERATION.myApprovals.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VICEPRESIDENTFOROPERATION.notification.key,
  path: ROUTES.VICEPRESIDENTFOROPERATION.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentOperations],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const vicePresidentOperationRoutes = [overview, myApprovals, commissionAndRequirements, notification];
