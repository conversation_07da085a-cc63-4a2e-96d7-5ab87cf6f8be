import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.VICEPRESIDENTFORSALES.dashboard.key,
  path: ROUTES.VICEPRESIDENTFORSALES.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.VICEPRESIDENTFORSALES.commissionAndRequirements.key,
  path: ROUTES.VICEPRESIDENTFORSALES.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.VICEPRESIDENTFORSALES.myApprovals.key,
  path: ROUTES.VICEPRESIDENTFORSALES.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.VICEPRESIDENTFORSALES.notification.key,
  path: ROUTES.VICEPRESIDENTFORSALES.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.vicePresidentSales],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const vicePresidentSalesRoutes = [overview, myApprovals, commissionAndRequirements, notification];
