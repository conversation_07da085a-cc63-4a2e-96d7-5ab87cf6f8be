import { createDynamicState } from "@helpers/array";
import { IClifsaAdminDashboardParams } from "@interface/form-inventory-dashboard";
import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TFormInventoryDashboards } from "@state/types/clifsa-admin";
import { useDispatch } from "react-redux";

// Interface for dashboard-specific parameters


const initialState: TFormInventoryDashboards = {
    getAdminClifsaAdmin: createDynamicState(),
    getSpecificClifsaDashboard: createDynamicState(),
};

const formInventoryDashboards = createSlice({
    name: "formInventoryDashboards",
    initialState,
    reducers: {
        getAdminClifsaAdmin(state, _action: PayloadAction<{params: IClifsaAdminDashboardParams}>) {
            state.getAdminClifsaAdmin = createDynamicState(["loading"]);
        },
        getAdminClifsaAdminSuccess(state, action) {
            state.getAdminClifsaAdmin = createDynamicState(["success"], action.payload);
        },
        getAdminClifsaAdminFailure(state) {
            state.getAdminClifsaAdmin = createDynamicState(["error"]);
        },
        getSpecificClifsaDashboard(state, _action: PayloadAction<{metricType: string, params: IClifsaAdminDashboardParams}>) {
            state.getSpecificClifsaDashboard = createDynamicState(["loading"]);
        },
        getSpecificClifsaDashboardSuccess(state, action) {
            state.getSpecificClifsaDashboard = createDynamicState(["success"], action.payload);
        },
        getSpecificClifsaDashboardFailure(state) {
            state.getSpecificClifsaDashboard = createDynamicState(["error"]);
        },
    },
});

export const {
    getAdminClifsaAdmin,
    getAdminClifsaAdminSuccess,
    getAdminClifsaAdminFailure,
    getSpecificClifsaDashboard,
    getSpecificClifsaDashboardSuccess,
    getSpecificClifsaDashboardFailure
} = formInventoryDashboards.actions;

export const useFormInventoryDashboardsActions = () => {
    return bindActionCreators(
        {
            getAdminClifsaAdmin,
            getSpecificClifsaDashboard
        },
        useDispatch()
    );
};

export default formInventoryDashboards.reducer;