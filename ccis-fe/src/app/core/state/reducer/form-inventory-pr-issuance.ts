import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  TPRIssuanceState,
  IPRIssuancePayload,
  IPRIssuanceWithIndexPayload,
  TGetPRIssuancesWithFilterActionPayload,
  TIPRIssuanceActionPayload,
  TPRIssuanceIDPayloadActionPayload,
  TPRIssuanceWithIndexActionPayload,
} from "@state/types/form-inventory-pr-issuance";
import { useDispatch } from "react-redux";
import { showSuccess } from "@helpers/prompt";

const initialState: TPRIssuanceState = {
  prIssuances: [],
  selectedPRIssuance: {
    index: 0,
    data: {
      id: 0,
      seriesNo: 0,
      padAssignmentId: 0,
      cooperativeId: 0,
      productId: 0,
      status: "",
      remarks: "",
      remitTo: "",
      createdAt: "",
      releasedAt: "",
      paymentDetail: [
        {
          paymentMethodId: "",
          amount: 0,
          dateDeposit: "",
          bankAccountId: 0,
          attachments: [],
        },
      ],
    },
  },
  getPRIssuances: {
    loading: false,
    success: false,
    error: false,
  },
  getPRIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  postPRIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  putPRIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  destroyPRIssuance: {
    loading: false,
    success: false,
    error: false,
  },
  prIssuanceDetails: {
    loading: false,
    success: false,
    error: false,
  },
};

const prIssuanceSlice = createSlice({
  name: "prIssuanceManagement",
  initialState,
  reducers: {
    setSelectedPRIssuance(state, action: TPRIssuanceWithIndexActionPayload) {
      state.selectedPRIssuance = action.payload;
    },
    clearSelectedPRIssuance(state) {
      state.selectedPRIssuance = initialState.selectedPRIssuance;
    },

    getPRIssuances(state, _action: TGetPRIssuancesWithFilterActionPayload) {
      state.getPRIssuances = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getPRIssuancesSuccess(state, action) {
      state.prIssuances = [...action.payload].reverse();
      state.getPRIssuances = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getPRIssuancesFailure(state) {
      state.getPRIssuances = {
        loading: false,
        success: false,
        error: true,
      };
    },

    getPRIssuance(state, _action: TPRIssuanceIDPayloadActionPayload) {
      state.getPRIssuance = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getPRIssuanceSuccess(state, action) {
      state.selectedPRIssuance = action.payload;
      state.getPRIssuance = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getPRIssuanceFailure(state) {
      state.getPRIssuance = {
        loading: false,
        success: false,
        error: true,
      };
    },
    postPRIssuance(state, _action: PayloadAction<IPRIssuancePayload>) {
      state.postPRIssuance = {
        loading: true,
        success: false,
        error: false,
      };
    },
    postPRIssuanceSuccess(state, action: TIPRIssuanceActionPayload) {
      state.prIssuances.unshift(action.payload);
      state.postPRIssuance = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    postPRIssuanceFailure(state) {
      state.postPRIssuance = {
        loading: false,
        success: false,
        error: true,
      };
    },
    
    putPRIssuance(state, _action: PayloadAction<IPRIssuancePayload>) {
      state.putPRIssuance = {
        loading: true,
        success: false,
        error: false,
      };
    },

    putPRIssuanceSuccess(state, action: TIPRIssuanceActionPayload) {
      state.prIssuances = state.prIssuances.map((prIssuance) => (prIssuance.id === action.payload.id ? action.payload : prIssuance));
      state.putPRIssuance = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    putPRIssuanceFailure(state) {
      state.putPRIssuance = {
        loading: false,
        success: false,
        error: true,
      };
    },
    destroyPRIssuance(state, _action: PayloadAction<IPRIssuanceWithIndexPayload>) {
      state.destroyPRIssuance = {
        loading: true,
        success: false,
        error: false,
      };
    },
    destroyPRIssuanceSuccess(state, action) {
      state.prIssuances?.splice(action.payload, 1);
      state.destroyPRIssuance = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess("PRIssuance deleted successfully.");
    },
    destroyPRIssuanceFailure(state) {
      state.destroyPRIssuance = {
        loading: false,
        success: false,
        error: true,
      };
    },
    destroyPRIssuanceReset(state) {
      state.destroyPRIssuance = {
        loading: false,
        success: false,
        error: false,
      };
    },

    getPRIssuanceDetails(state, _action: PayloadAction<{ id: number }>) {
      state.prIssuanceDetails = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },

    getPRIssuanceDetailsSuccess(state, action) {
      state.prIssuanceDetails = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },

    getPRIssuanceDetailsFailure(state) {
      state.prIssuanceDetails = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
  },
});

export const {
  setSelectedPRIssuance,
  clearSelectedPRIssuance,
  getPRIssuances,
  getPRIssuancesSuccess,
  getPRIssuancesFailure,
  getPRIssuance,
  getPRIssuanceSuccess,
  getPRIssuanceFailure,
  postPRIssuance,
  postPRIssuanceSuccess,
  postPRIssuanceFailure,
  putPRIssuance,
  putPRIssuanceSuccess,
  putPRIssuanceFailure,
  destroyPRIssuance,
  destroyPRIssuanceSuccess,
  destroyPRIssuanceFailure,
  destroyPRIssuanceReset,
  getPRIssuanceDetails,
  getPRIssuanceDetailsSuccess,
  getPRIssuanceDetailsFailure,
} = prIssuanceSlice.actions;

export const usePRIssuanceActions = () => {
  return bindActionCreators(
    {
      setSelectedPRIssuance,
      clearSelectedPRIssuance,
      getPRIssuances,
      getPRIssuance,
      postPRIssuance,
      putPRIssuance,
      destroyPRIssuance,
      destroyPRIssuanceReset,
      getPRIssuanceDetails,
      getPRIssuanceDetailsSuccess,
      getPRIssuanceDetailsFailure,
    },
    useDispatch()
  );
};

export default prIssuanceSlice.reducer;
