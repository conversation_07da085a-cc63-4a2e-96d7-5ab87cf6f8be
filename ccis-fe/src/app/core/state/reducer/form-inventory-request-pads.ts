import { IDefaultParams } from "@interface/common.interface";
import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { IPadRequestPayload, TIPadRequestFormActionPayload, TPadRequestFormState, TPadRequestIDPayloadActionPayload } from "@state/types/form-inventory-request-pads";
import { useDispatch } from "react-redux";

const initialState: TPadRequestFormState = {
  padRequests: [],
  getPadRequests: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  getPadRequest: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  postPadRequest: {
    loading: false,
    success: false,
    error: false,
  },
  putPadRequest: {
    loading: false,
    success: false,
    error: false,
  },
};

const padRequestFormSlice = createSlice({
  name: "padRequestForm",
  initialState,
  reducers: {
    getPadRequests(state, _action: PayloadAction<IDefaultParams>) {
      state.getPadRequests = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getPadRequestsSuccess(state, action) {
      state.getPadRequests = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getPadRequestsFailure(state) {
      state.getPadRequests = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    getPadRequest(state, _action: TPadRequestIDPayloadActionPayload) {
      state.getPadRequest = {
        data: undefined,
        loading: true,
        success: false,
        error: false,
      };
    },
    getPadRequestSuccess(state, action) {
      state.getPadRequest = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getPadRequestFailure(state) {
      state.getPadRequest = {
        data: undefined,
        loading: false,
        success: false,
        error: true,
      };
    },
    postPadRequest(state, _action: PayloadAction<IPadRequestPayload>) {
      state.postPadRequest = {
        loading: true,
        success: false,
        error: false,
      };
    },
    postPadRequestSuccess(state, action: TIPadRequestFormActionPayload) {
        state.padRequests.unshift(action.payload);
        state.postPadRequest = {
        loading: false,
        success: true,
        error: false,
      };
    },
    postPadRequestFailure(state) {
      state.postPadRequest = {
        loading: false,
        success: false,
        error: true,
      };
    },
    
  },
});

export const {
    getPadRequests,
    getPadRequestsSuccess,
    getPadRequestsFailure,
    getPadRequest,
    getPadRequestSuccess,
    getPadRequestFailure,
    postPadRequest,
    postPadRequestSuccess,
    postPadRequestFailure,
    } = padRequestFormSlice.actions;

export const usePadRequestActions = () => {
    return bindActionCreators(
        {
            getPadRequests,
            getPadRequest,
            postPadRequest,
        },
        useDispatch()
    );
};

export default padRequestFormSlice.reducer;