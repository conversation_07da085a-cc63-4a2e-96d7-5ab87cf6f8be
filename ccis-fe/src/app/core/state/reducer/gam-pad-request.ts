import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "redux";
import { useDispatch } from "react-redux";

import { TGamPadRequestCreatePayloadAction, TGamPadRequestState, TGamPadRequestUpdatePayloadAction, TGamPadRequestWithIndex } from "@state/types/gam-pad-request";
import { IDefaultParams } from "@interface/common.interface";
import { showSuccess } from "@helpers/prompt";

const initialState: TGamPadRequestState = {
  remainingPads: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
  lastSeries: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
  postGamPadRequest: {
    loading: false,
    success: false,
    error: false,
  },
  gamPadRequesTable: {
    selectedRecord: {} as TGamPadRequestWithIndex,
    fetchResult: {
      data: undefined,
      loading: false,
      success: false,
      error: false,
    },
  },
  gamPadRequestDetails: {
    loading: false,
    success: false,
    error: false,
  },
  putGamPadRequest: {
    loading: false,
    success: false,
    error: false,
  },
  nextSeries: {
    loading: false,
    success: false,
    error: false,
  },
};

const padRequestSlice = createSlice({
  name: "padRequest",
  initialState,
  reducers: {
    getPadRequests(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.gamPadRequesTable.fetchResult = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getPadRequestsSuccess(state, action) {
      state.gamPadRequesTable.fetchResult = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getPadRequestsFailure(state) {
      state.gamPadRequesTable.fetchResult = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getPadRequestDetails(state, _action: PayloadAction<{ id: number }>) {
      state.gamPadRequestDetails = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getPadRequestDetailsSuccess(state, action) {
      state.gamPadRequestDetails = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getPadRequestDetailsFailure(state) {
      state.gamPadRequestDetails = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getRemainingPads(state, _action: PayloadAction<{ id: number }>) {
      state.remainingPads = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getRemainingPadsSuccess(state, action) {
      state.remainingPads = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getRemainingPadsFailure(state) {
      state.remainingPads = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getLastSeries(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.lastSeries = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getLastSeriesSuccess(state, action) {
      state.lastSeries = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getLastSeriesFailure(state) {
      state.lastSeries = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    postGamPadRequest(state, _action: TGamPadRequestCreatePayloadAction) {
      state.postGamPadRequest = {
        loading: true,
        success: false,
        error: false,
      };
    },
    postGamPadRequestSuccess(state) {
      state.postGamPadRequest = {
        loading: false,
        success: true,
        error: false,
      };
    },
    postGamPadRequestFailure(state) {
      state.postGamPadRequest = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    postGamPadRequestStateDefault(state) {
      state.postGamPadRequest = {
        loading: false,
        success: false,
        error: false,
      };
    },
    putGamPadRequest(state, _action: TGamPadRequestUpdatePayloadAction) {
      state.putGamPadRequest = {
        loading: true,
        success: false,
        error: false,
      };
    },
    putGamPadRequestSuccess(state) {
      state.putGamPadRequest = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess("Pad request updated successfully");
    },
    putGamPadRequestFailure(state) {
      state.putGamPadRequest = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
    getNextSeries(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.nextSeries = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getNextSeriesSuccess(state, action) {
      state.nextSeries = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getNextSeriesFailure(state) {
      state.nextSeries = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },
  },
});

export const {
  getPadRequests,
  getPadRequestsSuccess,
  getPadRequestsFailure,
  getPadRequestDetails,
  getPadRequestDetailsSuccess,
  getPadRequestDetailsFailure,
  getRemainingPads,
  getRemainingPadsFailure,
  getRemainingPadsSuccess,
  getLastSeries,
  getLastSeriesSuccess,
  getLastSeriesFailure,
  postGamPadRequest,
  postGamPadRequestFailure,
  postGamPadRequestSuccess,
  postGamPadRequestStateDefault,
  putGamPadRequest,
  putGamPadRequestFailure,
  putGamPadRequestSuccess,
  getNextSeries,
  getNextSeriesFailure,
  getNextSeriesSuccess,
} = padRequestSlice.actions;

export const usePadRequestActions = () => {
  return bindActionCreators(
    {
      getPadRequests,
      getPadRequestsSuccess,
      getPadRequestsFailure,
      getPadRequestDetails,
      getPadRequestDetailsSuccess,
      getPadRequestDetailsFailure,
      getRemainingPads,
      getRemainingPadsFailure,
      getRemainingPadsSuccess,
      getLastSeries,
      getLastSeriesSuccess,
      getLastSeriesFailure,
      postGamPadRequest,
      postGamPadRequestFailure,
      postGamPadRequestSuccess,
      postGamPadRequestStateDefault,
      putGamPadRequest,
      putGamPadRequestFailure,
      putGamPadRequestSuccess,
      getNextSeries,
      getNextSeriesFailure,
      getNextSeriesSuccess,
    },
    useDispatch()
  );
};

export default padRequestSlice.reducer;
