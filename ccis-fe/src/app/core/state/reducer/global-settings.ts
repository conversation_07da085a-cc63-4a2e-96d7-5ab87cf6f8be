// Import dependencies
import { createDynamicState } from "@helpers/array";
import { showSuccess } from "@helpers/prompt";
import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
// Import all the types that we would use for the operations here. 
import {
    TIGlobalSettingsActionPayloadPostSuccess,
    TIGlobalSettingsActionPayloadSelectedPutSuccess,
    TGlobalSettingsActionPayloadPostPut,
    TGlobalSettingsDelete,
    TGlobalSettingsManagementState,
} from "@state/types/global-settings";

import { useDispatch } from "react-redux";

//For Optimization of the Get
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

const initialState: TGlobalSettingsManagementState = {
    globalSettings: [],
    selectedGlobalSettings: {
        index: 0,
        data: {
            key: "",
            value: "",
        },
    },
    getGlobalSettings: createDynamicState(),
    postGlobalSettings: createDynamicState(),
    putGlobalSettings: createDynamicState(),
    destroyGlobalSettings: createDynamicState(),
};

const globalSettingsManagementSlice = createSlice({
    name: "globalSettings",
    initialState,
    reducers: {
        setSelectedGlobalSettings(
            state,
            action: TIGlobalSettingsActionPayloadSelectedPutSuccess
        ) {
            state.selectedGlobalSettings = action.payload;
        },
        clearSelectedGlobalSettings(state) {
            state.selectedGlobalSettings = initialState.selectedGlobalSettings;
        },
        getGlobalSettings(
            state,
            _action: PayloadAction<{params: IDefaultParams}>
        ) {
            state.getGlobalSettings = createDynamicState(["loading"]);
        },
        getGlobalSettingsSuccess(state, action) {
            state.globalSettings = action.payload.data;
            state.getGlobalSettings = createDynamicState(["success"], action.payload);
        },
        getGlobalSettingsFailure(state) {
            state.getGlobalSettings = createDynamicState(["error"]);
        },
        postGlobalSettings(state, _action: TGlobalSettingsActionPayloadPostPut) {
            state.postGlobalSettings = createDynamicState(["loading"]);
        },
        postGlobalSettingsSuccess(
            state,
            action: TIGlobalSettingsActionPayloadPostSuccess
        ) {
            state.globalSettings?.unshift(action.payload);
            state.postGlobalSettings = createDynamicState(["success"]);
            showSuccess();
        },
        postGlobalSettingsFailure(state) {
            state.postGlobalSettings = createDynamicState(["error"]);
        },
        putGlobalSettings(state, _action: TGlobalSettingsActionPayloadPostPut) {
            state.putGlobalSettings = createDynamicState(["loading"]);
        },
        putGlobalSettingsSuccess(
            state,
            action: TIGlobalSettingsActionPayloadSelectedPutSuccess
        ) {
            state.globalSettings[state.selectedGlobalSettings.index] =
                action.payload.data;
            state.putGlobalSettings = createDynamicState(["success"]);
            showSuccess();
        },
        putGlobalSettingsFailure(state) {
            state.putGlobalSettings = createDynamicState(["error"]);
        },
        destroyGlobalSettings(state, _action: TGlobalSettingsDelete) {
            state.destroyGlobalSettings = createDynamicState(["loading"]);
        },
        destroyGlobalSettingsSuccess(state, action) {
            state.globalSettings?.splice(action.payload, 1);
            state.destroyGlobalSettings = createDynamicState(["success"]);
            showSuccess();
        },
        destroyGlobalSettingsFailure(state) {
            state.destroyGlobalSettings = createDynamicState(["error"]);
        },
    },
});

export const {
    setSelectedGlobalSettings,
    clearSelectedGlobalSettings,
    getGlobalSettings,
    getGlobalSettingsSuccess,
    getGlobalSettingsFailure,
    postGlobalSettings,
    postGlobalSettingsSuccess,
    postGlobalSettingsFailure,
    putGlobalSettings,
    putGlobalSettingsSuccess,
    putGlobalSettingsFailure,
    destroyGlobalSettings,
    destroyGlobalSettingsSuccess,
    destroyGlobalSettingsFailure,
} = globalSettingsManagementSlice.actions;

export const useGlobalSettingsManagementActions = () => {
    return bindActionCreators(
        {
            setSelectedGlobalSettings,
            clearSelectedGlobalSettings,
            getGlobalSettings,
            postGlobalSettings,
            putGlobalSettings,
            destroyGlobalSettings,
        },
        useDispatch()
    );
};
export default globalSettingsManagementSlice.reducer;
