import { createSlice } from "@reduxjs/toolkit";
import { bindActionCreators } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import { TManagerIdManagementState } from "@state/types/managerId-type";
import { createDynamicState } from "@helpers/array";

const initialState: TManagerIdManagementState = {
  managerIds: [],
  selectedManagerId: {
    index: 0,
    data: {
      id: 0,
      key: "",
      value: "",
      createdAt: null,
      updatedAt: null,
      deletedAt: null,
    },
  },
  getManagerId: createDynamicState(),
};

const managerIdSlice = createSlice({
  name: "managerId",
  initialState,
  reducers: {
    resetState(state) {
      state.managerIds = initialState.managerIds;
      state.getManagerId = initialState.getManagerId;
    },
    getManagerId(state) {
      state.getManagerId = createDynamicState(["loading"]);
    },

    getManagerIdSuccess(state, action) {
      //Added .data in payload
      state.managerIds = action.payload.data.reverse();

      state.getManagerId = createDynamicState(["success"], action.payload);
    },
    getManagerIdFailure(state) {
      state.getManagerId = createDynamicState(["error"]);
    },
  },
});

export const { resetState, getManagerId, getManagerIdSuccess, getManagerIdFailure } = managerIdSlice.actions;

export const useManagerIdActions = () => {
  return bindActionCreators(
    {
      resetState,
      getManagerId,
    },
    useDispatch()
  );
};

export default managerIdSlice.reducer;
