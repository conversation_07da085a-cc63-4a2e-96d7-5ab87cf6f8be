import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import { TNotificationState, TNotificationsIDPayload } from "@state/types/notification";
import { INotification } from "@interface/notification.interface";

const initialState: TNotificationState = {
  notifications: [],
  getNotifications: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
  patchMarkAllAsReadNotifications: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
  patchReadNotification: {
    loading: false,
    success: false,
    error: false,
    data: undefined,
  },
};

const notificationSlice = createSlice({
  name: "notification",
  initialState,
  reducers: {
    // For Websocket for new notifications
    addNotification: (state, action: PayloadAction<INotification>) => {
      const exists = state.notifications.some((notif) => notif.id === action.payload.id);
      if (!exists) {
        state.notifications.unshift(action.payload); // Add to top
      }
    },
    // For api response for unread notifications
    setNotifications: (state, action: PayloadAction<INotification[]>) => {
      state.notifications = action.payload;
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    removeNotification: (state, action: PayloadAction<number>) => {
      state.notifications = state.notifications.filter((notif) => notif.id !== action.payload);
    },

    getNotifications: (state) => {
      state.getNotifications = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    getNotificationsSuccess: (state, action: PayloadAction<INotification[]>) => {
      state.notifications = action.payload;
      state.getNotifications = {
        loading: false,
        success: true,
        error: false,
        data: action.payload,
      };
    },
    getNotificationsFailure: (state) => {
      state.getNotifications = {
        loading: false,
        success: false,
        error: true,
        data: undefined,
      };
    },

    patchMarkAllAsReadNotifications: (state) => {
      state.patchMarkAllAsReadNotifications = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    patchMarkAllAsReadNotificationsSuccess: (state, action) => {
      state.patchMarkAllAsReadNotifications = {
        loading: false,
        success: true,
        error: false,
        data: action.payload, // Assuming no data is returned on success
      };
    },
    patchMarkAllAsReadNotificationsFailure: (state) => {
      state.patchMarkAllAsReadNotifications = {
        loading: false,
        success: false,
        error: true,
        data: undefined, // Assuming no data is returned on failure
      };
    },

    patchReadNotification: (state, _action: PayloadAction<TNotificationsIDPayload>) => {
      state.patchReadNotification = {
        loading: true,
        success: false,
        error: false,
        data: undefined,
      };
    },
    patchReadNotificationSuccess: (state, action) => {
      state.patchReadNotification = {
        loading: false,
        success: true,
        error: false,
        data: action.payload, // Assuming the updated notification data is returned on success
      };
    },
    patchReadNotificationFailure: (state) => {
      state.patchReadNotification = {
        loading: false,
        success: false,
        error: true,
      };
    },
  },
});

export const {
  addNotification,
  clearNotifications,
  setNotifications,
  removeNotification,
  getNotifications,
  getNotificationsSuccess,
  getNotificationsFailure,
  patchMarkAllAsReadNotifications,
  patchMarkAllAsReadNotificationsSuccess,
  patchMarkAllAsReadNotificationsFailure,
  patchReadNotification,
  patchReadNotificationSuccess,
  patchReadNotificationFailure,
} = notificationSlice.actions;

export const useNotificationActions = () => {
  return bindActionCreators(
    {
      getNotifications,
      patchMarkAllAsReadNotifications,
      patchReadNotification,
      addNotification,
      clearNotifications,
      setNotifications,
      removeNotification,
    },
    useDispatch()
  );
};

export default notificationSlice.reducer;
