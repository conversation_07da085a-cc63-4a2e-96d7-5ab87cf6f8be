import { ISystemSettingsIdsResponse, TSystemSettingsIdsState } from "@interface/system-settings-ids.interface";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { bindActionCreators } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";

const initialState: TSystemSettingsIdsState = {
  getSystemSettingsIds: {
    loading: false,
    success: false,
    error: false,
  },
};

const systemSettingsIdsSlice = createSlice({
  name: "systemSettingsIds",
  initialState,
  reducers: {
    resetState(state) {
      state.getSystemSettingsIds = initialState.getSystemSettingsIds;
    },
    getSystemSettingsIds(state) {
      state.getSystemSettingsIds = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getSystemSettingsIdsSuccess(state, action: PayloadAction<ISystemSettingsIdsResponse>) {
      state.getSystemSettingsIds = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getSystemSettingsIdsError(state) {
      state.getSystemSettingsIds = {
        loading: false,
        success: false,
        error: true,
      };
    },
  },
});

export const { resetState, getSystemSettingsIds, getSystemSettingsIdsSuccess, getSystemSettingsIdsError } = systemSettingsIdsSlice.actions;

export const useSystemSettingsIdsActions = () => {
  return bindActionCreators(
    {
      resetState,
      getSystemSettingsIds,
      getSystemSettingsIdsSuccess,
      getSystemSettingsIdsError,
    },
    useDispatch()
  );
};

export default systemSettingsIdsSlice.reducer;
