import { IClifsaAdminDashboardParams } from "@interface/form-inventory-dashboard";
import { PayloadAction } from "@reduxjs/toolkit";
import { getAdminDashboardMetricsService, getSpecificRoleDashboardMetricsService } from "@services/form-inventory-dashboards/form-inventory-dashboards.service";
import { handleServerException } from "@services/utils/utils.service";
import { getAdminClifsaAdmin, getAdminClifsaAdminFailure, getAdminClifsaAdminSuccess, getSpecificClifsaDashboard, getSpecificClifsaDashboardFailure, getSpecificClifsaDashboardSuccess } from "@state/reducer/form-inventory-dashboards";
import { AxiosResponse } from "axios";
import { call, put, takeLatest } from "redux-saga/effects";

// Interface for dashboard-specific parameters

function* getSpecificClifsaDashboardSaga(action: PayloadAction<{ metricType: string, params: IClifsaAdminDashboardParams }>) {
  try {
    const { metricType, params } = action.payload;
    const result: AxiosResponse = yield call(getSpecificRoleDashboardMetricsService, metricType, params);
    yield put(getSpecificClifsaDashboardSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getSpecificClifsaDashboardFailure.type, true);
  }
}

function* getAdminClifsaDashboardSaga(action: PayloadAction<{ params: IClifsaAdminDashboardParams }>) {
  try {
    const { params } = action.payload;
    const result: AxiosResponse = yield call(getAdminDashboardMetricsService, "form", params);
    yield put(getAdminClifsaAdminSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getAdminClifsaAdminFailure.type, true);
  }
}

export function* rootSaga() {
    yield takeLatest(getSpecificClifsaDashboard.type, getSpecificClifsaDashboardSaga);
    yield takeLatest(getAdminClifsaAdmin.type, getAdminClifsaDashboardSaga);

}