import { TGetPRIssuancesWithFilterActionPayload } from "@state/types/form-inventory-pr-issuance";
import { takeLatest, call, put } from "redux-saga/effects"; // Importing necessary functions from redux-saga
import { AxiosResponse } from "axios"; // Importing AxiosResponse type from axios
import {
  getPRIssuances,
  getPRIssuancesSuccess,
  getPRIssuancesFailure,
  getPRIssuance,
  getPRIssuanceSuccess,
  getPRIssuanceFailure,
  destroyPRIssuance,
  destroyPRIssuanceSuccess,
  destroyPRIssuanceFailure,
  getPRIssuanceDetails,
  getPRIssuanceDetailsSuccess,
  getPRIssuanceDetailsFailure,
} from "@state/reducer/form-inventory-pr-issuance";

import { handleServerException } from "@services/utils/utils.service";
import { TPRIssuanceIDPayloadActionPayload } from "@state/types/form-inventory-pr-issuance";
import { PayloadAction } from "@reduxjs/toolkit";
import { toast } from "react-toastify";
import { destroyPRIssuanceService, getPRIssuanceDetailsService, getPRIssuanceService, getPRIssuancesService } from "@services/pr-issuance/pr-issuance-service";

function* getPRIssuancesSaga({ payload }: TGetPRIssuancesWithFilterActionPayload) {
  try {
    const result: AxiosResponse = yield call(getPRIssuancesService, payload.filter);
    yield put(getPRIssuancesSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getPRIssuancesFailure.type, true);
  }
}

function* getPRIssuanceSaga(actions: PayloadAction<{ id: number }>) {
  try {
    const result: AxiosResponse = yield call(getPRIssuanceService, actions.payload.id);
    yield put(getPRIssuanceSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getPRIssuanceFailure.type, true);
  }
}

function* getPRIssuanceDetailsSaga(actions: PayloadAction<{ id: number }>) {
  try {
    const result: AxiosResponse = yield call(getPRIssuanceDetailsService, actions.payload.id);
    yield put(getPRIssuanceDetailsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getPRIssuanceDetailsFailure.type, true);
  }
}

// // Saga for handling post
// function* postPRIssuanceSaga({ payload }: TPRIssuancePayloadAction) {
//   try {
//     const { data }: AxiosResponse<IPRIssuance> = yield call(postPRIssuanceService, payload);
//     yield put(postPRIssuanceSuccess(data));
//     toast.success("Successfully created new PRIssuance");
//   } catch (error) {
//     yield call(handleServerException, error, postPRIssuanceFailure.type, true);
//   }
// }

// function* putPRIssuanceSaga(actions: TPRIssuancePayloadAction & { id: number }) {
//   try {
//     const { data }: AxiosResponse = yield call(putPRIssuanceService, actions.payload);
//     yield put(putPRIssuanceSuccess(data));
//     toast.success("Successfully updated PRIssuance");
//   } catch (error) {
//     yield call(handleServerException, error, putPRIssuanceFailure.type, true);
//   }
// }

function* destroyPRIssuanceSaga({ payload }: TPRIssuanceIDPayloadActionPayload) {
  try {
    yield call(destroyPRIssuanceService, payload);
    yield put(destroyPRIssuanceSuccess(payload.index));
    toast.success("Successfully deleted PRIssuance");
  } catch (error) {
    yield call(handleServerException, error, destroyPRIssuanceFailure.type, true);
  }
}

export function* PRIssuanceSaga() {
  yield takeLatest(getPRIssuances.type, getPRIssuancesSaga);
  yield takeLatest(getPRIssuance.type, getPRIssuanceSaga);
  // yield takeLatest(postPRIssuance.type, postPRIssuanceSaga);
  // yield takeLatest(putPRIssuance.type, putPRIssuanceSaga);
  yield takeLatest(destroyPRIssuance.type, destroyPRIssuanceSaga);
  //
  yield takeLatest(getPRIssuanceDetails.type, getPRIssuanceDetailsSaga);
}
