import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { call, put, takeLatest } from "redux-saga/effects";
import { AxiosResponse } from "axios";
import { getPadRequestByIdService, getPadRequestsService, postPadRequestService } from "@services/form-inventory-request-pads/form-inventory-request-pads.service";
import { getPadRequest, getPadRequests, getPadRequestsFailure, getPadRequestsSuccess, postPadRequest, postPadRequestFailure, postPadRequestSuccess } from "@state/reducer/form-inventory-request-pads";
import { handleServerException } from "@services/utils/utils.service";
import { TIPadRequestFormActionPayload } from "@state/types/form-inventory-request-pads";
import { IPadRequest } from "@interface/form-inventory.interface";
import { toast } from "react-toastify";

function* getPadRequestsSaga(action: PayloadAction<IDefaultParams>) {
  try {
    const response: AxiosResponse = yield call(getPadRequestsService, action.payload);
    yield put(getPadRequestsSuccess(response));
  } catch (error) {
    yield call(handleServerException, error, getPadRequestsFailure.type, true);
  }
}
function* getPadRequestSaga(actions: PayloadAction<{ id: number }>) {
  try {
    const response: AxiosResponse = yield call(getPadRequestByIdService, actions.payload.id);
    yield put(getPadRequestsSuccess(response));
  } catch (error) {
    yield call(handleServerException, error, getPadRequestsFailure.type, true);
  }
}
function* postPadRequestSaga({ payload }: TIPadRequestFormActionPayload) {
  try {
    const { data }: AxiosResponse<IPadRequest> = yield call(postPadRequestService, payload);
    yield put(postPadRequestSuccess(data));
    toast.success("Successfully created new Bank");
  } catch (error) {
    yield call(handleServerException, error, postPadRequestFailure.type, true);
  }
}

export function* padRequestSaga() {
  yield takeLatest(getPadRequests.type, getPadRequestsSaga);
  yield takeLatest(getPadRequest.type, getPadRequestSaga);
  yield takeLatest(postPadRequest.type, postPadRequestSaga);
}
