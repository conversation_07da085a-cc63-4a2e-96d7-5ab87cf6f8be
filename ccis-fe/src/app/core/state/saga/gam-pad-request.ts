import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import {
  getGamPadRequestDetailsService,
  getGamPadRequestService,
  getLastSeriesNumberService,
  getNextSeriesService,
  getRemainingPadsService,
  postGamPadRequestService,
  putGamPadRequestService,
} from "@services/gam-pad-request/gam-pad-request.service";
import { handleServerException } from "@services/utils/utils.service";
import {
  getPadRequests,
  getPadRequestsSuccess,
  getPadRequestsFailure,
  getPadRequestDetails,
  getPadRequestDetailsSuccess,
  getPadRequestDetailsFailure,
  getRemainingPads,
  getRemainingPadsFailure,
  getRemainingPadsSuccess,
  getLastSeries,
  getLastSeriesSuccess,
  getLastSeriesFailure,
  postGamPadRequest,
  postGamPadRequestFailure,
  postGamPadRequestSuccess,
  putGamPadRequest,
  putGamPadRequestFailure,
  putGamPadRequestSuccess,
  getNextSeries,
  getNextSeriesFailure,
  getNextSeriesSuccess,
} from "@state/reducer/gam-pad-request";
import { TGamPadRequestCreatePayloadAction, TGamPadRequestUpdatePayloadAction } from "@state/types/gam-pad-request";
import { AxiosResponse } from "axios";
import { call, put, takeLatest } from "redux-saga/effects";

function* getGamPadRequestSaga({ payload }: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getGamPadRequestService, payload.params);
    yield put(getPadRequestsSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getPadRequestsFailure.type, true);
  }
}

function* getGamPadRequestDetailsSaga({ payload }: PayloadAction<{ id: number }>) {
  try {
    const result: AxiosResponse = yield call(getGamPadRequestDetailsService, payload.id);
    yield put(getPadRequestDetailsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getPadRequestDetailsFailure.type, true);
  }
}

function* getRemainingPadsSaga({ payload }: PayloadAction<{ id: number }>) {
  try {
    const result: AxiosResponse = yield call(getRemainingPadsService, payload.id);
    yield put(getRemainingPadsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getRemainingPadsFailure.type, true);
  }
}

function* getLastSeriesSaga({ payload }: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getLastSeriesNumberService, { filter: payload.params.filter });
    yield put(getLastSeriesSuccess(result.data[0]));
  } catch (error) {
    yield call(handleServerException, error, getLastSeriesFailure.type, true);
  }
}

function* postGamPadRequestSaga(actions: TGamPadRequestCreatePayloadAction) {
  try {
    const { data }: AxiosResponse = yield call(postGamPadRequestService, actions.payload); // Call the postProducts service with the action payload and destructure data from the response
    yield put(postGamPadRequestSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, postGamPadRequestFailure.type, true); // Handle any errors using handleServerException utility
  }
}

function* putGamPadRequestSaga(actions: TGamPadRequestUpdatePayloadAction) {
  try {
    const { data }: AxiosResponse = yield call(putGamPadRequestService, actions.payload); // Call the postProducts service with the action payload and destructure data from the response
    yield put(putGamPadRequestSuccess(data));
  } catch (error) {
    yield call(handleServerException, error, putGamPadRequestFailure.type, true); // Handle any errors using handleServerException utility
  }
}

function* getNextSeriesSaga({ payload }: PayloadAction<{ params: IDefaultParams }>) {
  try {
    const result: AxiosResponse = yield call(getNextSeriesService, payload.params);
    yield put(getNextSeriesSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getNextSeriesFailure.type, true);
  }
}

export function* GamRequestSaga() {
  yield takeLatest(getPadRequests.type, getGamPadRequestSaga);
  yield takeLatest(getPadRequestDetails.type, getGamPadRequestDetailsSaga);
  yield takeLatest(getRemainingPads.type, getRemainingPadsSaga);
  yield takeLatest(getLastSeries.type, getLastSeriesSaga);
  yield takeLatest(postGamPadRequest.type, postGamPadRequestSaga);
  yield takeLatest(putGamPadRequest.type, putGamPadRequestSaga);
  yield takeLatest(getNextSeries.type, getNextSeriesSaga);
}
