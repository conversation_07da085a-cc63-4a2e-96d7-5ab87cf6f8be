// Imports for the new GET function
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
// Importing AxiosResponse type from axios
import { AxiosResponse } from "axios";
// Importing necessary functions from redux-saga
import { call, put, takeLatest } from "redux-saga/effects";
import { handleServerException } from "@services/utils/utils.service";
import {
    destroyGlobalSystemSettingsService,
    getGlobalSystemSettingsService,
    postBulkGlobalSystemSettingsService,
    putGlobalSystemSettingsService,
} from "@services/global-settings/global-settings.service";
import{
    getGlobalSettings,
    getGlobalSettingsSuccess,
    getGlobalSettingsFailure,
    postGlobalSettings,
    postGlobalSettingsSuccess,
    postGlobalSettingsFailure,
    putGlobalSettings,
    putGlobalSettingsSuccess,
    putGlobalSettingsFailure,
    destroyGlobalSettings,
    destroyGlobalSettingsSuccess,
    destroyGlobalSettingsFailure,
} from "@state/reducer/global-settings";
import{
    TGlobalSettingsActionPayloadPostPut,
    TGlobalSettingsDelete,
    BulkGlobalSettings,
} from "@state/types/global-settings";

function* getGlobalSettingsSaga(
    actions: PayloadAction<{params: IDefaultParams}>
) {
    try {
        const result: AxiosResponse = yield call(
            getGlobalSystemSettingsService,
            actions.payload.params
        );
        yield put(getGlobalSettingsSuccess(result));
    } catch (error) {
        yield call(
            handleServerException,
            error,
            getGlobalSettingsFailure.type,
            true
        );
    }
}

function *postGlobalSettingSaga(actions: PayloadAction<BulkGlobalSettings>) {
    try {
        const { data }: AxiosResponse = yield call(
            postBulkGlobalSystemSettingsService,
            actions.payload
        );
        yield put(postGlobalSettingsSuccess(data));
    } catch (error) {
        yield call(
            handleServerException,
            error,
            postGlobalSettingsFailure.type,
            true
        );
    }
}

function *putGlobalSettingSaga(actions: TGlobalSettingsActionPayloadPostPut) {
    try {
        const { data }: AxiosResponse = yield call(
            putGlobalSystemSettingsService,
            actions.payload
        );
        yield put(putGlobalSettingsSuccess(data));
    } catch (error) {
        yield call(
            handleServerException,
            error,
            putGlobalSettingsFailure.type,
            true
        );
    }
}

function *destroyGlobalSettingSaga(actions: TGlobalSettingsDelete) {
    try {
        yield call(destroyGlobalSystemSettingsService, actions.payload);
        yield put(destroyGlobalSettingsSuccess(actions.payload.index));
    } catch (error) {
        yield call(
            handleServerException,
            error,
            destroyGlobalSettingsFailure.type,
            true
        );
    }
}

export function* rootSaga() {
    yield takeLatest(getGlobalSettings.type, getGlobalSettingsSaga);
    yield takeLatest(postGlobalSettings.type, postGlobalSettingSaga);
    yield takeLatest(putGlobalSettings.type, putGlobalSettingSaga);
    yield takeLatest(destroyGlobalSettings.type, destroyGlobalSettingSaga);
}

