// Importing AxiosResponse type from axios
import { AxiosResponse } from "axios";
// Importing necessary functions from redux-saga
import { call, put, takeLatest } from "redux-saga/effects";
// Import the Services declared earlier
import {
  getManagerIdService,
} from "@services/manager-id/manager-ids.service";

import { handleServerException } from "@services/utils/utils.service";
// All reducer types here
import {
  getManagerId,
  getManagerIdFailure,
  getManagerIdSuccess,
} from "@state/reducer/manager-id";

function* getManagerIdSaga() {
  try {
    const dataResponse : AxiosResponse = yield call(
      getManagerIdService,
    );
    yield put(getManagerIdSuccess(dataResponse));
  } catch (error) {
    yield call(
      handleServerException,
      error,
      getManagerIdFailure.type,
      true
    );
  }
}


export function* rootSaga() {
  yield takeLatest(getManagerId.type, getManagerIdSaga);
}
