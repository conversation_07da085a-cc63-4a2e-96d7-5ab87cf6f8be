import { takeLatest, call, put } from "redux-saga/effects";
import { AxiosResponse } from "axios";
import {
  getNotifications,
  getNotificationsFailure,
  getNotificationsSuccess,
  patchReadNotification,
  patchMarkAllAsReadNotifications,
  patchMarkAllAsReadNotificationsFailure,
  patchMarkAllAsReadNotificationsSuccess,
  patchReadNotificationFailure,
  patchReadNotificationSuccess,
} from "@state/reducer/notification";
import { getNotificationsService, patchMarkAllAsReadNotificationService, patchReadNotificationService } from "@services/notification/notification.service"; // Importing service functions for user management
import { handleServerException } from "@services/utils/utils.service";
import { PayloadAction } from "@reduxjs/toolkit";
import { TNotificationsIDPayload } from "@state/types/notification";

function* getNotificationsSaga() {
  try {
    const result: AxiosResponse = yield call(getNotificationsService);
    yield put(getNotificationsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getNotificationsFailure.type, true);
  }
}

function* patchMarkAllAsReadNotificationsSaga() {
  try {
    const result: AxiosResponse = yield call(patchMarkAllAsReadNotificationService);
    yield put(patchMarkAllAsReadNotificationsSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, patchMarkAllAsReadNotificationsFailure.type, true);
  }
}

function* patchReadNotificationSaga(action: PayloadAction<TNotificationsIDPayload>) {
  try {
    const result: AxiosResponse = yield call(patchReadNotificationService, action.payload.id);
    yield put(patchReadNotificationSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, patchReadNotificationFailure.type, true);
  }
}

export function* rootSaga() {
  yield takeLatest(getNotifications.type, getNotificationsSaga);
  yield takeLatest(patchReadNotification.type, patchReadNotificationSaga);
  yield takeLatest(patchMarkAllAsReadNotifications.type, patchMarkAllAsReadNotificationsSaga);
}
