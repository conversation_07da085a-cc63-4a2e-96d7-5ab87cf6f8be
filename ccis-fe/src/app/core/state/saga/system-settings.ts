// Importing AxiosResponse type from axios
import { AxiosResponse } from "axios";
// Importing necessary functions from redux-saga
import { call, put, takeLatest } from "redux-saga/effects";
// Import the Services declared earlier

import { handleServerException } from "@services/utils/utils.service";
import { getGlobalSystemSettingsService } from "@services/global-settings/global-settings.service";
import { getSystemSettingsIds, getSystemSettingsIdsError, getSystemSettingsIdsSuccess } from "@state/reducer/system-settings-ids";
// All reducer types here

function* getSysmtemSettingsIds() {
  try {
    const dataResponse: AxiosResponse = yield call(getGlobalSystemSettingsService);
    yield put(getSystemSettingsIdsSuccess(dataResponse));
  } catch (error) {
    yield call(handleServerException, error, getSystemSettingsIdsError.type, true);
  }
}

export function* rootSaga() {
  yield takeLatest(getSystemSettingsIds.type, getSysmtemSettingsIds);
}
