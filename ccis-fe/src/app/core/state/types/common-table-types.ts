import { IDefaultPaginatedLinks, LoadingResult } from "@interface/common.interface";
import { IMeta } from ".";

export type TResponseDataWithIndex<T = any> = {
  id: number;
  data?: T;
  index: number;
};

export interface ITableApiResponse<T = any> {
  data?: T;
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export type TableState<T = any> = {
  selectedRecord: TResponseDataWithIndex<T> | undefined;
  tableData: ITableApiResponse<T> & LoadingResult;
};
