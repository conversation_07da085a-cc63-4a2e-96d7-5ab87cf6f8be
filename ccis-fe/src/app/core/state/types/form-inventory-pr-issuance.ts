import { PayloadAction } from "@reduxjs/toolkit";
import { IAttachments, LoadingResult } from "@interface/common.interface";
import { IPRIssuance } from "@interface/form-inventory.interface";

// State type for managing prIssuances
export type TPRIssuanceState = {
  prIssuances: IPRIssuance[];
  selectedPRIssuance: TPRIssuancePayloadWithIndex;
  getPRIssuances?: LoadingResult;
  getPRIssuance?: LoadingResult;
  postPRIssuance?: TPRIssuanceResponse;
  putPRIssuance?: TPRIssuanceResponse;
  destroyPRIssuance?: LoadingResult;
  //
  prIssuanceDetails: LoadingResult;
};

// Extending LoadingResult type to include a single IMasterlist data
export type TPRIssuanceResponse = LoadingResult & {
  data?: IPRIssuance; // Optional masterlist data
};

export type TPRIssuancePayload = {
  id?: number;
  seriesNo: number;
  padAssignmentId: number;
  cooperativeId: number;
  productId: number;
  status: string;
  remarks: string;
  remitTo: string;
  createdAt: string;
  releasedAt: string;
  paymentDetail: TPaymentDetail[];
};

// Payload type for creating or updating a prIssuance
export type IPRIssuancePayload = {
  id?: number;
  seriesNo: number;
  padAssignmentId: number;
  cooperativeId: number;
  productId: number;
  status: string;
  remarks: string;
  remitTo: string;
  createdAt: string;
  releasedAt: string;
  paymentDetail: TPaymentDetail[];
};

// Payload type for deleting a prIssuance
export type IPRIssuanceWithIndexPayload = {
  id: number;
  index: number;
};

export type TGetPRIssuancesWithFilterActionPayload = PayloadAction<{
  filter: string;
  id?: number | string;
}>;

// Defining payload that handles TMasterlistPayload type as data
export type TPRIssuancePayloadWithIndex = {
  data: IPRIssuancePayload;
  index: number;
};

export type TIPRIssuanceWithIndexPayload = {
  filter: string;
  id: number;
};

export type TPaymentDetail = {
  paymentMethodId: string;
  amount: number;
  dateDeposit: string;
  bankAccountId: number;
  attachments: IAttachments[];
};

// Action payloads for prIssuances
export type TPRIssuancePayloadAction = PayloadAction<TPRIssuancePayload>;
// export type TPRIssuanceActionPayload = PayloadAction<{ data?: IPRIssuance[] }>;
export type TIPRIssuanceActionPayload = PayloadAction<IPRIssuance>;
export type TPRIssuanceActionPayloadIPRIssuance = PayloadAction<TPRIssuancePayload>;

export type TPRIssuanceWithIndexActionPayload = PayloadAction<TPRIssuancePayloadWithIndex>;
export type TPRIssuanceIDPayloadActionPayload = PayloadAction<IPRIssuanceWithIndexPayload>;
export type TGetPRIssuanceWithFilterActionPayload = PayloadAction<{ filter?: string; id?: number }>;

// Payload for operations involving index and prIssuance data
// export type TPRIssuanceWithIndexActionPayload = PayloadAction<{ data: TPRIssuancePayload, index: number }>;
