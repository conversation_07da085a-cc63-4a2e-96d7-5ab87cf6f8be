import { IPadRequest } from "@interface/form-inventory.interface";
import { LoadingResult } from ".";
import { IUser } from "@interface/user.interface";
import { PayloadAction } from "@reduxjs/toolkit";
// State type for managing pad requests
export type TPadRequestFormState = {
  padRequests: IPadRequest[];
  getPadRequests: TIPadRequestResponse;
  getPadRequest?: LoadingResult;
  postPadRequest?: TIPadRequestResponse;
  putPadRequest?: TIPadRequestUpdateResponse;
};
// Extending LoadingResult type to include a single IPadRequest data
export type TIPadRequestResponse = LoadingResult & {
    data?: IPadRequest;
  };

// Define the structure for Bank data
export type TPadRequestPayload = {
    divisionId: number;
    areaId: number;
    formTypeId: number;
    numberOfPads: number;
    seriesFrom: number;
    seriesTo: number;
    releasedTo?: IUser;
    };

// Payload type for creating or updating 
export type IPadRequestPayload = {
  divisionId: number;
  areaId: number;
  formTypeId: number;
  numberOfPads: number;
  seriesFrom: number;
  seriesTo: number;
  releasedTo: IUser;
};

export type TIPadRequestUpdateResponse = LoadingResult & {
  data?: IPadRequest;
  status?: string;
};

export type TPadRequestPayloadWithIndex = {
  data: IPadRequestPayload;
  index: number;
};

export type IPadRequestWithIndexPayload = {
  id: number;
  index?: number;
};
export type TRequestPadPayloadAction = PayloadAction<TPadRequestPayload>;
//for Get
export type TPadRequestIDPayloadActionPayload =
  PayloadAction<IPadRequestWithIndexPayload>;
//for post
export type TIPadRequestFormActionPayload = PayloadAction<IPadRequest>;
