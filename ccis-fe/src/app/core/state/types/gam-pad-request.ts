import { IDefaultPaginatedLinks, LoadingResult } from "@interface/common.interface";
import { ICreatePadRequest, IGamPadRequest, IUpdatePadRequest } from "@interface/gam-request-pads";
import { PayloadAction } from "@reduxjs/toolkit";
import { IMeta } from ".";

export type TGamPadRequestState = {
  remainingPads: LoadingResult;
  lastSeries: LoadingResult;
  postGamPadRequest: LoadingResult;
  gamPadRequesTable: TableState;
  gamPadRequestDetails: LoadingResult;
  putGamPadRequest: LoadingResult;
  nextSeries: LoadingResult;
};

export type TGamPadRequestWithIndex = {
  id: number;
  data?: IGamPadRequest;
  index: number;
};

export interface ITableApiResponse {
  data: IGamPadRequest[]; // your main data interface
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export type TTableFetchResult = LoadingResult & {
  data?: ITableApiResponse;
};

export type TableState = {
  selectedRecord: TGamPadRequestWithIndex;
  fetchResult: TTableFetchResult;
};

export type TGamPadRequestCreatePayloadAction = PayloadAction<ICreatePadRequest>;

export type TGamPadRequestUpdatePayloadAction = PayloadAction<IUpdatePadRequest>;
