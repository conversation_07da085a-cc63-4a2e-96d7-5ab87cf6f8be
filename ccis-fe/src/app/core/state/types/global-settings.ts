import { IGlobalSettings, IGSettings } from "../../interface/global-settings";
import { LoadingResult } from "../../interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

export type TGlobalSettingsManagementState = {
  globalSettings: IGlobalSettings[];
  globalSetting?: IGlobalSettings;

  selectedGlobalSettings: TGlobalSettingsDataAndIndexPayload;

  getGlobalSettings?: LoadingResult;
  postGlobalSettings?: TGlobalSettingsResponse;
  putGlobalSettings?: TGlobalSettingsResponse;
  destroyGlobalSettings?: LoadingResult;
};

//For searching functions based on the ID and Index
export type TGlobalSettingsWithKeyAndIndexPayload = {
  key: string;
  index: number | string;
};

export type TGlobalSettingsWithIdAndIndexPayload = {
  id: number;
  index: number | string;
};

export type TGlobalSettingsResponse = LoadingResult & {
  data?: IGlobalSettings;
};

export type TGlobalSettingsPayload = {
  id?: number;
  key?: string;
  value?: number | string | unknown;
};

//If the index numbers doesn't work, I'm scrapping them
export type TIGlobalSettingsDataAndIndexPayload = {
  data: IGlobalSettings;
  index: number;
};

export type TGlobalSettingsDataAndIndexPayload = {
  data: TGlobalSettingsPayload;
  index: number;
};

//Specific for Bulk Global Post
export type BulkGlobalSettings = {
  settings?: IGSettings[];
};

export type TGlobalSettingsActionPayloadPostPut = 
  PayloadAction<TGlobalSettingsPayload>;
export type TIGlobalSettingsActionPayloadPostSuccess =
  PayloadAction<IGlobalSettings>;
export type TIGlobalSettingsActionPayloadSelectedPutSuccess = 
  PayloadAction<TIGlobalSettingsDataAndIndexPayload>
export type TGlobalSettingsDelete = 
  PayloadAction<TGlobalSettingsWithIdAndIndexPayload>;
