// Importing LoadingResult type from the current module
import { LoadingResult } from ".";
// Importing IUser interface from user.interface module
import { IManagerId, IManagerIdInterface, IManagerIdsResponse } from "../../interface/manager-id.interface";

// This is a simplified state management system, since we are dealing with only one ID
export type TManagerIdManagementState = {
  managerIds?: IManagerId[];

  selectedManagerId: TManagerIdDataAndIndexPayload;

  getManagerId?: GetManagerId;
};

//The new Get function for Remittance
export type GetManagerId = LoadingResult & {
  data?: IManagerIdsResponse;
};

//Different types of payloads are here and are self-explainatory.
export type TIManagerIdDataAndIndexPayload = {
  data: IManagerIdInterface;
  index: number;
};

export type TManagerIdDataAndIndexPayload = {
  data: TManagerIdPayload;
  index: number;
};

//For getting the response after loading
export type TIManagerIdResponse = LoadingResult & {
  data?: IManagerIdInterface;
};

//The payload that would be sent to the State Management
export type TManagerIdPayload = {
  id?: number;
  key?: string;
  value?: string;
  createdAt: string | null;
  updatedAt: string | null;
  deletedAt: string | null;
};
