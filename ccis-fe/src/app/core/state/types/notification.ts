import { INotification } from "@interface/notification.interface";
import { LoadingResult } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";

export type TNotificationState = {
  notifications: INotification[];
  getNotifications: LoadingResult;
  patchMarkAllAsReadNotifications: LoadingResult;
  patchReadNotification: LoadingResult;
};

export type TNotificationsPayload = {
  data: INotification;
};

export type TNotificationsIDPayload = {
  id?: number;
};

export type TNotificationsFilterPayload = {
  filter?: string;
};

export type TNotificationsPayloadAction = PayloadAction<TNotificationsPayload>;

export type TNotificationsPayloadActionPayload = PayloadAction<TNotificationsFilterPayload>;

export type TNotificationsActionPayload = PayloadAction<{
  data?: INotification[];
}>;

export type TNotificationsIDPayloadActionPayload = PayloadAction<TNotificationsIDPayload>;
