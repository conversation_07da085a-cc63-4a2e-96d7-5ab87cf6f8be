import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "react-toastify";
import Button from "@components/common/Button";
import Select from "@components/form/Select";
import { FaUserCircle } from "react-icons/fa";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useSignatoryTemplatesManagementActions } from "@state/reducer/utilities-signatory-template";
import { useSignatoryTypesManagementActions } from "@state/reducer/utilities-signatory-type";
import { useUserManagementActions } from "@state/reducer/users-management";
import { getButtonClassName } from "@constants/clsp";
import { ROUTES } from "@constants/routes";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { useContestabilityActions } from "@state/reducer/contestability";
import { IContestability } from "@interface/contestability.interface";
import { ProductCode } from "@enums/product-code";
import _ from "lodash";
import { readQuotationFromStorage } from "./Utils/quotation-storage";
import { Statuses } from "@constants/global-constant-value";

const CLPPAerSignatory = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const storedData = readQuotationFromStorage();
  const quotationData = storedData ?? {};
  const status = quotationData?.status || Statuses.DRAFT;
  const aerID = location.state?.aerID ?? quotationData?.id ?? location.state?.selectedQuotation?.id ?? null;

  // actions
  const { getSignatoryTemplate, getSignatoryTemplateID } = useSignatoryTemplatesManagementActions();
  const { getSignatoryType } = useSignatoryTypesManagementActions();
  const { getUsers } = useUserManagementActions();
  const { getCooperatives, getCooperativeById } = useCooperativesManagementActions();
  const { getContestability } = useContestabilityActions();

  // global state
  const cooperativesData = useSelector((state: RootState) => state.cooperatives?.cooperatives);
  const getCooperativeByIdState = useSelector((state: RootState) => state.cooperatives?.getCooperativeById);
  const contestabilityData = useSelector((state: RootState) => state.contestability?.contestabilities);
  const selectedTemplate = useSelector((state: RootState) => state.utilitiesSignatoryTemplate.selectedSignatoryTemplate);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const signatoryTypes = useSelector((state: RootState) => state.utilitiesSignatoryType?.signatoryTypes);
  const signatoryTemplates = useSelector((state: RootState) => state.utilitiesSignatoryTemplate.signatoryTemplates);

  // local state
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(() => {
    return location.state?.signatoryTemplateId || quotationData?.quotations?.signatoryTemplateId || null;
  });

  const coopId: number | undefined = Number(quotationData?.quotations?.coopId) || undefined;
  const [cooperativeName, setCooperativeName] = useState<string>("N/A");
  const fetchedCoopOnceRef = useRef(false);
  const prevTplIdRef = useRef<number | null>(null);

  useEffect(() => {
    if (!quotationData || !Object.keys(quotationData).length) {
      toast.error("Missing quotation data.");
      navigate(ROUTES.ACTUARY.CLPP.key);
      return;
    }
    getSignatoryTemplate({ filter: "" });
    getUsers({ filter: "" });
    getSignatoryType({ filter: "" });
    getCooperatives({ filter: "" });
    getContestability({ filter: ProductCode.CLPP });
  }, []);

  useEffect(() => {
    const stored = readQuotationFromStorage();
    const existingId = stored?.quotations?.signatoryTemplateId || quotationData?.quotations?.signatoryTemplateId;
    if (existingId && existingId !== selectedTemplateId) {
      setSelectedTemplateId(existingId);
    }
  }, []);

  useEffect(() => {
    if (!selectedTemplateId) return;
    if (prevTplIdRef.current === selectedTemplateId) return;
    prevTplIdRef.current = selectedTemplateId;
    getSignatoryTemplateID({ id: selectedTemplateId });
  }, [selectedTemplateId]);

  const signatories = useMemo(() => {
    const templateUsers = selectedTemplate?.data?.signatoriesTemplateUsers || [];
    return templateUsers
      .map((signatory: any) => {
        const user = users.find((u) => u.id === signatory.userId);
        return user ? { ...user, sequence: signatory.sequence, signatoryTypeId: signatory.signatoryTypeId } : null;
      })
      .filter(Boolean)
      .sort((a: any, b: any) => a.sequence - b.sequence);
  }, [selectedTemplate, users]);

  const groupedSignatories = useMemo(() => {
    const grouped: Record<number, any[]> = {};
    for (const sig of signatories) {
      if (!sig) continue;
      if (!grouped[sig.signatoryTypeId]) grouped[sig.signatoryTypeId] = [];
      grouped[sig.signatoryTypeId].push(sig);
    }
    return grouped;
  }, [signatories]);

  const templateOptions = [{ value: "", text: "Select Signatory Template" } as const].concat((signatoryTemplates || []).map((v: any) => ({ value: v?.id, text: v?.templateName })));

  useEffect(() => {
    if (!coopId) return;
    const fromList = cooperativesData?.find((c) => Number(c.id) === Number(coopId));
    if (fromList?.coopName) setCooperativeName(fromList.coopName);
  }, [coopId, cooperativesData]);

  useEffect(() => {
    if (!coopId) return;
    const inList = cooperativesData?.some((c) => Number(c.id) === Number(coopId));
    if (inList) return;
    if (fetchedCoopOnceRef.current) return;
    fetchedCoopOnceRef.current = true;
    getCooperativeById({ id: coopId });
  }, [coopId, cooperativesData, getCooperativeById]);

  useEffect(() => {
    if (getCooperativeByIdState?.success && getCooperativeByIdState?.data?.coopName) {
      setCooperativeName(getCooperativeByIdState.data.coopName);
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.data?.coopName]);

  const handleSignatoryTemplateChange = (e: any) => {
    const id = Number(e.target.value) || null;
    setSelectedTemplateId(id);

    const latestRaw = localStorage.getItem("quotationData");
    const latest = latestRaw ? JSON.parse(latestRaw) : {};
    const updated = {
      ...latest,
      quotations: {
        ...(latest.quotations || {}),
        signatoryTemplateId: id ?? undefined,
      },
    };

    localStorage.setItem("quotationData", JSON.stringify(updated));
  };

  return (
    <section className="px-6 py-4">
      {/* Back Button */}
      {/* <div className="mb-6">
        <Button
          classNames="bg-white border-0 flex items-center justify-center hover:bg-gray-100"
          outline
          variant="primary"
          onClick={() => {
            navigate(ROUTES.ACTUARY.createClppAER.key, {
              state: {
                signatoryTemplateId: selectedTemplateId,
                aerID,
              },
            });
          }}
        >
          <IoChevronBack />
          Back
        </Button>
      </div> */}

      <div className="text-lg font-medium mb-3 font-poppins-bold">CLPP WORKSHEET</div>

      {/* Quotation Summary */}
      <div className="grid grid-cols-4 gap-4 text-sm mb-10">
        <div className="text-zinc-400">COOPERATIVE</div>
        <div>{cooperativeName !== "N/A" ? cooperativeName : cooperativesData.length > 0 && coopId ? cooperativesData.find((coop) => coop.id === Number(coopId))?.coopName || "N/A" : "N/A"}</div>

        <div className="text-zinc-400">AVERAGE AGE</div>
        <div>{quotationData?.loanPortfolioYears?.[0]?.averageAge || "N/A"}</div>

        <div className="text-zinc-400">PREVIOUS PROVIDER</div>
        <div>{quotationData?.quotations?.previousProvider || "N/A"}</div>

        <div className="text-zinc-400">TOTAL NUMBER OF MEMBERS</div>
        <div>{quotationData?.quotations?.totalNumberOfMembers || "N/A"}</div>

        <div className="text-zinc-400">CONTESTABILITY</div>
        <div>{contestabilityData?.find((c: IContestability) => c.id === quotationData?.quotations?.contestability)?.label || "N/A"}</div>
      </div>

      <hr className="my-10 border-gray/10" />

      <div className="text-md font-medium mb-3">Signatories for AER Approval</div>

      <div className="text-primary mt-3 mb-2">Select Template</div>
      <Select name="signatoryTemplateId" options={templateOptions} value={selectedTemplateId ? selectedTemplateId.toString() : ""} onChange={handleSignatoryTemplateChange} required />

      {selectedTemplateId &&
        Object.entries(groupedSignatories).map(([typeId, group]) => {
          const type = signatoryTypes.find((t) => t.id === Number(typeId));
          return (
            <div key={typeId} className="mt-6">
              <h3 className="uppercase text-primary text-md font-semibold mb-2">{type?.signatoryTypeName || "Unnamed Type"}</h3>
              <div className="space-y-2">
                {group.map((signatory: any) => (
                  <div key={signatory.id} className="flex items-center w-full h-16 rounded-xl p-4 bg-gray-100">
                    <div className="px-4">
                      <FaUserCircle size={40} className="text-primary" />
                    </div>
                    <div className="flex flex-col w-[85%] items-start h-full justify-center">
                      <div className="text-lg font-bold flex gap-2 items-center text-primary h-full">
                        <span>{signatory.firstname}</span>
                        <span>{signatory.middlename}</span>
                        <span>{signatory.lastname}</span>
                      </div>
                      <div className="text-xs text-gray-500">{signatory.position?.positionName}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}

      <div className="mt-10 flex justify-end">
        <Button
          classNames={getButtonClassName(!selectedTemplateId)}
          onClick={() =>
            navigate(ROUTES.ACTUARY.reviewCLPP.key, {
              state: { signatoryTemplateId: selectedTemplateId, status, aerID },
            })
          }
          disabled={!selectedTemplateId}
        >
          <div className="flex flex-row items-center gap-2">
            <span className={`${!selectedTemplateId ? "text-gray/50" : "text-white"} text-[14px] font-[400] font-poppins-medium`}>Continue to Review</span>
          </div>
        </Button>
      </div>
    </section>
  );
};

export default CLPPAerSignatory;
