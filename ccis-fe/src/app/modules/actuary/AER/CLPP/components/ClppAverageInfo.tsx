import Input from "@modules/sales/components/input";
import colorMode from "@modules/sales/utility/color";
import { parseNumber } from "@modules/sales/utility/number";
import { useEffect, useState } from "react";

export type TClppMemberAndAverageInfo = {
  totalNumberOfMembers: number;
};

type TClppMemberAndAverageInfoProps = {
  value?: TClppMemberAndAverageInfo;
  onChange?: (value: TClppMemberAndAverageInfo) => void;
};

export const ClppMemberAndAverageInfo = ({ value, onChange }: TClppMemberAndAverageInfoProps) => {
  const [fieldState, setFieldState] = useState<TClppMemberAndAverageInfo>(
    value ?? {
      totalNumberOfMembers: 0,
    }
  );

  const handleChange = (key: keyof TClppMemberAndAverageInfo, value: number) => {
    const data = { ...fieldState, [key]: value };
    setFieldState(data);
    onChange?.(data);
  };

  useEffect(() => {
    if (value) {
      setFieldState(value);
    }
  }, [value]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Total number of members */}
      <div className="space-y-2">
        <label
          htmlFor="totalNumberOfMembers"
          className={colorMode({
            classLight: "text-[14px] font-poppins-medium font-[500]",
            classDark: "text-[14px] font-poppins-medium font-[500]",
          })}
        >
          Total Number of Members
        </label>
        <Input
          type="text"
          disabled
          name="totalNumberOfMembers"
          placeholder="Enter total number of members"
          className={colorMode({
            classLight: "!border-[#00000040] w-full",
            classDark: "!border-primary w-full",
          })}
          number={true}
          onChange={(e) => handleChange("totalNumberOfMembers", parseNumber(e.target.value))}
          value={fieldState.totalNumberOfMembers}
        />
      </div>
    </div>
  );
};
