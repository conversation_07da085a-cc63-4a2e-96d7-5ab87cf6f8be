import _ from "lodash";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import colorMode from "@modules/sales/utility/color";
import { PiMinusCircle, PiPlusCircle } from "react-icons/pi";
import { useCallback, useEffect, useState, useRef } from "react";
import Input from "@modules/sales/components/input";
import { parseNumber, romanize } from "@modules/sales/utility/number";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import Switch from "@components/switch";
import FormattedNumberInput from "@components/common/InputFormattedNumber";
import { toast } from "react-toastify";

export type TClppBenefitInfo = {
  ageFrom: number;
  ageTo: number;
  maximumCoverage: number;
  maximumTerm: number;
  nml: number;
  nel: number;
  rate: number;
};

type BenefitColumnReverse = {
  label: string;
  name: keyof TClppBenefitInfo;
  inputType: "textfield" | "select";
  hasCheckbox?: boolean;
  checkboxMessage?: string;
};

type BenefitRowReverse = {
  column: string;
  values: number[];
  checkbox?: (boolean | string)[];
};

type TClppBenefitInfoProps = {
  value?: TClppBenefitInfo[];
  onChange?: (benefits: TClppBenefitInfo[]) => void;
  hasRider?: boolean;
  handleRider: () => void;
};

const BENEFITSCOLUMNS: BenefitColumnReverse[] = [
  { label: "Age From", name: "ageFrom", inputType: "textfield" },
  { label: "Age To", name: "ageTo", inputType: "textfield" },
  { label: "Maximum Coverage", name: "maximumCoverage", inputType: "textfield" },
  { label: "Maximum Term", name: "maximumTerm", inputType: "textfield" },
  { label: "NML", name: "nml", inputType: "textfield" },
  { label: "NEL", name: "nel", inputType: "textfield" },
  { label: "NET RATE", name: "rate", inputType: "textfield" },
];

const getEntryLabel = (index: number) => `ENTRY ${romanize(index + 1).toUpperCase()}`;
const getEntryKey = (index: number) => `entry-value-${romanize(index + 1).toUpperCase()}`;

export const ClppBenefitInfo = ({ value, onChange, handleRider, hasRider }: TClppBenefitInfoProps) => {
  const { getProductBenefits } = useProductBenefitsManagementActions();

  const [dynamicBenefitRow, setDynamicBenefitRow] = useState<BenefitRowReverse[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const previousValueRef = useRef<string>("");
  const isUpdatingFromInternal = useRef(false);

  const onChangeRef = useRef(onChange);
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  const initializeDynamicBenefitColumn = useCallback(() => {
    const initialRows = BENEFITSCOLUMNS.map((item) => ({
      column: item.label,
      values: item.label === "Age From" ? [18] : [0],
      checkbox: item.name === "rate" ? [false] : undefined,
    }));
    setDynamicBenefitRow(initialRows);
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    const currentValueString = JSON.stringify(value || []);
    if (previousValueRef.current === currentValueString) return;
    previousValueRef.current = currentValueString;

    if (!value || value.length === 0) {
      if (!isInitialized) initializeDynamicBenefitColumn();
      return;
    }

    isUpdatingFromInternal.current = true;
    try {
      const mappedRows: BenefitRowReverse[] = BENEFITSCOLUMNS.map((column) => {
        const row: BenefitRowReverse = {
          column: column.label,
          values: [],
          checkbox: column.name === "rate" ? [] : undefined,
        };

        value.forEach((benefit) => {
          const numericValue = Number(benefit[column.name] ?? 0);
          row.values.push(numericValue);

          if (column.name === "rate" && row.checkbox) {
            const ageTo = Number(benefit.ageTo ?? 0);
            const rate = Number(benefit.rate ?? 0);
            const isGraduated = ageTo >= 66 && rate === 0;
            row.checkbox.push(isGraduated ? "GRADUATED" : false);
          }
        });

        return row;
      });

      setDynamicBenefitRow((prev) => (_.isEqual(prev, mappedRows) ? prev : mappedRows));
      setIsInitialized(true);
    } catch (error) {
      toast.error(`Error mapping benefit data: ${String(error)}`);
      if (!isInitialized) initializeDynamicBenefitColumn();
    } finally {
      queueMicrotask(() => {
        isUpdatingFromInternal.current = false;
      });
    }
  }, [value, isInitialized, initializeDynamicBenefitColumn]);

  useEffect(() => {
    getProductBenefits({ filter: "" });
  }, []);

  const createFlatBenefits = useCallback((): TClppBenefitInfo[] => {
    if (!dynamicBenefitRow.length || !dynamicBenefitRow[0]?.values?.length) return [];
    const n = dynamicBenefitRow[0].values.length;
    return Array.from({ length: n }, (_, i) => ({
      ageFrom: dynamicBenefitRow[0]?.values[i] ?? 0,
      ageTo: dynamicBenefitRow[1]?.values[i] ?? 0,
      maximumCoverage: dynamicBenefitRow[2]?.values[i] ?? 0,
      maximumTerm: dynamicBenefitRow[3]?.values[i] ?? 0,
      nml: dynamicBenefitRow[4]?.values[i] ?? 0,
      nel: dynamicBenefitRow[5]?.values[i] ?? 0,
      rate: dynamicBenefitRow[6]?.values[i] ?? 0,
    }));
  }, [dynamicBenefitRow]);

  useEffect(() => {
    if (isUpdatingFromInternal.current) return;
    const flat = createFlatBenefits();
    if (!_.isEqual(flat, value ?? [])) {
      onChangeRef.current?.(flat);
    }
  }, [createFlatBenefits, value]);

  const autoCheckGraduatedRate = useCallback(() => {
    const ageToRowIndex = 1;
    const rateRowIndex = 6;
    const targetIdx = 1;

    setDynamicBenefitRow((prev) => {
      const updated = [...prev];
      const ageToVals = updated[ageToRowIndex]?.values ?? [];
      if (ageToVals.length <= targetIdx) return updated;

      const secondEntryAgeTo = ageToVals[targetIdx];

      if (!updated[rateRowIndex].checkbox) updated[rateRowIndex].checkbox = [];
      while (updated[rateRowIndex].checkbox!.length <= targetIdx) {
        updated[rateRowIndex].checkbox!.push(false);
      }
      while (updated[rateRowIndex].values.length <= targetIdx) {
        updated[rateRowIndex].values.push(0);
      }

      const shouldBeGraduated = secondEntryAgeTo >= 66;
      updated[rateRowIndex].checkbox![targetIdx] = shouldBeGraduated ? "GRADUATED" : false;

      if (shouldBeGraduated) {
        updated[rateRowIndex].values[targetIdx] = 0;
      }

      return updated;
    });
  }, []);

  useEffect(() => {
    const firstEntryAgeTo = dynamicBenefitRow.find((row) => row.column === "Age To")?.values[0];
    if (firstEntryAgeTo && firstEntryAgeTo > 65) {
      toast.error("1st entry 'Age To' must not exceed 65.");
    }
  }, [dynamicBenefitRow]);

  const handleValueChange = useCallback(
    (rowIndex: number, columnIndex: number, newValue: number) => {
      setDynamicBenefitRow((prev) => {
        const updated = [...prev];

        if (updated[rowIndex].column === "NET RATE" && updated[rowIndex].checkbox?.[columnIndex] === "GRADUATED") {
          newValue = 0;
        }

        if (rowIndex === 1 && columnIndex === 0) {
          if (newValue > 65) {
            toast.error("First benefit 'Age To' must not exceed 65.");
            newValue = 65;
          }
        }

        updated[rowIndex].values[columnIndex] = newValue;

        if (updated[rowIndex].column === "Age To") {
          const ageFromRowIdx = updated.findIndex((r) => r.column === "Age From");
          const nextIdx = columnIndex + 1;
          const hasNext = updated[ageFromRowIdx]?.values?.length > nextIdx;
          if (hasNext) {
            updated[ageFromRowIdx].values[nextIdx] = Number.isFinite(newValue) ? newValue + 1 : 0;
          }
          if (columnIndex === 1) setTimeout(() => autoCheckGraduatedRate(), 0);
        }

        return updated;
      });
    },
    [autoCheckGraduatedRate]
  );

  const handleCheckboxChange = useCallback((rowIndex: number, columnIndex: number, isChecked: boolean) => {
    setDynamicBenefitRow((prev) => {
      const updated = [...prev];
      if (!updated[rowIndex].checkbox) updated[rowIndex].checkbox = [];
      updated[rowIndex].checkbox![columnIndex] = isChecked ? "GRADUATED" : false;

      if (isChecked) {
        updated[rowIndex].values[columnIndex] = 0;
      }

      return updated;
    });
  }, []);

  const createRenderFunction = useCallback(
    (columnIndex: number) => {
      return (_: any, rowIndex: number) => {
        const currentRows = dynamicBenefitRow;
        const row = currentRows[rowIndex];
        const value = row?.values[columnIndex] ?? 0;

        if (row?.column === "NET RATE") {
          const ageToRow = currentRows.find((r) => r.column === "Age To");
          const ageToValue = ageToRow?.values[columnIndex] ?? 0;
          const showCheckbox = ageToValue >= 66;
          const isGraduated = row.checkbox?.[columnIndex] === "GRADUATED";

          return (
            <div className="relative w-full flex items-center justify-center">
              <Input
                name="rate"
                className="text-center h-10"
                hasCheckbox={showCheckbox}
                infoMessage="Apply Graduated Rate."
                value={isGraduated ? 0 : value}
                checked={isGraduated}
                onCheckboxChange={(isChecked) => handleCheckboxChange(rowIndex, columnIndex, isChecked)}
                onChange={(e) => handleValueChange(rowIndex, columnIndex, parseNumber(e.target.value))}
                number
              />
              {isGraduated && <div className="font-poppins-semibold text-center absolute top-2">GRADUATED</div>}
            </div>
          );
        }

        return (
          <FormattedNumberInput
            name="benefit"
            className="input-field text-center w-full p-2 px-5 font-regular rounded-lg focus:!outline-2"
            value={value.toString()}
            disabled={row?.column === "Age From"}
            onChange={(e) => handleValueChange(rowIndex, columnIndex, parseNumber(e.target.value))}
            number
            dataList={row?.column === "Age From" ? ["18", "66", "71", "75"] : row?.column === "Age To" ? ["65", "70", "75", "80"] : undefined}
          />
        );
      };
    },
    [dynamicBenefitRow, handleValueChange, handleCheckboxChange]
  );

  const dynamicBenefitColumn = [
    {
      header: "COLUMN",
      key: "column",
      className: "!min-w-[200px] shrink-0 text-[14px] font-[500]",
      render: (data: any) => data.column,
    },
    ...Array.from({ length: dynamicBenefitRow[0]?.values?.length || 0 }, (_, index) => ({
      header: getEntryLabel(index),
      key: getEntryKey(index),
      action: () => (
        <Button classNames="!w-fit !h-fit !p-0" onClick={() => handleDeleteBenefit(index + 1)}>
          <PiMinusCircle className="inline text-white" />
        </Button>
      ),
      render: createRenderFunction(index),
    })),
  ];

  const handleDeleteBenefit = useCallback((entryIndex: number) => {
    const arrayIndex = entryIndex - 1;
    setDynamicBenefitRow((prev) =>
      prev.map((row) => ({
        ...row,
        values: row.values.filter((_, idx) => idx !== arrayIndex),
        checkbox: row.checkbox?.filter((_, idx) => idx !== arrayIndex),
      }))
    );
  }, []);

  const handleCreateBenefit = useCallback(() => {
    setDynamicBenefitRow((prev) => {
      const existingCount = prev[0]?.values?.length ?? 0;
      const ageToRow = prev.find((r) => r.column === "Age To");
      const prevAgeTo = existingCount > 0 ? Number(ageToRow?.values?.[existingCount - 1] ?? 0) : 0;
      const nextAgeFrom = existingCount === 0 ? 18 : Number.isFinite(prevAgeTo) && prevAgeTo > 0 ? prevAgeTo + 1 : 0;

      const updated = prev.map((row) => {
        if (row.column === "Age From") {
          return {
            ...row,
            values: [...row.values, nextAgeFrom],
          };
        }
        return {
          ...row,
          values: [...row.values, 0],
          checkbox: row.checkbox ? [...row.checkbox, false] : undefined,
        };
      });

      return updated;
    });
  }, []);

  return (
    <div
      className={colorMode({
        classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
        classDark: "shadow-sm bg-black/10 p-6",
      })}
    >
      <div className="flex justify-between items-center mb-3">
        <h3
          className={colorMode({
            classLight: "text-lg text-primary font-[600] text-[16px]",
            classDark: "text-lg text-white/60 font-[600] text-[16px]",
          })}
        >
          BENEFITS
        </h3>

        <div className="flex">
          <div className="flex items-center gap-2">
            <span className="text-sm">HAS RIDER</span>
            <Switch checked={hasRider ?? false} onChange={handleRider} />
          </div>

          <Button onClick={handleCreateBenefit}>
            <div className="flex flex-row items-center gap-2">
              <PiPlusCircle className="inline text-primary" />
              <span className="font-thin text-primary font-[300] text-sm">Add Age Bracket</span>
            </div>
          </Button>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <EditableTable className="border-b-0" columns={dynamicBenefitColumn} rows={dynamicBenefitRow} />
          <div
            className={colorMode({
              classLight: "bg-[#F6F5F5] p-4 border border-gray/20 border-t-0",
              classDark: "bg-black/10 p-4 border border-gray/20 border-t-0",
            })}
          >
            <div className="flex justify-between">
              <span
                className={colorMode({
                  className: "font-poppins-semibold text-[16px] font-[700]",
                  classLight: "text-gray",
                  classDark: "text-white/60",
                })}
              >
                TOTAL
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
