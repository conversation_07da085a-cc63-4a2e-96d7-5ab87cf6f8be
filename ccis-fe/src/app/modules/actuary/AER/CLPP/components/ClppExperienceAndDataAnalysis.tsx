import { FC, Fragment, useState } from 'react'
import { EXPERIENCE_DATA_ANALYSIS } from '@constants/product-proposal';
import Details from "@modules/actuary/AER/components/Details";
import { claimsColumns, columns7, experience_data_analysis_options } from '../constants';
import Button from '@components/common/Button';
import colorMode from '@modules/sales/utility/color';
import Table from "@modules/actuary/AER/components/Table";
import { data2, data7 } from '../../GYRT/Forms/data';

type Props = {
    handleDemographicModal?: () => void
    handleGenerateDemographicModal?: () => void;
}

const ClppExperienceAndDataAnalysis: FC<Props> = ({ handleDemographicModal, handleGenerateDemographicModal }) => {

    const [selectedDetail, setSelectedDetail] = useState<string>(EXPERIENCE_DATA_ANALYSIS.BASED_ON_MASTERLIST.key);

    const handleSelectDetail = (detail: string | null) => {
        setSelectedDetail(detail ?? "");
    };

    const handleGenerateDemographic = () => {
        handleGenerateDemographicModal && handleGenerateDemographicModal();
    }

    const handleShowDemographic = () => {
        handleDemographicModal && handleDemographicModal();
    };


    return (
        <Fragment>
            <div className={colorMode({
                classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
                classDark: "shadow-sm bg-black/10 p-6",
            })}>
                <div className="w-full min-h-80 h-96 gap-2 flex">
                    <div className="w-[30%] ">
                        <Details title={"Experience and Data Analysis"} details={experience_data_analysis_options} onDetailSelect={handleSelectDetail} selected={selectedDetail} />
                    </div>
                    <div className="w-[70%] h-full overflow-auto  border border-zinc-300 rounded-md p-4">
                        <div className=" flex flex-row justify-end gap-4">
                            <Button classNames="!bg-info text-white rounded-[5px]" variant="default" onClick={handleGenerateDemographic}>
                                <div className="flex flex-row items-center gap-2">
                                    <span
                                        className={colorMode({
                                            classLight: "text-white text-[14px] font-[400] font-poppins-medium",
                                            classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
                                        })}
                                    >
                                        Generate Demographic
                                    </span>
                                </div>
                            </Button>
                            {/* Demographic */}
                            <Button classNames="!bg-[#99999926] rounded-[5px]" variant="default" onClick={handleShowDemographic}>
                                <div className="flex flex-row items-center gap-2">
                                    <span
                                        className={colorMode({
                                            classLight: "text-gray/50 text-[14px] font-[400] font-poppins-medium",
                                            classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
                                        })}
                                    >
                                        Show Demographic
                                    </span>
                                </div>
                            </Button>
                        </div>
                        {selectedDetail !== EXPERIENCE_DATA_ANALYSIS.CLAIMS_DEATH_EXPERIENCE.key && (
                            <div>
                                <Table columns={claimsColumns} data={data2} headerClassNames="border-b border-zinc-300" />
                            </div>
                        )}

                        {selectedDetail === EXPERIENCE_DATA_ANALYSIS.CLAIMS_DEATH_EXPERIENCE.key && (
                            <div className="flex flex-col gap-10 mt-4">
                                {" "}
                                <div>
                                    {" "}
                                    <Table columns={columns7} data={data7} headerClassNames="border-b border-zinc-300" />{" "}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Fragment>
    )
}

export default ClppExperienceAndDataAnalysis;