import Button from "@components/common/Button";
import Tabs from "@components/common/Tabs";
import TextField from "@components/form/TextField";
import EditableTable from "@modules/sales/components/editable-table";
import colorMode from "@modules/sales/utility/color";
import { useEffect, useState, useMemo } from "react";
import { PiPlusCircle } from "react-icons/pi";
import { toast } from "react-toastify";

export type TClppLoanPortfolio = {
  loanPortfolioYears: TClppLoanPortfolioYear[];
  loanPortfolioAges: TClppLoanPortfolioAge[];
};

type TClppLoanPortfolioYear = {
  year: string;
  minimumAge: number;
  minimumLoanAmount: number;
  maximumAge: number;
  maximumLoanAmount: number;
  totalLoanPortfolio: number;
  numberOfBorrowers: number;
  averageAge: number;
};

type TClppLoanPortfolioAge = {
  ageFrom: number;
  ageTo: number;
  numberOfBorrowers: number;
  totalLoanPortfolio: number;
};

type TClppLoanPortfolioProps = {
  value: TClppLoanPortfolio;
  onChange: (value: TClppLoanPortfolio) => void;
  hasDemographics?: boolean;
  demographics?: any;
};

export const ClppLoanPortfolio = ({ value, onChange, hasDemographics = false, demographics }: TClppLoanPortfolioProps) => {
  const [fieldState, setFieldState] = useState<TClppLoanPortfolio>(
    value ?? {
      loanPortfolioYears: [],
      loanPortfolioAges: [],
    }
  );

  // inputs for demographics-based add
  const [ageFrom, setAgeFrom] = useState<string>("");
  const [ageTo, setAgeTo] = useState<string>("");

  const handleChange = (key: keyof TClppLoanPortfolio, val: any) => {
    const data = { ...fieldState, [key]: val };
    setFieldState(data);
    onChange?.(data);
  };

  // helpers to read demographics safely
  const agesFromDemo: any[] = useMemo(() => (Array.isArray(demographics?.ageAndYearCounts?.age) ? demographics.ageAndYearCounts.age : []), [demographics]);

  const toNum = (v: unknown): number => {
    if (typeof v === "number") return v;
    if (typeof v === "string") {
      const n = Number(v.replace(/,/g, "").trim());
      return Number.isFinite(n) ? n : 0;
    }
    return 0;
  };

  const getAgeVal = (row: any) => toNum(row?.age ?? row?.Age ?? row?.ageValue);
  const getCount = (row: any) => toNum(row?.count ?? row?.totalCount ?? row?.members ?? row?.totalMembers ?? row?.totalNumberOfMembers);
  const getCoverage = (row: any) => toNum(row?.coverage ?? row?.coverageAmount ?? row?.totalCoverageAmount ?? row?.amount ?? row?.total);

  // Map demographics within [ageFrom, ageTo] into one row
  const handleCreateAgeBaseOnDemographics = () => {
    if (!hasDemographics) return;

    const from = Number(ageFrom);
    const to = Number(ageTo);

    if (!Number.isFinite(from) || !Number.isFinite(to)) {
      toast.error("Please enter valid numbers for Age From and Age To.");
      return;
    }
    if (from <= 0 || to <= 0) {
      toast.error("Age values must be greater than 0.");
      return;
    }
    if (from > to) {
      toast.error("Age From must not be greater than Age To.");
      return;
    }

    if (!agesFromDemo.length) {
      toast.error("No demographics age data found to map.");
      return;
    }

    const inRange = agesFromDemo.filter((r) => {
      const a = getAgeVal(r);
      return a >= from && a <= to;
    });

    if (!inRange.length) {
      toast.error(`No records found in demographics for ages ${from}–${to}.`);
      return;
    }

    const numberOfBorrowers = inRange.reduce((s, r) => s + getCount(r), 0);
    const totalLoanPortfolio = inRange.reduce((s, r) => s + getCoverage(r), 0);

    const next = {
      ...fieldState,
      loanPortfolioAges: [
        ...fieldState.loanPortfolioAges,
        {
          ageFrom: from,
          ageTo: to,
          numberOfBorrowers,
          totalLoanPortfolio,
        },
      ],
    };

    setFieldState(next);
    onChange?.(next);

    setAgeFrom("");
    setAgeTo("");
  };

  useEffect(() => {
    if (value) setFieldState(value);
  }, [value]);

  return (
    <div
      className={colorMode({
        classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
        classDark: "shadow-sm bg-black/10 p-6",
      })}
    >
      <div className="flex justify-between items-center mb-3">
        <h3
          className={colorMode({
            classLight: "text-lg text-primary font-[600] text-[16px]",
            classDark: "text-lg text-white/60 font-[600] text-[16px]",
          })}
        >
          LOAN PORTFOLIO
        </h3>
      </div>

      <Tabs
        headers={[
          { label: "Year", key: "year" },
          { label: "Age", key: "age" },
        ]}
        contents={[
          // ===== YEAR TAB =====
          <div className="space-y-2">
            {/* <Button classNames="block ms-auto" onClick={handleCreateLoanPortfolioYear}>
              <div className="flex flex-row items-center gap-2">
                <PiPlusCircle className="inline text-primary" />
                <span className="font-thin text-primary font-[300] text-sm">Add Year</span>
              </div>
            </Button> */}

            <EditableTable
              columns={[
                { key: "year", header: "Year(s)", disabled: true },
                {
                  key: "totalLoanAmount",
                  header: "Minimum",
                  children: [
                    { key: "minimumAge", header: "Age", number: true, formatInput: true, disabled: true },
                    { key: "minimumLoanAmount", header: "Loan Amount", number: true, formatInput: true, disabled: true },
                  ],
                  disabled: true,
                },
                {
                  key: "maximum",
                  header: "Maximum",
                  children: [
                    { key: "maximumAge", header: "Age", number: true, formatInput: true, disabled: true },
                    { key: "maximumLoanAmount", header: "Loan Amount", number: true, formatInput: true, disabled: true },
                  ],
                  disabled: true,
                },
                {
                  key: "total",
                  header: "Total",
                  children: [
                    { key: "numberOfBorrowers", header: "NO. OF BORROWERS", number: true, formatInput: true, disabled: true },
                    { key: "totalLoanPortfolio", header: "LOAN PORTFOLIO", number: true, formatInput: true, disabled: true },
                  ],
                  disabled: true,
                },
                { key: "averageAge", header: "AVERAGE AGE", number: true, formatInput: true, disabled: true },
              ]}
              rows={fieldState.loanPortfolioYears}
              onChange={(updatedRows) => {
                handleChange("loanPortfolioYears", updatedRows);
              }}
            />
          </div>,

          // ===== AGE TAB =====
          <div className="space-y-2">
            {hasDemographics && (
              <div className="flex p-4">
                <div className="flex gap-4">
                  <div>
                    <label className="text-xs font-poppins-medium text-primary">Age From</label>
                    <TextField className="w-[150px] text-sm input-sm border-slate-200" placeholder="Age From" value={ageFrom} onChange={(e) => setAgeFrom(e.target.value)} />
                  </div>
                  <div>
                    <label className="text-xs font-poppins-medium text-primary">Age To</label>
                    <TextField className="w-[150px] text-sm input-sm border-slate-200" placeholder="Age To" value={ageTo} onChange={(e) => setAgeTo(e.target.value)} />
                  </div>
                </div>
                <div className="ml-4 py-1">
                  <Button classNames="mt-4" variant="primary" onClick={handleCreateAgeBaseOnDemographics}>
                    <div className="flex flex-row items-center gap-2">
                      <PiPlusCircle className="inline" />
                      <span className="font-thin font-[300] text-sm">Add</span>
                    </div>
                  </Button>
                </div>
              </div>
            )}

            <EditableTable
              columns={[
                {
                  key: "ageFrom",
                  header: "Age From",
                  number: true,
                  dataList: ["18", "66", "71", "75"],
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "ageTo",
                  header: "Age To",
                  number: true,
                  dataList: ["65", "70", "75", "80"],
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "numberOfBorrowers",
                  header: "NO. OF BORROWERS",
                  number: true,
                  formatInput: true,
                  disabled: true,
                },
                {
                  key: "totalLoanPortfolio",
                  header: "LOAN PORTFOLIO AMOUNT",
                  number: true,
                  formatInput: true,
                  disabled: true,
                },
              ]}
              rows={fieldState.loanPortfolioAges}
              onChange={(updatedRows) => {
                handleChange("loanPortfolioAges", updatedRows);
              }}
            />
          </div>,
        ]}
      />
    </div>
  );
};
