import { toast } from "react-toastify";
import Button from "@components/common/Button";
import WysiwygConditionEditor from "../../CLSP/components/wysiwyg/wysiwygConditions";

// -------------------- Types -------------------- //
export type TClppOptionsInfoProps = {
  show: boolean;
  canSubmit?: boolean;
  submitErrors?: string[];
  onContinueToSignatory?: () => void;
  quotationCondition: { condition: string };
  onSetShowConditionContainer: (visible: boolean) => void;
  showConditionContainer: boolean;
  onConditionChange: (content: string) => void;
};

// -------------------- Component -------------------- //
export default function ClppSavedOptionsPanel({
  show,
  quotationCondition,
  onConditionChange,
  onSetShowConditionContainer,
  showConditionContainer,
  onContinueToSignatory,
  canSubmit = false,
  submitErrors = [],
}: TClppOptionsInfoProps) {
  if (!show) return null;

  return (
    <div className="mt-6">
      <div className="flex justify-end">
        {!quotationCondition.condition && (
          <Button classNames="elevation-sm shadow-md rounded-[5px] !px-12 mt-8" variant="primary" onClick={() => onSetShowConditionContainer(true)}>
            <div className="flex flex-row items-center gap-2">Create Condition</div>
          </Button>
        )}
      </div>

      {(showConditionContainer || quotationCondition.condition) && (
        <div className="mb-3">
          <h2 className="text-[20px] font-poppins-medium font-[600] text-primary mt-4">CONDITIONS</h2>
          <WysiwygConditionEditor value={quotationCondition.condition} onChange={onConditionChange} className="border border-gray/20 rounded-md bg-white" />
        </div>
      )}

      <div className="flex justify-end">
        <Button
          classNames={`rounded-[5px] !px-12 ${canSubmit ? "!bg-primary hover:opacity-90" : "!bg-slate-400 cursor-not-allowed"}`}
          onClick={() => {
            if (!canSubmit) {
              const first = submitErrors[0] || "Please correct the form errors before proceeding.";
              toast.error(first);
              return;
            }
            onContinueToSignatory?.();
          }}
          disabled={!canSubmit}
        >
          <span className="text-white text-[14px] font-[400] font-poppins-medium">Continue to Signatory</span>
        </Button>
      </div>
    </div>
  );
}
