import { FC } from "react";
import colorMode from "@modules/sales/utility/color";
import Radio from "@components/form/Radio";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import Button from "@components/common/Button";
import { FaPlus } from "react-icons/fa6";

// type Props = {
//     value?: TClppCommissionDistribution;
//     onChange?: (value: TClppCommissionDistribution) => void;
// }

const ClppRateProjectionAndCommission: FC = () => {
    return (
        <div className={colorMode({
            classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
            classDark: "shadow-sm bg-black/10 p-6",
        })}>
            <div className="mb-3">
                <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">RATE PROJECTION AND COMMISSION ALLOCATION</h2>
                <p className="text-[12px] text-gray/50">Select the basis for the Calculation</p>
            </div>
            <div className="flex justify-between gap-4 text-xl mt-4">
                <div className="flex gap-2">
                    <Radio
                        key="1"
                        value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
                        label="Masterlist"
                        name="RateProjectionCommissionAllocation"
                        // checked={
                        //   formik.values.rateProjectionCommissionAllocation ===
                        //   RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key
                        // }
                        // onChange={handleRadio}
                        required
                    />
                    <Radio
                        key={"2"}
                        value={RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
                        label={"Coop Existing Product"}
                        name="RateProjectionCommissionAllocation"
                        // checked={
                        //   formik.values.rateProjectionCommissionAllocation ===
                        //   RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key
                        // }
                        // onChange={handleRadio}
                        required
                    />
                    <Radio
                        key={"3"}
                        value={RATE_PROJECT_COMMISSION_ALLOCATION.CLIMBS_MORTALITY_RATE.key}
                        label={"CLIMBS Mortality Rate"}
                        name="RateProjectionCommissionAllocation"
                        // checked={
                        //   formik.values.rateProjectionCommissionAllocation ===
                        //   RATE_PROJECT_COMMISSION_ALLOCATION.CLIMBS_MORTALITY_RATE.key
                        // }
                        // onChange={handleRadio}
                        required
                    />
                </div>
                <div>
                    <div className="flex justify-end">
                        <div className="flex justify-end gap-2">
                            <Button classNames="block ms-auto border border-primary" onClick={() => { }}>
                                <div className="flex flex-row items-center gap-2">
                                    <FaPlus className="inline text-primary" size={10} />
                                    <span className="font-thin text-primary font-[300] text-sm">Add</span>
                                </div>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex flex-col border border-gray/10 rounded-md">
                    <table className="w-full border-collapse border border-zinc-200 rounded">
                        <thead className="bg-primary text-white">
                            <tr>
                                <th className="border border-zinc-200 px-4 py-2 text-left">PROJECTION</th>
                                <th className="border border-zinc-200 px-4 py-2 text-left text-xs">Net Rate</th>
                                <th className="border border-zinc-200 px-4 py-2 text-left text-xs">Gross Rate</th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {[
                                { projection: "Total Premium", netRate: "", grossRate: "" },
                                { projection: "No. of Claims", netRate: "", grossRate: "" },
                                {
                                    projection: "Amount of Claims",
                                    netRate: "",
                                    grossRate: "",
                                },
                                { projection: "Claims Ratio", netRate: "", grossRate: "" },
                            ].map((row, index) => (
                                <tr key={index}>
                                    <td className="border border-zinc-200 px-4 py-2">{row.projection}</td>
                                    <td className="border border-zinc-200 px-4 py-2">
                                        <input type="number" className="w-full border border-zinc-200 rounded px-2 py-1" defaultValue={row.netRate} />
                                    </td>
                                    <td className="border border-zinc-200 px-4 py-2">
                                        <input type="number" className="w-full border border-zinc-200 rounded px-2 py-1" defaultValue={row.grossRate} />
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="flex flex-col border border-gray/10 rounded-md">
                    <table className="w-full border-collapse border border-zinc-200 rounded">
                        <thead className="bg-primary text-white">
                            <tr>
                                <th className="border border-zinc-200 px-4 py-2 text-left" colSpan={2}>
                                    RATING
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {/* {formik.values.benefits.slice(0, 1).map((benefit, index) => (
                                    <tr key={index}>
                                        <td className="border border-zinc-200 px-4 py-2">{benefitsSelectItems.find((benefit) => benefit.text.toLowerCase().trim() === "life insurance")?.text ?? "N/A"}</td>
                                        <td className="border border-zinc-200 px-4 py-2">
                                            <input
                                                type="number"
                                                className="w-full border border-zinc-200 rounded px-2 py-1"
                                                value={benefit.rate}
                                                onChange={(e) => {
                                                    const updatedBenefits = [...formik.values.benefits];
                                                    updatedBenefits[index].rate = parseFloat(e.target.value) || 0;
                                                    formik.setFieldValue("benefits", updatedBenefits);
                                                }}
                                            />
                                        </td>
                                    </tr>
                                ))} */}
                        </tbody>
                    </table>
                </div>

                {/* Commission Loading  */}
                <div className="flex flex-col border border-gray/10 rounded-md">
                    <div className="">
                        <div className="col-span-12">
                            {/* <EditableTable
                                    className="border-b-0"
                                    columns={[
                                        {
                                            key: "commissionType",
                                            header: "COMMISSION LOADING",
                                            className: "text-[14px] font-[500]",
                                            locked: formik.values.coopMode,
                                            render(data, index) {
                                                return (
                                                    <Select
                                                        placeholder="--- Select ---"
                                                        className="min-w-[100px]"
                                                        options={commisionTypesSelectItems}
                                                        onChange={(e) => {
                                                            const updatedCommissions = [...formik.values.commissionDistribution];
                                                            updatedCommissions[index].commissionType = parseInt(e);
                                                            formik.setFieldValue("commissionDistribution", updatedCommissions);
                                                        }}
                                                        value={data.commissionType ?? ""}
                                                        disabled={formik.values.coopMode}
                                                    />
                                                );
                                            },
                                        },
                                        {
                                            key: "rate",
                                            header: "",
                                            className: "text-[14px] font-[500]",
                                            number: true,
                                        },
                                        {
                                            key: "",
                                            header: "",
                                            align: "center",
                                            className: "text-[14px] font-[500] w-[50px]",
                                            render: (_, index) => (
                                                <Button
                                                    classNames="!w-fit !h-fit !p-0"
                                                    disabled={formik.values.coopMode}
                                                    onClick={() => {
                                                        if (formik.values.coopMode) return;
                                                        const updatedCommissions = [...formik.values.commissionDistribution];
                                                        updatedCommissions.splice(index, 1);
                                                        formik.setFieldValue("commissionDistribution", updatedCommissions);
                                                    }}
                                                >
                                                    <PiMinusCircle className="inline text-primary" />
                                                </Button>
                                            ),
                                        },
                                    ]}
                                    rows={formik.values.commissionDistribution}
                                    onChange={(updatedRows) => {
                                        formik.setFieldValue("commissionDistribution", updatedRows);
                                    }}
                                /> */}
                        </div>
                    </div>
                </div>
            </div>

        </div>
    )
}

export default ClppRateProjectionAndCommission