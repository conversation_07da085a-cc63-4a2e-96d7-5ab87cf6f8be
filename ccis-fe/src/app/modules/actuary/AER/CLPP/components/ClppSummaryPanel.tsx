import { useEffect } from "react";
import { toast } from "react-toastify";
import Accordion, { AccordionContent, AccordionItem, AccordionTrigger } from "@components/common/Accordion";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import { useContestabilityActions } from "@state/reducer/contestability";
import { ProductCode } from "@enums/product-code";
import WysiwygConditionEditor from "../../CLSP/components/wysiwyg/wysiwygConditions";
import { TClppBenefitInfo } from "./ClppBenefitInfo";
import { TClppCommissionDistribution } from "./CommissionDistribution";

// ---- types ----
export type TClppOptionProjection = {
  totalPremiumNetRate?: number | string;
  totalPremiumGrossRate?: number | string;
  numberOfClaims?: number | string;
  amountOfClaims?: number | string;
  claimsRatio?: number | string;
  udd?: number | string;
};

export type TSingleOption = {
  option: number; // always 1
  benefits: TClppBenefitInfo[];
  conditions: string;
  projection?: TClppOptionProjection;
  commissionDistribution?: TClppCommissionDistribution[];
};

export default function ClppSummaryPanel({
  show,
  option,
  onConditionChange,
  onSetShowConditionContainer,
  showConditionContainer,
  quotationCondition,
  onContinueToSignatory,
  canSubmit = false,
  submitErrors = [],
  resolveCommissionType,
  resolveCommissionAgeType,
  resolveBenefitName,
}: {
  show: boolean;
  option: TSingleOption;
  onConditionChange: (content: string) => void;
  onSetShowConditionContainer: (visible: boolean) => void;
  showConditionContainer: boolean;
  quotationCondition: { condition: string };
  onContinueToSignatory?: () => void;
  canSubmit?: boolean;
  submitErrors?: string[];
  resolveCommissionType?: (id?: number) => string | undefined;
  resolveCommissionAgeType?: (id?: number) => string | undefined;
  resolveBenefitName?: (benefitId?: number) => string | undefined;
}) {
  const { getContestability } = useContestabilityActions();
  useEffect(() => {
    getContestability({ filter: ProductCode.CLPP });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!show) return null;

  const benefitColumns = [
    { header: "AGE FROM", key: "ageFrom", align: "center", formatInput: true },
    { header: "AGE TO", key: "ageTo", align: "center", formatInput: true },
    { header: "BENEFIT", key: "benefitId", align: "center", render: (r: any) => <span className="text-[12px] font-[500]">{resolveBenefitName?.(r.benefitId) ?? r.benefitId}</span> },
    {
      header: "MAXIMUM COVERAGE",
      key: "maximumCoverage",
      align: "center",
      render: (r: any) => <span className="text-[12px] font-[500]">{r.maximumCoverage?.toLocaleString?.() ?? r.maximumCoverage}</span>,
    },
    { header: "MAXIMUM TERM", key: "maximumTerm", align: "center", render: (r: any) => <span className="text-[12px] font-[500]">{r.maximumTerm?.toLocaleString?.() ?? r.maximumTerm}</span> },
    { header: "NML", key: "nml", align: "center", render: (r: any) => <span className="text-[12px] font-[500]">{r.nml?.toLocaleString?.() ?? r.nml}</span> },
    { header: "NEL", key: "nel", align: "center", render: (r: any) => <span className="text-[12px] font-[500]">{r.nel?.toLocaleString?.() ?? r.nel}</span> },
    { header: "NET RATE", key: "rate", align: "center", render: (r: any) => <span className="text-[12px] font-[500]">{r.rate?.toLocaleString?.() ?? r.rate}</span> },
  ];

  const projectionColumns = [
    { header: "TOTAL PREMIUM (NET)", key: "totalPremiumNetRate", align: "center" },
    { header: "TOTAL PREMIUM (GROSS)", key: "totalPremiumGrossRate", align: "center" },
    { header: "# OF CLAIMS", key: "numberOfClaims", align: "center" },
    { header: "AMOUNT OF CLAIMS", key: "amountOfClaims", align: "center" },
    { header: "CLAIMS RATIO", key: "claimsRatio", align: "center" },
    { header: "MORTALITY RATE (UDD)", key: "udd", align: "center" },
  ];

  const commissionColumns = [
    {
      header: "COMMISSION TYPE",
      key: "commissionTypeId",
      align: "center",
      render: (r: any) => <span className="text-[12px] font-[500]">{resolveCommissionType?.(r.commissionTypeId) ?? r.commissionTypeId}</span>,
    },
    { header: "AGE TYPE", key: "ageTypeId", align: "center", render: (r: any) => <span className="text-[12px] font-[500]">{resolveCommissionAgeType?.(r.ageTypeId) ?? r.ageTypeId}</span> },
    { header: "AGE FROM", key: "ageFrom", align: "center" },
    { header: "AGE TO", key: "ageTo", align: "center" },
    { header: "RATE", key: "rate", align: "center" },
  ];

  return (
    <div className="mt-6">
      <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">QUOTATION SUMMARY</h2>

      <Accordion className="mt-4">
        <AccordionItem value="opt-1" className="border border-gray/10 rounded-md mb-2">
          <AccordionTrigger className="p-3 flex justify-between items-center">
            <div className="flex items-center gap-2">
              <span className="text-[12px] font-[500]">Option 1</span>
            </div>
            <span className="text-[11px] text-black/50">{option.benefits?.length ?? 0} benefits</span>
          </AccordionTrigger>

          <AccordionContent className="p-3 border-t border-gray/10 text-[10px] space-y-4">
            <div className="overflow-x-auto">
              <EditableTable columns={benefitColumns as any} rows={(option as any).benefits || []} editable={false} />
            </div>

            {option.projection && (
              <div>
                <h3 className="text-[12px] font-[600] mb-2">Projection</h3>
                <EditableTable columns={projectionColumns as any} rows={[option.projection] as any} editable={false} />
              </div>
            )}

            {(option.commissionDistribution?.length ?? 0) > 0 && (
              <div>
                <h3 className="text-[12px] font-[600] mb-2">Commission Distribution</h3>
                <EditableTable columns={commissionColumns as any} rows={(option.commissionDistribution as any) || []} editable={false} />
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="flex justify-end">
        {!quotationCondition.condition && (
          <Button classNames="elevation-sm shadow-md rounded-[5px] !px-12 mt-8" variant="primary" onClick={() => onSetShowConditionContainer(true)}>
            <div className="flex flex-row items-center gap-2">Create Condition</div>
          </Button>
        )}
      </div>

      {(showConditionContainer || quotationCondition.condition) && (
        <div className="mb-3">
          <h2 className="text-[20px] font-poppins-medium font-[600] text-primary mt-4">CONDITIONS</h2>
          <WysiwygConditionEditor value={quotationCondition.condition} onChange={onConditionChange} className="border border-gray/20 rounded-md bg-white" />
        </div>
      )}

      <div className="flex justify-end">
        <Button
          classNames={`rounded-[5px] !px-12 ${canSubmit ? "!bg-primary hover:opacity-90" : "!bg-slate-400 cursor-not-allowed"}`}
          onClick={() => {
            if (!canSubmit) {
              const first = submitErrors[0] || "Please correct the form errors before proceeding.";
              toast.error(first);
              return;
            }
            onContinueToSignatory?.();
          }}
          disabled={!canSubmit}>
          <span className="text-white text-[14px] font-[400] font-poppins-medium">Continue to Signatory</span>
        </Button>
      </div>
    </div>
  );
}
