import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import Button from "@components/common/Button";
import EditableTable from "@modules/sales/components/editable-table";
import Select, { TOptions } from "@modules/sales/components/select";
import Switch from "@components/switch";
import colorMode from "@modules/sales/utility/color";
import { PiMinusCircle, PiPlusCircle } from "react-icons/pi";
import { ISelectOptions } from "@interface/common.interface";
import { ICommissionType } from "@interface/commission-structure.interface";
import { IUtilitiesCommissionAgeTypes } from "@interface/utilities.interface";
import { RootState } from "@state/store";
import { useCommissionAgeTypeSManagementActions } from "@state/reducer/utilities-commission-age-types";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { COMMISION_AGE_TYPES, COMMISSION_TYPES, HIDDEN_COMMISSION_ID } from "@modules/sales/clpp-quotations/components/constants";

export type TClppCommissionDistribution = {
  commissionTypeId: number;
  ageTypeId: number;
  ageFrom?: number;
  ageTo?: number;
  rate: number;
};

type TClppCommissionDistributionInfoProps = {
  value?: TClppCommissionDistribution[];
  onChange?: (data: TClppCommissionDistribution[]) => void;
  quotationData?: any;
};

export const ClppCommissionDistributionInfo = ({ value, onChange }: TClppCommissionDistributionInfoProps) => {
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getCommissionAgeType } = useCommissionAgeTypeSManagementActions();

  const commissionAgeTypeData = useSelector((state: RootState) => state.utilitiesCommissionsAgeTypes.commissionAgeTypes);
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);

  const [coopMode, setCoopMode] = useState(false);
  const [commissionDistribution, setCommissionDistribution] = useState<TClppCommissionDistribution[]>(value ?? []);

  const mgmtFeeRateRef = useRef<number | null>(null);

  const commissionAgeTypesSelectItems = useMemo<ISelectOptions[]>(() => {
    return commissionAgeTypeData.map((item: IUtilitiesCommissionAgeTypes) => ({
      text: item.name,
      value: item.id.toString(),
    }));
  }, [commissionAgeTypeData]);

  const commissionTypesSelectItems = useMemo<TOptions[]>(() => {
    if (!commissionTypesData?.data) return [];
    return commissionTypesData.data
      .filter((item: ICommissionType) => !HIDDEN_COMMISSION_ID.includes(item.id))
      .map((item: ICommissionType) => ({
        text: item.commissionName,
        value: item.id.toString(),
      }));
  }, [commissionTypesData]);

  const totalDistribution = useMemo(() => {
    return commissionDistribution.reduce((acc, curr) => acc + Number(curr.rate), 0);
  }, [commissionDistribution]);

  const isStandardSelected = useCallback((selectedAgeTypeId: number) => {
    return selectedAgeTypeId == COMMISION_AGE_TYPES.STANDARD;
  }, []);

  const handleCreateCommissionDistribution = () => {
    const data = [...commissionDistribution];
    data.push({
      commissionTypeId: 0,
      ageTypeId: 0,
      ageFrom: 0,
      ageTo: 0,
      rate: 0,
    });
    setCommissionDistribution(data);
    onChange?.(data);
  };

  const normalizeRows = (rows?: Partial<TClppCommissionDistribution>[]): TClppCommissionDistribution[] => {
    if (!Array.isArray(rows)) return [];
    return rows.map((r) => ({
      commissionTypeId: Number(r.commissionTypeId ?? 0),
      ageTypeId: Number(r.ageTypeId ?? 0),
      ageFrom: r.ageFrom != null ? Number(r.ageFrom) : 0,
      ageTo: r.ageTo != null ? Number(r.ageTo) : 0,
      rate: r.rate != null ? Number(r.rate) : 0,
    }));
  };

  const isManagementFeeCommission = (commissionTypeId: number): boolean => {
    return commissionTypeId === COMMISSION_TYPES.COOP_COMMISSION;
  };

  // helpers to preserve/restore the user's Management Fee rate
  const captureMgmtFeeRate = (rows: TClppCommissionDistribution[]) => {
    const mf = rows.find((r) => isManagementFeeCommission(r.commissionTypeId));
    if (mf && typeof mf.rate === "number" && !Number.isNaN(mf.rate)) {
      mgmtFeeRateRef.current = mf.rate;
    }
  };

  const isSingleMgmtFeeStandard = (rows: TClppCommissionDistribution[], standardId: number) =>
    rows.length === 1 && isManagementFeeCommission(rows[0].commissionTypeId) && rows[0].ageTypeId === standardId;

  // Coop Mode is a UI flag only. No data mutations here.
  const handleSwitchCoopMode = useCallback(
    (checked: boolean) => {
      const standardId = Number(commissionAgeTypesSelectItems.find((i) => +i.value === COMMISION_AGE_TYPES.STANDARD)?.value ?? 0);

      setCoopMode((prevMode) => {
        if (checked) {
          const canEnable = commissionDistribution.length === 1 && isManagementFeeCommission(commissionDistribution[0].commissionTypeId) && commissionDistribution[0].ageTypeId === standardId;

          return canEnable ? true : prevMode; // ignore toggle if rule not met
        }
        return false;
      });
    },
    [commissionDistribution, commissionAgeTypesSelectItems]
  );

  // Fetch lists once
  useEffect(() => {
    getCommissionTypes();
    getCommissionAgeType({ filter: "" });
  }, []);

  useEffect(() => {
    if (!commissionTypesSelectItems.length || !commissionAgeTypesSelectItems.length) return;

    const normalizedRows = normalizeRows(value ?? []);
    setCommissionDistribution(normalizedRows);

    const standardId = Number(commissionAgeTypesSelectItems.find((i) => +i.value === COMMISION_AGE_TYPES.STANDARD)?.value ?? 0);
    const onlyOne = normalizedRows.length === 1;
    const isMgmtOnly = onlyOne && isManagementFeeCommission(normalizedRows[0]?.commissionTypeId);
    const isStandard = normalizedRows[0]?.ageTypeId === standardId;
    setCoopMode(isMgmtOnly && isStandard);

    captureMgmtFeeRate(normalizedRows);
  }, [value, commissionTypesSelectItems, commissionAgeTypesSelectItems]);

  return (
    <div
      className={colorMode({
        classLight: "border border-gray/10 bg-[#F6F6F680] p-6",
        classDark: "shadow-sm bg-black/10 p-6",
      })}
    >
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-4">
          <h3
            className={colorMode({
              classLight: "text-lg text-primary font-[600] text-[16px]",
              classDark: "text-lg text-white/60 font-[600] text-[16px]",
            })}
          >
            COMMISSION LOADING
          </h3>
        </div>
        <div className="flex flex-row flex-nowrap items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm">COOP MODE</span>
            <Switch checked={coopMode} onChange={(v: any) => handleSwitchCoopMode(typeof v === "boolean" ? v : !!v?.target?.checked)} />
          </div>
          {!coopMode && (
            <Button classNames="block ms-auto" onClick={handleCreateCommissionDistribution}>
              <div className="flex flex-row items-center gap-2">
                <PiPlusCircle className="inline text-primary" />
                <span className="font-thin text-primary font-[300] text-sm">Add Commission</span>
              </div>
            </Button>
          )}
        </div>
      </div>
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <EditableTable
            className="border-b-0"
            columns={[
              {
                key: "commissionType",
                header: "COMMISSION TYPE",
                className: "text-[14px] font-[500]",
                locked: coopMode,
                render(data, index) {
                  return (
                    <Select
                      placeholder="--- Select ---"
                      className="min-w=[250px]"
                      options={commissionTypesSelectItems}
                      disabled={coopMode}
                      onChange={(e) => {
                        const updatedCommissions = [...commissionDistribution];
                        updatedCommissions[index].commissionTypeId = Number(e);
                        captureMgmtFeeRate(updatedCommissions);
                        setCommissionDistribution(updatedCommissions);
                        onChange?.(updatedCommissions);
                      }}
                      value={data.commissionTypeId?.toString() ?? ""}
                    />
                  );
                },
              },
              {
                key: "ageType",
                header: "AGE TYPE",
                className: "text-[14px] font-[500]",
                render: (data, index) => (
                  <Select
                    placeholder="--- Select ---"
                    options={commissionAgeTypesSelectItems}
                    onChange={(e) => {
                      const updatedCommissions = [...commissionDistribution];
                      updatedCommissions[index].ageTypeId = Number(e);
                      if (isStandardSelected(Number(e))) {
                        updatedCommissions[index].ageFrom = "" as any;
                        updatedCommissions[index].ageTo = "" as any;
                      }
                      captureMgmtFeeRate(updatedCommissions);
                      setCommissionDistribution(updatedCommissions);
                      onChange?.(updatedCommissions);
                    }}
                    value={data.ageTypeId?.toString() ?? ""}
                  />
                ),
              },
              {
                key: "ageFrom",
                header: "AGE FROM",
                className: "text-[14px] font-[500]",
                number: true,
                formatInput: true,
                locked: (_, index) => isStandardSelected(commissionDistribution[index].ageTypeId),
              },
              {
                key: "ageTo",
                header: "AGE TO",
                className: "text-[14px] font-[500]",
                number: true,
                formatInput: true,
                locked: (_, index) => isStandardSelected(commissionDistribution[index].ageTypeId),
              },
              {
                key: "rate",
                header: "COMMISSION PERCENTAGE",
                className: "text-[14px] font-[500]",
                number: true,
              },
              {
                key: "",
                header: "",
                align: "center",
                className: "text-[14px] font-[500] w-[50px]",
                render: (_, index) => (
                  <Button
                    classNames="!w-fit !h-fit !p-0"
                    onClick={() => {
                      if (coopMode) return;
                      const updated = [...commissionDistribution];
                      updated.splice(index, 1);

                      const standardId = Number(commissionAgeTypesSelectItems.find((i) => +i.value === COMMISION_AGE_TYPES.STANDARD)?.value ?? 0);

                      if (isSingleMgmtFeeStandard(updated, standardId)) {
                        const current = Number(updated[0].rate ?? 0);
                        const snap = mgmtFeeRateRef.current;
                        if ((current === 0 || Number.isNaN(current)) && typeof snap === "number" && snap > 0) {
                          updated[0].rate = snap;
                        }
                      }

                      captureMgmtFeeRate(updated);
                      setCommissionDistribution(updated);
                      onChange?.(updated);
                    }}
                  >
                    <PiMinusCircle className="inline text-primary" />
                  </Button>
                ),
              },
            ]}
            rows={commissionDistribution}
            onChange={(updatedRows) => {
              setCommissionDistribution(updatedRows as TClppCommissionDistribution[]);
              onChange?.(updatedRows as TClppCommissionDistribution[]);
              captureMgmtFeeRate(updatedRows as TClppCommissionDistribution[]);
            }}
          />
          <div
            className={colorMode({
              classLight: "bg-[#F6F5F5] p-4 border border-gray/20 border-t-0",
              classDark: "bg-black/10 p-4 border border-gray/20 border-t-0",
            })}
          >
            <div className="flex justify-between">
              <span
                className={colorMode({
                  className: "font-poppins-semibold text-[16px] font-[700]",
                  classLight: " text-gray",
                  classDark: "text-white/60",
                })}
              >
                TOTAL
              </span>
              <span className="text-gray-300">{totalDistribution}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
