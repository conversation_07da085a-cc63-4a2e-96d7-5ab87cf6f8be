import { useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Button from "@components/common/Button";
import { toast } from "react-toastify";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@state/reducer";
import { putClppAER, getClppAER } from "@state/reducer/actuary-clpp-aer";
import { useCooperativesManagementActions } from "@state/reducer/cooperatives";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { ProductStatus } from "@enums/product-status";
import { ROUTES } from "@constants/routes";
import { useContestabilityActions } from "@state/reducer/contestability";
import { IContestability } from "@interface/contestability.interface";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import _ from "lodash";
import { readQuotationFromStorage, writeQuotationToStorage } from "../Utils/quotation-storage";
import { BENEFITSCOLUMNS } from "../../constants";
import { TClppBenefits } from "@state/types/actuary-clpp-aer";
import { stripAudit } from "../Utils/strip-autid";

const ClppReview = () => {
  const location = useLocation();
  const [quotationData, setQuotationData] = useState<any>(null);
  const aerID = location.state?.aerID ?? location.state?.selectedQuotation?.id ?? quotationData?.id ?? null;

  const { getCooperatives, getCooperativeById } = useCooperativesManagementActions();
  const cooperativesData = useSelector((state: RootState) => state.cooperatives?.cooperatives);
  const getCooperativeByIdState = useSelector((state: RootState) => state.cooperatives?.getCooperativeById);
  const { getContestability } = useContestabilityActions();
  const contestabilityData = useSelector((state: RootState) => state.contestability?.contestabilities);
  const { getCommissionTypes } = useCommissionTypeActions();
  const navigate = useNavigate();

  const dispatch = useDispatch();
  const signatoryTemplateId = location.state?.signatoryTemplateId;
  const coopId: number | undefined = Number(quotationData?.quotations?.coopId);
  const [cooperativeName, setCooperativeName] = useState<string>("N/A");
  const fetchedCoopOnceRef = useRef(false);
  const [pendingNavigation, setPendingNavigation] = useState<"submit" | "draft" | null>(null);
  const putCLppSuccess = useSelector((state: RootState) => state.clppAER?.putClppAER?.success);
  const putCLppLoading = useSelector((state: RootState) => state.clppAER?.putClppAER?.loading);

  const benefits = useMemo(() => {
    return quotationData?.quotation?.clppBenefits ?? quotationData?.options?.clppBenefits ?? quotationData?.quotations?.clppBenefits ?? [];
  }, [quotationData]);

  const pickProjection = (parsed: any) => parsed?.projection ?? parsed?.options?.projection ?? [];

  // Handle navigation after successful PUT operation
  useEffect(() => {
    if (putCLppSuccess && !putCLppLoading && pendingNavigation) {
      if (pendingNavigation === "draft") {
        dispatch(getClppAER({ params: { filter: "" } }));
      }
      navigate(ROUTES.ACTUARY.CLPP.key);
      setPendingNavigation(null);
    }
  }, [putCLppSuccess, putCLppLoading, pendingNavigation, navigate, dispatch]);

  const handleCreateAer = async () => {
    const parsed = readQuotationFromStorage() ?? quotationData;
    if (!parsed) {
      toast.error("Missing data");
      return;
    }
    const { quotations = {} } = parsed;

    const payload = _.merge({}, parsed, {
      status: ProductStatus.FOR_SIGNATORY,
      quotations: { ...quotations, signatoryTemplateId: signatoryTemplateId ?? quotations?.signatoryTemplateId },
      projection: pickProjection(parsed),
    });

    const sanitized = stripAudit({ ...payload, id: aerID as any });
    const isConfirmed = await confirmSaveOrEdit(`Do you want to submit the AER"?`);
    if (isConfirmed) {
      try {
        setPendingNavigation("submit");
        await dispatch(putClppAER(sanitized));
        toast.success("AER successfully created.");
      } catch {
        toast.error("Failed to create AER");
        setPendingNavigation(null);
      }
    }
  };

  const handleSaveDraft = async () => {
    const parsed = readQuotationFromStorage() ?? quotationData;
    if (!parsed) {
      toast.error("Missing data");
      return;
    }
    const { quotations = {} } = parsed;

    const draftPayload = _.merge({}, parsed, {
      status: ProductStatus.DRAFT,
      quotations: { ...quotations, signatoryTemplateId: signatoryTemplateId ?? quotations?.signatoryTemplateId },
      projection: pickProjection(parsed),
    });

    const sanitized = stripAudit({ ...draftPayload, id: aerID });
    const isConfirmed = await confirmSaveOrEdit(`Do you want to save the AER as draft?`);
    if (isConfirmed) {
      try {
        setPendingNavigation("draft");
        await dispatch(putClppAER(sanitized));
        toast.success("AER saved as draft.");
      } catch {
        toast.error("Failed to save draft");
        setPendingNavigation(null);
      }
    }
  };
  // Initialize from storage; merge template id like CLSP
  useEffect(() => {
    const fromStore = readQuotationFromStorage();
    const baseData = fromStore ?? null;

    if (!baseData) {
      toast.error("Missing data");
      navigate(ROUTES.ACTUARY.CLPP.key);
      return;
    }

    const updatedData = _.merge({}, baseData, {
      quotations: {
        signatoryTemplateId: location.state?.signatoryTemplateId ?? baseData?.quotations?.signatoryTemplateId,
      },
    });

    writeQuotationToStorage(updatedData);
    setQuotationData(updatedData);
  }, [location.state, navigate]);

  useEffect(() => {
    getCooperatives({ filter: "" });
    getContestability({ filter: "" });
    getCommissionTypes();
  }, []);

  // cooperative name resolution
  useEffect(() => {
    if (!coopId) return;
    const fromList = cooperativesData?.find((c) => Number(c.id) === Number(coopId));
    if (fromList?.coopName) setCooperativeName(fromList.coopName);
  }, [coopId, cooperativesData]);

  useEffect(() => {
    if (!coopId) return;
    const inList = cooperativesData?.some((c) => Number(c.id) === Number(coopId));
    if (inList) return;
    if (fetchedCoopOnceRef.current) return;
    fetchedCoopOnceRef.current = true;
    getCooperativeById({ id: coopId });
  }, [coopId, cooperativesData, getCooperativeById]);

  useEffect(() => {
    if (getCooperativeByIdState?.success && getCooperativeByIdState?.data?.coopName) {
      setCooperativeName(getCooperativeByIdState.data.coopName);
    }
  }, [getCooperativeByIdState?.success, getCooperativeByIdState?.data?.coopName]);

  return (
    <section className="px-6 py-4">
      {/* <Button
        variant="primary"
        outline
        classNames="bg-white border-0 flex items-center justify-center hover:bg-gray-100"
        onClick={() => {
          navigate(ROUTES.ACTUARY.createCLPPSignatory.key, {
            state: {
              signatoryTemplateId,
              status,
              aerID,
            },
          });
        }}
      >
        <IoChevronBack /> Back
      </Button> */}

      <h1 className="text-xl font-bold text-primary mb-4 mt-8">ACTUARY EVALUATION REPORT</h1>

      {/* General info */}
      <div>
        {aerID && (
          <div className="grid grid-cols-4 gap-4 text-sm mb-4">
            <div className="text-slate-400">AER No.</div>
            <div>{aerID}</div>
          </div>
        )}
        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Cooperative</div>
          <div>{cooperativeName}</div>
        </div>
        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Previous Provider</div>
          <div>{quotationData?.quotations?.previousProvider || "N/A"}</div>
        </div>
        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Total Members</div>
          <div>{quotationData?.quotations?.totalNumberOfMembers || "N/A"}</div>
        </div>
        <div className="grid grid-cols-4 gap-4 text-sm mb-4">
          <div className="text-slate-400">Contestability</div>
          <div>{contestabilityData?.find((c: IContestability) => c.id === quotationData?.quotations?.contestability)?.label}</div>
        </div>
      </div>

      <hr className="my-10 border-gray/10" />

      {/* Benefits */}
      <div className="w-full mt-16 border-b border-zinc-200 bg-zinc-100 rounded-md">
        <div className="p-4">
          <div className="text-xl font-poppins-semibold text-primary">BENEFITS</div>
        </div>
        <div className="overflow-x-auto p-4">
          <table className="table-auto bg-white w-full border-collapse border border-zinc-300">
            <thead className="bg-primary text-white">
              <tr>
                <th className="border border-zinc-300 px-4 py-2"></th>
                {benefits.map((_: any, index: number) => (
                  <th key={index} className="border border-zinc-300 px-4 py-2">{`ENTRY ${index + 1}`}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {BENEFITSCOLUMNS.map((column, rowIdx) => (
                <tr key={rowIdx}>
                  <td className="text-left border px-4 py-2 w-1/4">{column.label}</td>
                  {benefits.map((benefit: TClppBenefits, colIdx: number) => {
                    if (column.name === "ageGroup") {
                      return <td key={colIdx} className="text-center border px-4 py-2">{`${benefit.ageFrom} - ${benefit.ageTo}`}</td>;
                    }
                    const value = benefit[column.name as keyof TClppBenefits];
                    return (
                      <td key={colIdx} className="text-center border px-4 py-2">
                        {typeof value === "object" && value !== null ? JSON.stringify(value) : (value ?? "")}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Conditions */}
      <div className="pb-10 mt-16">
        <div className="my-4 text-xl font-poppins-semibold">CONDITIONS</div>
        <div className="border rounded border-slate-300 p-4 text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: quotationData?.quotationCondition?.condition || "" }} />
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-4">
        <Button classNames="bg-sky-500 text-white px-6 py-2 rounded-md hover:bg-primary-dark" onClick={handleSaveDraft}>
          Save as Draft
        </Button>
        <Button variant="primary" classNames="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark" onClick={handleCreateAer}>
          Submit AER
        </Button>
      </div>
    </section>
  );
};

export default ClppReview;
