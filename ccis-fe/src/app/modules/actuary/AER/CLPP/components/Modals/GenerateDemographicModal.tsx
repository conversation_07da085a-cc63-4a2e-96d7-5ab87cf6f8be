import LoadingButton from "@components/common/LoadingButton";
import Modal from "@components/common/Modal";
import Radio from "@components/form/Radio";
import TextField from "@components/form/TextField";
import { formatSelectOptions } from "@helpers/array";
import Select from "@modules/sales/components/select";
import colorMode from "@modules/sales/utility/color";
import { getProductNamesService } from "@services/products/products.service";
import { useFormik } from "formik";
import { FC, useEffect, useState } from "react";
import { toast } from "react-toastify";

type DemographicModalProps = {
    isOpen: boolean;
    handleModal?: () => void;
}

const GenerateDemographicModal: FC<DemographicModalProps> = ({ isOpen, handleModal }) => {


    const [mainProductsOptions, setMainProductsOptions] = useState<{ value: string; text: string }[]>([]);
    const [basis, setBasis] = useState<string>("remittance");
    const [ageBasis, setAgeBasis] = useState<string>("standard");
    const [fetchingProductNames, setFetchingProductNames] = useState<boolean>(false);

    const handleBasisChange = (value: string) => {
        setBasis(value);
    };

    const handleAgeBasisChange = (value: string) => {
        setAgeBasis(value);
    };

    const getMainProductNames = async () => {
        try {
            setFetchingProductNames(true);
            const result = await getProductNamesService();

            if (result.data) {
                const formattedNames = formatSelectOptions(result.data, "name");
                setMainProductsOptions(formattedNames);
            }
        } catch (error: any) {
            toast.error(error?.response?.data?.message);
        } finally {
            setFetchingProductNames(false);
        }
    }

    const formik = useFormik({
        initialValues: {
            productName: "",
            subProduct: "",
            basis: "remittance",
            remittancePeriod: "",
            claimsPeriod: "",
            dateFrom: "",
            dateTo: "",
            ageBasis: "standard",
        },
        onSubmit: (values) => {
            console.log("Form Values:", values);
        }
    })

    useEffect(() => {
        getMainProductNames();
    }, []) // eslint-disable-line react-hooks/exhaustive-deps
    console.log("Main Products Options:", mainProductsOptions);
    return (
        <Modal
            title="Select Basis"
            isOpen={isOpen}
            onClose={() => handleModal && handleModal()}
            modalContainerClassName="!max-w-4xl !rounded-2xl overflow-hidden"
            modalBgColor={colorMode({
                classLight: "!bg-gradient-to-b !from-[#FFFFFF] !to-[#E8EEFF] !via-[#FDFDFD] !rotate-160",
                classDark: "!bg-[#1D232A]",
            })}
            showCloseButton={true}
        >
            <div className="flex flex-col p-6 pb-0">
                <div className="w-full flex flex-col col-span-2 gap-4 gap-y-4">
                    <div className="flex flex-row w-full items-center">
                        <span className="flex flex-[1]">Product Name</span>
                        <div className="flex flex-[1.5] w-full">
                            <Select
                                name="productName"
                                options={mainProductsOptions}
                                onChange={formik.handleChange}
                                value={formik.values.productName}
                                error={!!formik.errors.productName && formik.touched.productName}
                                errorText={formik.errors.productName}
                                placeholder="Select Product"
                                disabled={fetchingProductNames}
                                allowSearch={true}
                            />
                        </div>
                    </div>
                    <div className="flex flex-row w-full items-center">
                        <span className="flex flex-[1]">Subproduct</span>
                        <div className="flex flex-[1.5] w-full">
                            <Select
                                options={[]}
                                onChange={() => { }}
                                placeholder="Select Sub-product"
                            />
                        </div>
                    </div>
                    <div className="flex flex-row w-full items-center">
                        <span className="flex flex-[1]">Based from</span>
                        <div className="flex flex-[1.5] flex-row w-full gap-x-20">
                            <Radio
                                label="Remittance"
                                value="remittance"
                                checked={basis === "remittance"}
                                onChange={() => handleBasisChange("remittance")}
                            />
                            <Radio
                                label="Claims"
                                value="claims"
                                checked={basis === "claims"}
                                onChange={() => handleBasisChange("claims")}
                            />
                        </div>
                    </div>
                    {basis === "remittance" && (
                        <div className="flex flex-row w-full items-center">
                            <span className="flex flex-[1]">Remittance Period</span>
                            <div className="flex flex-[1.5] flex-row w-full gap-x-20">
                                <Select
                                    options={[]}
                                    onChange={() => { }}
                                    placeholder="Select Period"
                                />
                            </div>
                        </div>
                    )}

                    {basis === "claims" && (
                        <div className="flex flex-row w-full items-center">
                            <span className="flex flex-[1]">Claims Period</span>
                            <div className="flex flex-[1.5] flex-row w-full gap-x-20">
                                <Select
                                    options={[]}
                                    onChange={() => { }}
                                    placeholder="Select Period"
                                />
                            </div>
                        </div>
                    )}

                    <div className="flex flex-row w-full items-center">
                        <span className="flex flex-[1]">Date Period</span>
                        <div className="flex flex-[1.5] flex-row w-full gap-x-4 items-center">
                            <TextField
                                type="date"
                                placeholder="From Date"
                                className="w-full"
                            />
                            <span className="mx-2 font-bold text-black dark:text-white">-</span>
                            <TextField
                                type="date"
                                placeholder="To Date"
                                className="w-full"
                            />
                        </div>
                    </div>

                    <div className="flex flex-row w-full items-start">
                        <span className="flex flex-[1]">Age Basis</span>
                        <div className="flex flex-col flex-[1.5] w-full gap-y-2">
                            <div className="flex flex-1 flex-row w-full gap-x-20">
                                <Radio
                                    label="Standard"
                                    value="standard"
                                    checked={ageBasis === "standard"}
                                    onChange={() => handleAgeBasisChange("standard")}
                                />
                                <Radio
                                    label="Customize"
                                    value="customize"
                                    checked={ageBasis === "customize"}
                                    onChange={() => handleAgeBasisChange("customize")}
                                    className="ml-4"
                                />
                            </div>
                            <div className="flex flex-[1.5] flex-row w-full gap-x-20 mt-2">
                                <table className="w-full">
                                    <thead className="bg-primary text-white">
                                        <tr>
                                            <th className="p-2 border-r-white border-r-2">AGE FROM</th>
                                            <th className="p-2">AGE TO</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="From Age"
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="From Age"
                                                />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="From Age"
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="To Age"
                                                />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="From Age"
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="To Age"
                                                />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="From Age"
                                                />
                                            </td>
                                            <td>
                                                <input
                                                    type="number"
                                                    className="input input-bordered rounded-none w-full"
                                                    placeholder="To Age"
                                                />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div className="divider mt-1"></div>
                    <div className="flex flex-row w-full items-center justify-end gap-x-2">
                        <LoadingButton
                            type="button"
                            className="!bg-zinc-400 !text-white !rounded-[5px] !w-44"
                            onClick={() => handleModal && handleModal()}
                            isLoading={false}
                            disabled={false}
                        >
                            Cancel
                        </LoadingButton>
                        <LoadingButton
                            type="button"
                            className="!bg-info !text-white !rounded-[5px] !w-44"
                            onClick={() => handleModal && handleModal()}
                            isLoading={false}
                            disabled={false}
                        >
                            Generate Data
                        </LoadingButton>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default GenerateDemographicModal;
