import { ChangeEvent, FC, Fragment, useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { IActions, IDefaultParams } from "@interface/common.interface";
import Table from "@components/common/Table";
import ActionDropdown from "@components/common/ActionDropdown";
import { formatDate } from "@helpers/date";
import Typography from "@components/common/Typography";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { GoVersions } from "react-icons/go";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import { useDebouncedCallback } from "use-debounce";
import { useClppAERActions } from "@state/reducer/actuary-clpp-aer";
import { TClppQuotationRequestsResponse } from "@state/types/actuary-clpp-aer";

const QuotationRequestTable: FC = () => {
  const navigate = useNavigate();

  const [searchText, setSearchText] = useState<string | undefined>("");
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [deleting, _setDeleting] = useState<boolean>(false);
  const loading = useSelector((state: RootState) => state.clppAER.getClppQuotationRequests?.loading);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const { getClppQuotationRequests } = useClppAERActions();
  const quotationRequests = useSelector((state: RootState) => state.clppAER.quotationRequests);
  const responseData = useSelector((state: RootState) => state.clppAER.getClppQuotationRequests);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const fetchQuotations = () => {
    const params = {
      page: page,
      pageSize: pageSize,
      filter: searchText,
      dateFrom: dateFrom,
      dateTo: dateTo,
    } as IDefaultParams;
    getClppQuotationRequests({ params });
  };

  useEffect(() => {
    const getQuotations = setTimeout(() => {
      fetchQuotations();
    }, 500);
    return () => clearTimeout(getQuotations);
  }, [searchText, page, pageSize, dateFrom, dateTo]);

  const getActionEvents = (_data: any): IActions<TClppQuotationRequestsResponse>[] => {
    const actions: IActions<TClppQuotationRequestsResponse>[] = [
      {
        name: "View",
        event: (data: TClppQuotationRequestsResponse) => {
          navigate(ROUTES.ACTUARY.viewQuotationRequestCLPP.key, { state: { data } });
        },
        icon: GoVersions,
        color: "primary",
      },
    ];

    return actions;
  };

  const columns: TableColumn<TClppQuotationRequestsResponse>[] = [
    {
      name: "Product Name",
      cell: (row) => row?.product?.name ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => row?.quotation?.cooperative?.coopName ?? "Not Set",
      ...commonSetting,
    },

    {
      name: "Date Requested",
      cell: (row) => formatDate(row?.createdAt, "d MMMM yyyy"),
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={`${getTextStatusColor(row.status)}`}>
          {capitalizeFirstLetterWords(row.status, "_")}
        </Typography>
      ),
    },

    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return <div className="flex flex-1 flex-row justify-center items-center gap-x-2">{!deleting && <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />}</div>;
      },
    },
  ];

  const sortedQR = quotationRequests?.slice().sort((a: TClppQuotationRequestsResponse, b: TClppQuotationRequestsResponse) => {
    return Number(b.id) - Number(a.id); // Explicitly convert to number
  });

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);

  const handleClearAll = () => {
    setSearchText("");
    setDateFrom("");
    setDateTo("");
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };

  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };

  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  return (
    <Fragment>
      <div className="p-4">
        <Typography className=" font-poppins-semibold text-xl mt-6 text-zinc-700">List of Quotation Request</Typography>
        <div className="flex flex-row justify-between  mt-4">
          <div className="flex flex-1 flex-row justify-start items-center gap-x-2">
            <Filter search={searchText} onChange={handleSearch}>
              <div className="flex justify-end">
                <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                  Clear All
                </button>
              </div>
              <div>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <div className="text-xs">Date From</div>
                    <TextField type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
                  </div>
                  <div>
                    <div className="text-xs">Date To</div>
                    <TextField type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
                  </div>
                </div>
              </div>
            </Filter>
          </div>
          <div className="w-full flex flex-1 items-center"></div>
        </div>
        <div className="flex mt-4">
          <Table
            className="h-[500px]"
            columns={columns}
            data={sortedQR}
            loading={loading}
            searchable={false}
            multiSelect={false}
            paginationServer={true}
            onPaginate={handlePaginate}
            onChangeRowsPerPage={handleRowsChange}
            paginationTotalRows={responseData?.data?.meta?.total}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default QuotationRequestTable;
