import _ from "lodash";

const STORAGE_KEY = "clpp_aer_working_draft";

// Merge that preserves existing rich arrays/objects when incoming is empty/missing
const preserveMerge = (existing: any, incoming: any) =>
  _.mergeWith({}, existing ?? {}, incoming ?? {}, (objVal, srcVal) => {
    // If both are arrays: keep existing when incoming is empty
    if (Array.isArray(objVal) && Array.isArray(srcVal)) {
      return srcVal.length === 0 ? objVal : srcVal;
    }
    // Keep existing when incoming is null/undefined
    if (srcVal === null || srcVal === undefined) return objVal;

    // For strings: keep existing if incoming is blank
    if (typeof srcVal === "string") return srcVal.trim() === "" ? objVal : srcVal;

    // For numbers: keep existing if incoming is NaN
    if (typeof srcVal === "number") return Number.isNaN(srcVal) ? objVal : srcVal;

    // For other objects, fall through to default merge
    return undefined;
  });

export const readQuotationFromStorage = () => {
  try {
    const raw = localStorage.getItem(STORAGE_KEY);
    return raw ? JSON.parse(raw) : null;
  } catch {
    return null;
  }
};

export const writeQuotationToStorage = (data: any) => {
  const existing = readQuotationFromStorage();
  const merged = preserveMerge(existing, data);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(merged));
  return merged;
};
