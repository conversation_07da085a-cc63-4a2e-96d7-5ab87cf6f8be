import React, { useEffect, useMemo, useState } from "react";
import { IoChevronBack } from "react-icons/io5";
import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { useLocation, useNavigate } from "react-router-dom";
import { showSuccess } from "@helpers/prompt";
import "react-quill/dist/quill.snow.css";
import "./styles/ViewQuotationRequest.css";
import dayjs from "dayjs";
import Loader from "@components/Loader";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { TClppBenefits, TClppClaimsExperiencAges, TClppClaimsExperienceYears, TClppCommissionDistribution, TClppLoanPortfolioYears } from "@state/types/actuary-clpp-aer";
import { BENEFITSCOLUMNS } from "../constants";
import { useClppAERActions } from "@state/reducer/actuary-clpp-aer";
import { IAttachmentResponse } from "@interface/products.interface";
import { getTextStatusColor } from "@helpers/text";
import { useContestabilityActions } from "@state/reducer/contestability";
import { Statuses } from "@constants/global-constant-value";

const ViewQuotationRequestForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [quotationData, setquotationData] = useState<any>({});
  const aerAttachments: IAttachmentResponse[] = useMemo(() => quotationData?.attachments ?? [], [quotationData?.attachments]);
  const { getClppAER, resetState } = useClppAERActions();
  const contestabilityData = useSelector((state: RootState) => state.contestability?.contestabilities);
  const updateStatus = useSelector((state: RootState) => state.clppAER.putClppAERStatus);
  const { getContestability } = useContestabilityActions();
  // const handleApprove = async () => {
  //   const isConfirmed = await confirmSaveOrEdit("Confirmation", "Do you confirm the approval of this AER?");
  //   if (isConfirmed) {
  //     putClppAERStatus({ id: quotationData?.id, data: { status: AERStatus.approved } });
  //   }
  // };

  let remarksArray: string[] = [];

  try {
    const raw = quotationData?.remarks;
    remarksArray = Array.isArray(raw) ? raw : typeof raw === "string" ? JSON.parse(raw) : [];
  } catch (e) {
    remarksArray = [];
  }

  const parsedRemarks = {
    checkboxes: [
      {
        label: "All in",
        checked: remarksArray.some((r) => r.toLowerCase().includes("all in".toLowerCase())),
      },
      {
        label: "Waived contestability",
        checked: remarksArray.some((r) => r.toLowerCase().includes("waived contestability".toLowerCase())),
      },
      {
        label: "5% of Members aged 66-69",
        checked: remarksArray.some((r) => r.toLowerCase().includes("5% of Members aged 66-69".toLowerCase())),
      },
    ],
    notes: remarksArray.filter((r) => !["all in", "waived contestability", "5% of members aged 66-69"].some((predef) => r.toLowerCase().includes(predef))),
  };
  useEffect(() => {
    if (updateStatus?.success) {
      showSuccess("Success", "Actuarial Evaluation Approved. The report has been successfully approved and is ready for the next steps.").then((result) => {
        if (result.isConfirmed) {
          resetState();
          navigate(ROUTES.ACTUARY.CLPP.key);
          getClppAER({
            params: {
              page: 1,
              pageSize: 10,
            },
          });
        }
      });
    }
  }, [updateStatus]);

  const handleCreateNewAER = () => {
    navigate(ROUTES.ACTUARY.createClppAER.key, { state: { quotationData, action: "customize" } });
  };

  useEffect(() => {
    setquotationData(location.state.data);
  }, []);

  useEffect(() => {
    getContestability({ filter: "" });
  }, []);

  return (
    <>
      <Button classNames="bg-white border-0 flex items-center justify-center" outline variant="primary" onClick={() => navigate(ROUTES.ACTUARY.CLPP.key)}>
        <IoChevronBack />
        Back
      </Button>

      {!quotationData && <Loader />}

      <div className="p-16 pl-20 w-full">
        <div className="text-2xl font-poppins-semibold  w-full text-primary uppercase">{quotationData?.product?.name}</div>
        <div className="flex ">
          <div className="w-full p-4 pl-0 flex flex-col gap-4 mt-4">
            <div className="flex items-center justify-start w-full">
              <div className="w-64 ml-2 text-zinc-400">AER NO.</div>
              <div>{quotationData?.id}</div>
            </div>
            <div className="flex items-center justify-start w-full">
              <div className="w-64 ml-2 text-zinc-400">DATE</div>
              <div>{dayjs(quotationData?.createdAt).format("MMMM DD, YYYY")}</div>
            </div>
            <div className="flex items-center justify-start w-full">
              <div className="w-64 ml-2 text-zinc-400">COOPERATIVE</div>
              <div>{quotationData?.quotation?.cooperative?.coopName}</div>
            </div>

            <div className="flex items-center justify-start w-full">
              <div className="w-64 ml-2 text-zinc-400">PREVIOUS PROVIDER</div>
              <div>{quotationData?.quotation?.previousProvider ?? "N/A"}</div>
            </div>
            <div className="flex items-center justify-start w-full">
              <div className="w-64 ml-2 text-zinc-400">TOTAL NUMBERS OF MEMBERS</div>
              <div>{quotationData?.quotation?.totalNumberOfMembers}</div>
            </div>
            <div className="flex items-center justify-start w-full">
              <div className="w-64 ml-2 text-zinc-400">CONTESTABILITY</div>
              <div>{contestabilityData?.find((c) => c.id === quotationData?.quotation?.contestability)?.label || "N/A"}</div>
            </div>
          </div>
          <div className=" p-5 w-full border-l border-slate-300 mb-2">
            <span className="text-xl text-center">STATUS</span>
            <div className="flex flex-row gap-3 justify-between items-center my-2">
              <div className="text-xs font-medium">Status</div>
              <span className="text-sm">
                <span className={`text-sm font-medium ${getTextStatusColor((quotationData as any)?.status)}`}>{(quotationData as any)?.status?.replace(/_/g, " ")}</span>
              </span>
            </div>
            <hr className="border-gray/20" />
            <span className="block text-xl mb-5">REMARKS</span>
            <div className="flex flex-col gap-3">
              {parsedRemarks.checkboxes.map((item, index) => (
                <span className="flex flex-row gap-2 items-center" key={index}>
                  <input type="checkbox" disabled size={12} checked={item.checked} />
                  <p className="text-sm">{item.label}</p>
                </span>
              ))}

              {parsedRemarks.notes.map((note: string, index: number) => (
                <span key={index} className="flex flex-row gap-2 items-center min-h-[50px] border border-zinc-300 bg-zinc-100 rounded p-2">
                  <p className="text-xs">{note}</p>
                </span>
              ))}
            </div>
            <div className="my-4">
              <div className="text-xs font-medium mb-1">Attachments</div>
              {aerAttachments.length > 0 ? (
                <ul className="space-y-1">
                  {aerAttachments.map((att) => (
                    <li key={att.id} className="flex justify-between items-center gap-2">
                      <a
                        className="text-xs underline text-accent truncate"
                        href={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${att.filepath}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        title={att.label || att.filename}
                      >
                        {att.label || att.filename}
                      </a>
                      <span className="text-[11px] text-gray-500 shrink-0">{typeof att.size === "number" ? (att.size / 1024).toFixed(1) : "0.0"} KB</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-xs text-gray-500">No attachment</div>
              )}
            </div>
          </div>
        </div>

        <div className="w-full mt-16 border-b border-zinc-200 bg-zinc-100 rounded-md">
          <div className="p-4">
            <div className="text-xl font-poppins-semibold text-primary">LOAN PORTFOLIO</div>
            <table className="w-full mt-4 table-fixed border-white border-spacing-1" border={1}>
              <thead className="bg-primary text-white border-b-0">
                <tr>
                  <th className="min-w-32 border-[1px] border-b-0" rowSpan={2}>
                    YEAR(S)
                  </th>
                  <th className="max-w-64 min-w-72 py-2 border-[1px] border-b-0" colSpan={2}>
                    MINIMUM
                  </th>
                  <th className="max-w-64 min-w-72 border-[1px] border-b-0" colSpan={2}>
                    MAXIMUM
                  </th>
                  <th className="max-w-64 min-w-72 border-[1px] border-b-0" colSpan={2}>
                    TOTAL
                  </th>
                </tr>
                <tr>
                  <th className="border-[1px] border-b-0">AGE</th>
                  <th className="border-[1px] border-b-0">LOAN AMOUNT</th>
                  <th className="border-[1px] border-b-0">AGE</th>
                  <th className="border-[1px] border-b-0">LOAD AMOUNT</th>
                  <th className="border-[1px] border-b-0">NO. OF BORROWERS</th>
                  <th className="border-[1px] border-b-0">LOAN PORTFOLIO</th>
                </tr>
              </thead>
              <tbody>
                {quotationData?.quotation?.clppLoanPortfolioYears?.map((item: TClppLoanPortfolioYears, idx: number) => (
                  <tr key={item.id || idx} className="border-t">
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.year}</td>
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.minimumAge}</td>
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.maximumAge}</td>
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.minimumLoanAmount}</td>
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.maximumLoanAmount}</td>
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.totalNumberOfBorrowers}</td>
                    <td className="text-center px-2 py-2 border border-zinc-300">{item.totalLoanPortfolio}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="w-full mt-16 border-b border-zinc-200 bg-zinc-100 rounded-md">
          <div className="p-4">
            <div className="text-xl font-poppins-semibold text-primary">CLAIMS / DEATH EXPERIENCE</div>
          </div>
          <div className="p-4 w-full gap-4">
            <div className="p-2">
              <table className="w-full table-fixed border-white border-spacing-1" border={1}>
                <thead className="bg-primary text-white">
                  <tr>
                    <th className="min-w-32 border-[1px] border-b-0">YEAR(S)</th>
                    <th className="max-w-64 min-w-72 py-2 border-[1px] border-b-0">NO. OF DEATHS</th>
                    <th className="max-w-64 min-w-72 border-[1px] border-b-0">TOTAL CLAIM AMOUNT</th>
                  </tr>
                </thead>
                <tbody>
                  {quotationData?.quotation?.quotationClaimsExperienceYear?.map((item: TClppClaimsExperienceYears, idx: number) => (
                    <tr key={idx} className="border-t">
                      <td className="text-center px-2 py-2 border border-zinc-300">{item.year}</td>
                      <td className="text-center px-2 py-2 border border-zinc-300">{item.numberOfDeaths}</td>
                      <td className="text-center px-2 py-2 border border-zinc-300">{item.totalClaimAmount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {quotationData?.quotation?.quotationClaimsExperienceAge?.length > 0 && (
              <div className="p-2">
                <table className="w-full table-fixed border-white border-spacing-1" border={1}>
                  <thead className="bg-primary text-white">
                    <tr>
                      <th className="min-w-32 border-[1px] border-b-0">AGE GROUP</th>
                      <th className="max-w-64 min-w-72 py-2 border-[1px] border-b-0">NO. OF DEATHS</th>
                      <th className="max-w-64 min-w-72 border-[1px] border-b-0">TOTAL CLAIM AMOUNT</th>
                    </tr>
                  </thead>
                  <tbody>
                    {quotationData?.quotation?.quotationClaimsExperienceAge?.map((item: TClppClaimsExperiencAges, idx: number) => (
                      <tr key={idx} className="border-t">
                        <td className="text-center px-2 py-2 border border-zinc-300">{`${item.ageFrom} - ${item.ageTo}`}</td>
                        <td className="text-center px-2 py-2 border border-zinc-300">{item.numberOfDeaths}</td>
                        <td className="text-center px-2 py-2 border border-zinc-300">{item.totalClaimAmount}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
        <div className="w-full mt-16 border-b border-zinc-200 bg-zinc-100 rounded-md">
          <div className="p-4">
            <div className="text-xl font-poppins-semibold text-primary">BENEFITS</div>
          </div>
          <div className="overflow-x-auto p-4">
            <table className="table-auto bg-white w-full border-collapse border border-zinc-300">
              <thead className="bg-primary text-white">
                <tr>
                  <th className="border border-zinc-300 px-4 py-2"></th>
                  {quotationData?.quotation?.clppBenefits?.map((_: any, index: number) => {
                    return (
                      <th key={index} className="border border-zinc-300 px-4 py-2">
                        {`ENTRY ${index + 1}`}
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="overflow-x-auto">
                {BENEFITSCOLUMNS.map((column, index) => {
                  return (
                    <tr key={index}>
                      <td className="text-left border-zinc-300 border-[1px] px-4 py-2 w-1/4">{column.label}</td>
                      {quotationData?.quotation?.clppBenefits?.map((benefit: TClppBenefits) => {
                        if (column.name === "ageGroup") {
                          return <td key={index} className="text-center border-zinc-300 border-[1px] px-4 py-2">{`${benefit.ageFrom} - ${benefit.ageTo}`}</td>;
                        }
                        if (column.name === "benefitId") {
                          return (
                            <td key={index} className="text-center border-zinc-300 border-[1px] px-4 py-2">
                              {benefit.benefit?.benefitName}
                            </td>
                          );
                        }
                        const value = benefit[column.name as keyof TClppBenefits];
                        return (
                          <td key={index} className="text-center border-zinc-300 border-[1px] px-4 py-2">
                            {typeof value === "object" && value !== null ? JSON.stringify(value) : (value ?? "")}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <div className="w-full mt-16 border-b border-zinc-200 bg-zinc-100 rounded-md">
          <div className=" p-4">
            <div className="text-xl font-poppins-semibold text-primary">COMMISSION DISTRIBUTION</div>
            <div>
              <table className="w-full table-fixed border-white border-spacing-1" border={1}>
                <thead className="bg-primary text-white">
                  <tr>
                    <th className="min-w-32 border-[1px] border-b-0">AGE BRACKET</th>
                    <th className="py-2 border-[1px] border-b-0">COMMISSION TYPE</th>
                    <th className="py-2 border-[1px] border-b-0">RATE</th>
                  </tr>
                </thead>
                <tbody>
                  {quotationData?.quotation?.quotationCommissionDistribution?.map((item: TClppCommissionDistribution, idx: number) => {
                    return (
                      <tr key={idx} className="border-t">
                        <td className="text-center px-2 py-2 border border-zinc-300">{item.ageFrom && item.ageTo ? `${item.ageFrom} - ${item.ageTo}` : "Standard"}</td>
                        <td className="text-center px-2 py-2 border border-zinc-300">{item.commissionType?.commissionName?.toUpperCase()}</td>
                        <td className="text-center px-2 py-2 border border-zinc-300">{parseFloat(item.rate.toString() ?? 0).toFixed(2)}%</td>
                      </tr>
                    );
                  })}
                  <tr>
                    <td className="px-2 py-2 border border-zinc-300" colSpan={2}>
                      TOTAL
                    </td>
                    <td className="text-center px-2 py-2 border border-zinc-300">
                      {quotationData?.quotation?.quotationCommissionDistribution?.reduce((sum: number, item: TClppCommissionDistribution) => sum + Number(item.rate), 0).toFixed(2)}%
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div className="pb-10 mt-16">
          <div className="my-4 text-xl font-poppins-semibold">CONDITIONS</div>
          <div
            className="border rounded border-slate-300 p-4 text-sm leading-relaxed"
            dangerouslySetInnerHTML={{
              __html: quotationData?.quotation?.quotationCondition?.condition || "",
            }}
          />
        </div>

        <div className="w-full flex justify-end gap-4">
          {![Statuses.FOR_SIGNATORY, Statuses.APPROVED].includes(quotationData?.status) &&
            (quotationData?.status === Statuses.DRAFT ? (
              <Button classNames="min-w-60" variant="primary" onClick={handleCreateNewAER}>
                Edit Quotation
              </Button>
            ) : (
              <Button classNames="min-w-60" variant="primary" onClick={handleCreateNewAER}>
                Evaluate Quotation
              </Button>
            ))}
        </div>
      </div>
    </>
  );
};

export default ViewQuotationRequestForm;
