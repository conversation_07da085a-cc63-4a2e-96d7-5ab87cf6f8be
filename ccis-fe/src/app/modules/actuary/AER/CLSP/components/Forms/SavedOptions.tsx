import React, { useEffect, useMemo } from "react";
import { PiCaretRight } from "react-icons/pi";
import EditableTable from "@modules/sales/components/editable-table";
import Accordion, { AccordionItem, AccordionTrigger, AccordionContent } from "@components/common/Accordion";
import CheckBox from "@components/form/CheckBox";
import Button from "@components/common/Button";
import WysiwygConditionEditor from "../wysiwyg/wysiwygConditions";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { ISelectOptions } from "@interface/common.interface";
import { ICommissionType } from "@interface/commission-structure.interface";
import { getCommissionTypes } from "@state/reducer/commision-type";
import { formatCurrency } from "@helpers/currency";

interface SavedOptionsProps {
  options: any[];
  selectedOptions: number[];
  quotationCondition: { condition: string };
  onSelectOption: (optionId: number) => void;
  onSetShowConditionContainer: (visible: boolean) => void;
  showConditionContainer: boolean;
  onConditionChange: (content: string) => void;
  onSubmitToSignatory: () => void;
}

const SavedOptions: React.FC<SavedOptionsProps> = ({
  options,
  selectedOptions,
  quotationCondition,
  onSelectOption,
  onSetShowConditionContainer,
  showConditionContainer,
  onConditionChange,
  onSubmitToSignatory,
}) => {
  const isSubmitDisabled = selectedOptions.length === 0 || !quotationCondition.condition;
  const commissionTypesData = useSelector((state: RootState) => state.commissionType.getCommissionTypes);
  const commisionTypesItems = useMemo<ISelectOptions[]>(() => {
    if (!commissionTypesData?.data) return [];
    return commissionTypesData.data.map((item: ICommissionType) => ({
      text: item.commissionName,
      value: item.id.toString(),
    }));
  }, [commissionTypesData]);

  useEffect(() => {
    getCommissionTypes();
  }, []);
  return (
    <>
      <hr className="my-4 border-gray/10" />
      <div className="mb-3">
        <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">SAVED OPTIONS</h2>

        <Accordion className="mt-4">
          {options.map((option, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="border border-gray/10 rounded-md mb-2 [&[data-state=open]_.caret]:rotate-90">
              <AccordionTrigger className="p-3 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <CheckBox
                    checked={selectedOptions.includes(option.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      onSelectOption(option.id);
                    }}
                  />
                  <span className="text-[12px] font-[500]">Option {option.id}</span>
                </div>
                <PiCaretRight className="text-[12px] transition-transform duration-200 caret" />
              </AccordionTrigger>
              <AccordionContent className="p-3 border-t border-gray/10 text-[10px]">
                <div className="flex flex-col gap-2">
                  <div className="overflow-x-auto">
                    <EditableTable
                      columns={[
                        {
                          key: "ageFrom",
                          header: "AGE FROM",
                          className: "text-[14px] font-[500]",
                          number: true,
                          disabled: true,
                        },
                        {
                          key: "ageTo",
                          header: "AGE TO",
                          className: "text-[14px] font-[500]",
                          number: true,
                          disabled: true,
                        },
                        {
                          key: "benefitId",
                          header: "BENEFITS",
                          className: "text-[14px] font-[500]",
                          locked: true,
                          render: () => (
                            <div className="min-w-[250px]">
                              <span>Life Insurance</span>
                            </div>
                          ),
                        },
                        {
                          key: "maximumCoverageAmount",
                          header: "MAXIMUM COVERAGE AMOUNT",
                          className: "text-[14px] font-[500]",
                          number: true,
                          formatInput: true,
                          disabled: true,
                        },
                        {
                          key: "rate",
                          header: "RATE",
                          className: "text-[14px] font-[500]",
                          number: true,
                          disabled: true,
                        },
                      ]}
                      rows={
                        option.options.map((opt: any) => ({
                          ageFrom: opt.ageFrom,
                          ageTo: opt.ageTo,
                          benefitId: opt.benefitId,
                          maximumCoverageAmount: parseFloat(opt.maximumCoverageAmount).toFixed(2),
                          rate: parseFloat(opt.rate).toFixed(2),
                        })) || []
                      }
                      editable={false}
                    />
                  </div>
                  <div>
                    <span className="text-[14px] font-[500]">Commission Distribution</span>
                    <div className="overflow-x-auto mt-2">
                      <EditableTable
                        columns={[
                          {
                            key: "commissionTypeId",
                            header: "Type",
                            className: "text-[14px] font-[500]",
                            render: (data) => {
                              const commissionType = commisionTypesItems.find((item) => item.value === data.commissionTypeId.toString());
                              return <span>{commissionType?.text || "N/A"}</span>;
                            },
                          },
                          {
                            key: "rate",
                            header: "Rate",
                            className: "text-[14px] font-[500]",
                            number: true,
                          },
                        ]}
                        rows={option.commissionDistribution || []}
                        editable={false}
                      />
                    </div>
                  </div>
                  <div>
                    <span className="text-[14px] font-[500]">Projection</span>
                    {/* inside SavedOptions, under "Projection" */}
                    <div className="overflow-x-auto mt-2">
                      <table className="w-full border-collapse border border-zinc-200 rounded text-xs">
                        <thead className="bg-primary text-white">
                          <tr>
                            <th className="border px-2 py-1 text-left">Total Net Premium</th>
                            <th className="border px-2 py-1 text-left">Total Gross Premium</th>
                            <th className="border px-2 py-1 text-left">Number Claims</th>
                            <th className="border px-2 py-1 text-left">Amount Claims</th>
                            <th className="border px-2 py-1 text-left">Claims Ratio</th>
                            {/* <th className="border px-2 py-1 text-left">UDD</th> */}
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            {/* <td className="border px-2 py-1">{option.projection?.totalPremiumNetRate ?? "—"}</td>
                            <td className="border px-2 py-1">{option.projection?.totalPremiumGrossRate ?? "—"}</td> */}
                            <td className="border px-2 py-1">{option.projection?.totalPremiumNetRate ? formatCurrency(option.projection.totalPremiumNetRate) : "—"}</td>
                            <td className="border px-2 py-1">{option.projection?.totalPremiumGrossRate ? formatCurrency(option.projection.totalPremiumGrossRate) : "—"}</td>
                            <td className="border px-2 py-1">{option.projection?.numberOfClaims ?? "—"}</td>
                            <td className="border px-2 py-1">{option.projection?.amountOfClaims ? formatCurrency(option.projection.amountOfClaims) : "—"}</td>
                            <td className="border px-2 py-1">{option.projection?.claimsRatio ?? "—"}</td>
                            {/* <td className="border px-2 py-1">{option.projection?.udd ?? "—"}</td> */}
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        {/* Create Condition */}
        <div className="flex justify-end">
          {!quotationCondition.condition && (
            <Button classNames="elevation-sm shadow-md rounded-[5px] !px-12 mt-8" variant="primary" onClick={() => onSetShowConditionContainer(true)}>
              <div className="flex flex-row items-center gap-2">Create Condition</div>
            </Button>
          )}
        </div>

        {/* WYSIWYG Editor */}
        {(showConditionContainer || quotationCondition.condition) && (
          <div className="mb-3">
            <h2 className="text-[20px] font-poppins-medium font-[600] text-primary mt-4">CONDITIONS</h2>
            <WysiwygConditionEditor value={quotationCondition.condition} onChange={onConditionChange} className="border border-gray/20 rounded-md bg-white" />
          </div>
        )}

        {/* Continue Button */}
        {quotationCondition.condition && (
          <div className="block text-end mt-3">
            <Button
              classNames={`${isSubmitDisabled ? "!bg-slate-400" : "!bg-info"} rounded-[5px] !px-12`}
              variant={isSubmitDisabled ? "default" : undefined}
              disabled={isSubmitDisabled}
              onClick={onSubmitToSignatory}
            >
              Continue to Signatory
            </Button>
          </div>
        )}
      </div>
    </>
  );
};

export default SavedOptions;
