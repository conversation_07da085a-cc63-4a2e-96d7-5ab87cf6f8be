import React, { useEffect } from "react";
import Modal from "@components/common/Modal";
import { FormikProvider, Form, useFormik } from "formik";
import TextField from "@components/form/TextField";
import Button from "@components/common/Button";
import Select1 from "@components/form/Combo-box";
import Radio from "@components/form/Radio";
import { formatSelectOptions } from "@helpers/array";
import Table from "../AER/components/Table";
import { TiPlus } from "react-icons/ti";
import { RemittancePeriod, BasedFrom } from "@enums/demographic-actuary";
import { useProductActions } from "@state/reducer/products";
import { toast } from "react-toastify";
import { showError } from "@helpers/prompt";

interface BasisModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: any;
  onUpdate?: (data: any) => void;
  onGenerate?: () => void;
  mainProducts: any[];
  subProducts: any[];
  products?: any[];
}

const claimsPeriodTypeOptions = [
  { periodTypeName: "Date of Death", id: RemittancePeriod.dateOfDeath },
  { periodTypeName: "Paid Date", id: RemittancePeriod.paidDate },
];

const remittancePeriodTypeOptions = [
  { periodTypeName: "Effectivity Date", id: RemittancePeriod.effectivityDate },
  { periodTypeName: "Official Receipt Date", id: RemittancePeriod.officialReceiptDate },
];

const BasisModal: React.FC<BasisModalProps> = ({ isOpen, onClose, data, onUpdate, onGenerate, products }) => {
  const { getSubProducts } = useProductActions();
  const formik = useFormik({
    initialValues: data,
    onSubmit: (values) => {
      if (formik.values.cooperativeId === 0) {
        toast.error("Please select a cooperative.");
        return;
      }

      if (values.age.length === 0) {
        toast.error("Please add at least one age bracket.");
        return;
      }

      onGenerate?.();
      onClose();
    },
  });

  useEffect(() => {
    onUpdate?.(formik.values);
  }, [formik.values]);

  useEffect(() => {
    if (formik.values.productId) {
      getSubProducts({ parentId: formik.values.productId });
    }
  }, [formik.values.productId]);

  const handleRadio = (key: string, value: string) => {
    formik.setFieldValue(key, value);
  };
  const columns = [
    { header: "AGE FROM", selector: (row: any) => row.ageFrom },
    { header: "AGE TO", selector: (row: any) => row.ageTo },
  ];

  const age = useFormik({
    initialValues: {
      ageFrom: "",
      ageTo: "",
    },
    onSubmit: () => {},
  });

  const handleAddAge = () => {
    const newAgeFrom = Number(age.values.ageFrom);
    const newAgeTo = Number(age.values.ageTo);

    // Validate input
    if (isNaN(newAgeFrom) || isNaN(newAgeTo) || newAgeFrom > newAgeTo) {
      showError("Validation Error", "Invalid age range.");
      return;
    }

    // Check for overlap
    const hasOverlap = formik.values.age.some((range: any) => newAgeFrom <= Number(range.ageTo) && newAgeTo >= Number(range.ageFrom));

    if (hasOverlap) {
      showError("Validation Error", "Age range overlaps with an existing range.");
      return;
    }

    const newAge = { ageFrom: newAgeFrom, ageTo: newAgeTo };
    formik.setFieldValue("age", [...formik.values.age, newAge]);
    age.resetForm();
  };

  const handleClickedRowBenefits = (rowIndex?: number) => {
    if (typeof rowIndex === "number") {
      const updatedAge = formik.values.age.filter((_: any, idx: number) => idx !== rowIndex);
      formik.setFieldValue("age", updatedAge);
    }
  };

  const productOptionsWithDefault = [
    { name: "Select Product", id: "" }, // Default option
    ...(products ?? []),
  ];
  return (
    <Modal title="Select Basis" modalContainerClassName="max-w-2xl" titleClass="font-poppins-semibold text-xl" isOpen={isOpen} onClose={onClose}>
      <FormikProvider value={formik}>
        <Form>
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className=" text-sm font-medium text-gray-700 w-1/3 flex  items-center">Product ID</div>
              {/* <Select1
                id="productId"
                name="productId"
                placeholder="Select Product"
                options={formatSelectOptions(mainProducts, "name")}
                className="mt-1  w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={formik.values.productId}
                onChange={formik.handleChange}
                required
              /> */}
              <Select1
                id="productId"
                name="productId"
                options={formatSelectOptions(productOptionsWithDefault, "name")}
                className="mt-1  w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={formik.values.productId}
                onChange={formik.handleChange}
                required
              />
            </div>

            {/* <div className="flex gap-2">
              <div className=" text-sm font-medium text-gray-700 w-1/3 flex  items-center">Sub Product ID</div>
              <Select1
                id="subProductId"
                name="subProductId"
                placeholder="Select Sub Product"
                options={formatSelectOptions(subProducts ? subProducts : [], "name")}
                className="mt-1  w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={formik.values.subProductId}
                onChange={formik.handleChange}
              />
            </div> */}

            <div className="flex gap-2">
              <div className=" text-sm font-medium text-gray-700 w-1/4 flex  items-center">Based From</div>
              <div className="flex gap-4 w-3/4 items-start justify-start">
                <Radio
                  key="1"
                  value={BasedFrom.remittance}
                  label="Remittance"
                  name="baseFrom"
                  checked={formik.values.baseFrom === BasedFrom.remittance}
                  onChange={() => handleRadio("baseFrom", BasedFrom.remittance)}
                  required
                />
                <Radio
                  key={"2"}
                  value={BasedFrom.claims}
                  label={"Claims"}
                  name="baseFrom"
                  checked={formik.values.baseFrom === BasedFrom.claims}
                  onChange={() => handleRadio("baseFrom", BasedFrom.claims)}
                  required
                />
              </div>
            </div>
            <div className="flex gap-2">
              <div className=" text-sm font-medium text-gray-700 w-1/3 flex  items-center">
                {formik.values?.baseFrom?.charAt(0).toUpperCase() + formik.values?.baseFrom?.slice(1).toLowerCase()} Period
              </div>
              <Select1
                id="periodType"
                name="periodType"
                options={formatSelectOptions(formik.values.baseFrom === BasedFrom.claims ? claimsPeriodTypeOptions : remittancePeriodTypeOptions, "periodTypeName")}
                className="mt-1  w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                value={formik.values.periodType}
                onChange={formik.handleChange}
              />
            </div>
            <div className="flex gap-2">
              <div className=" text-sm font-medium text-gray-700 w-1/4 flex  items-center">Date Period</div>
              <div className="flex gap-2 w-3/4">
                <div className="w-1/2">
                  <label className="text-xs text-zinc-500">From</label>
                  <TextField
                    id="periodFrom"
                    name="periodFrom"
                    type="date"
                    className="mt-1 w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={formik.values.periodFrom}
                    onChange={formik.handleChange}
                    required
                  />{" "}
                </div>

                <div className="w-1/2">
                  <label className="text-xs text-zinc-500">To</label>
                  <TextField
                    id="periodTo"
                    name="periodTo"
                    type="date"
                    className="mt-1  w-1/2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={formik.values.periodTo}
                    onChange={formik.handleChange}
                    required
                  />
                </div>
              </div>
            </div>
            {/* <div className="flex gap-2">
              <div className=" text-sm font-medium text-gray-700 w-1/4 flex  items-center">Age Basis</div>

              <div className="flex gap-4 w-3/4 items-start justify-start">
                <Radio
                  key="1"
                  value={AgeBasis.standard}
                  label="Standard"
                  name="ageBasis"
                  checked={formik.values.ageBasis === AgeBasis.standard}
                  onChange={() => handleRadio("ageBasis", AgeBasis.standard)}
                />
                <Radio
                  key="2"
                  value={AgeBasis.customize}
                  label={"Customize"}
                  name="ageBasis"
                  checked={formik.values.ageBasis === AgeBasis.customize}
                  onChange={() => handleRadio("ageBasis", AgeBasis.customize)}
                />
              </div>
            </div> */}

            <div className="flex">
              <div className="w-1/4">Age Bracket</div>
              <div className=" w-3/4">
                <div className="flex">
                  <div className="w-[86%]">
                    <Table onClickedRow={handleClickedRowBenefits} columns={columns} data={formik.values.age} tableMaxHeight="min-h-2" headerClassNames={"text-xs bg-primary text-white"} />
                  </div>
                  <div className="w-[14%]"></div>
                </div>
                <div className="flex gap-2 items-center">
                  <TextField
                    id="ageFrom"
                    name="ageFrom"
                    type="number"
                    size="sm"
                    placeholder="Enter Value"
                    value={age.values.ageFrom}
                    onChange={age.handleChange}
                    className="w-2/5 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  <TextField
                    id="ageTo"
                    name="ageTo"
                    type="number"
                    size="sm"
                    placeholder="Enter Value"
                    value={age.values.ageTo}
                    onChange={age.handleChange}
                    className="w-2/5 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  <Button
                    type="button"
                    isSubmitting={!age.values.ageFrom && !age.values.ageTo}
                    onClick={handleAddAge}
                    classNames={`text-xs text-center flex items-center justify-center w-1/5 bg-primary hover:bg-opacity-80 transition duration-200 text-white font-poppins-semibold ${
                      !age.values.ageFrom || !age.values.ageTo ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <TiPlus size={15} />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" classNames="text-sm bg-info hover:bg-opacity-80 transition duration-200 text-white font-poppins-semibold ">
                GENERATE
              </Button>
            </div>
          </div>
        </Form>
      </FormikProvider>
    </Modal>
  );
};

export default BasisModal;
