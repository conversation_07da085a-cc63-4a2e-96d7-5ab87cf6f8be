// components/inventory/CLIFSAInventoryTab.tsx
import React from "react";
import GenericInventoryTab from "@components/template/GenericInventoryTab";
import { RoleType } from "@enums/form-status";

const InventoryTab: React.FC = () => {
  return (
    <GenericInventoryTab 
      userRole={RoleType.ADMINOUTGOING}
      title="INVENTORY (ON-HAND)"
      description="This page lists all forms across all statuses. Use filters to narrow down results by status, division, or type."
    />
  );
};

export default InventoryTab;