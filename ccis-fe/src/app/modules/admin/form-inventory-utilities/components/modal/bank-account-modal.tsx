import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { TOption } from "@modules/gam/request-pads/TableFilter";
import { IBankAccountPayload } from "@state/types/form-inventory-utilities-bank-accounts";
import { FormikProps } from "formik";

type BankAccountModalProps = {
  isFormOpen: boolean;
  handleToggleFormModal: () => void;
  divisionOptions: TOption[];
  areaOptions: TOption[];
  bankOptions: TOption[];
  formik: FormikProps<IBankAccountPayload>;
};

const BankAccountModal = ({ isFormOpen, handleToggleFormModal, divisionOptions, areaOptions, bankOptions, formik }: BankAccountModalProps) => {
  return (
    <Modal title="Add new Bank Account" modalContainerClassName="max-w-3xl " titleClass="text-primary text-lg uppercase" isOpen={isFormOpen} onClose={handleToggleFormModal}>
      <form onSubmit={formik.handleSubmit} className="flex flex-col my-4 gap-4">
        <div>
          <label>Bank</label>
          <Select
            name="bankId"
            placeholder="Select Bank"
            options={bankOptions}
            error={formik.touched.bankId && !!formik.errors.bankId}
            errorText={formik.errors.bankId}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.bankId || 0}
            required
          />
        </div>
        <div>
          <label>Bank Account Name</label>
          <TextField
            name="bankAccountName"
            placeholder="Enter Bank Account Code"
            type="text"
            className="bg-white"
            error={formik.touched.bankAccountName && !!formik.errors.bankAccountName}
            errorText={formik.errors.bankAccountName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.bankAccountName}
            required
          />
        </div>
        <div>
          {" "}
          <label>Bank Account Number</label>
          <TextField
            name="bankAccountNumber"
            placeholder="Enter Bank Account Number"
            type="text"
            className="bg-white"
            error={formik.touched.bankAccountNumber && !!formik.errors.bankAccountNumber}
            errorText={formik.errors.bankAccountNumber}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.bankAccountNumber}
            required
          />
        </div>
        <div>
          <label>Description</label>
          <TextField
            name="description"
            placeholder="Enter Description"
            type="text"
            className="bg-white"
            error={formik.touched.description && !!formik.errors.description}
            errorText={formik.errors.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.description}
          />
        </div>
        <div>
          <label>Division</label>
          <Select
            name="divisionId"
            placeholder="Select Division"
            options={divisionOptions}
            error={formik.touched.divisionId && !!formik.errors.divisionId}
            errorText={formik.errors.divisionId}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.divisionId}
            required
          />
        </div>
        <div>
          <label>Area</label>
          <Select
            name="areaId"
            placeholder="Select Area"
            options={areaOptions}
            error={formik.touched.areaId && !!formik.errors.areaId}
            errorText={formik.errors.areaId}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.areaId}
            required
          />
        </div>
        <Button type="submit" variant="primary" classNames="btn rounded-xl">
          Save
        </Button>
      </form>
    </Modal>
  );
};

export default BankAccountModal;
