import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { TOption } from "@modules/gam/request-pads/TableFilter";
import { IBankPayload } from "@state/types/form-inventory-utilities-banks";
import { FormikProps } from "formik";

type BankModalProps = {
  isFormOpen: boolean;
  handleToggleFormModal: () => void;
  bankTypeOptions: TOption[];
  formik: FormikProps<IBankPayload>;
};

const BankModal = ({ isFormOpen, handleToggleFormModal, bankTypeOptions, formik }: BankModalProps) => {
  return (
    <Modal title="Add new Bank" modalContainerClassName="max-w-3xl " titleClass="text-primary text-lg uppercase" isOpen={isFormOpen} onClose={handleToggleFormModal}>
      <form onSubmit={formik.handleSubmit} className="flex flex-col my-4 gap-4">
        <div>
          <label>Bank Code</label>
          <TextField
            name="bankCode"
            placeholder="Enter Bank Code"
            type="text"
            className="bg-white"
            error={formik.touched.bankCode && !!formik.errors.bankCode}
            errorText={formik.errors.bankCode}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.bankCode}
            required
          />
        </div>
        <div>
          {" "}
          <label>Bank Name</label>
          <TextField
            name="bankName"
            placeholder="Enter Bank Name"
            type="text"
            className="bg-white"
            error={formik.touched.bankName && !!formik.errors.bankName}
            errorText={formik.errors.bankName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.bankName}
            required
          />
        </div>
        <div>
          <label>Bank Type</label>
          <Select
            name="bankTypeId"
            options={bankTypeOptions}
            value={formik.values.bankTypeId}
            onChange={formik.handleChange}
            error={formik.touched.bankTypeId && !!formik.errors.bankTypeId}
            errorText={formik.errors.bankTypeId}
            onBlur={formik.handleBlur}
            required
          />
        </div>
        <div>
          <label>Description</label>
          <TextField
            name="description"
            type="text"
            placeholder="Enter Description"
            className="bg-white"
            error={formik.touched.description && !!formik.errors.description}
            errorText={formik.errors.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.description}
            required
          />
        </div>

        <Button type="submit" variant="primary" classNames="btn rounded-xl">
          Save
        </Button>
      </form>
    </Modal>
  );
};

export default BankModal;
