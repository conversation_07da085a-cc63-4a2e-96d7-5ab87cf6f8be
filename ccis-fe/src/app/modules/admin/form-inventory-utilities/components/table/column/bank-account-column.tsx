import { TableColumn } from "react-data-table-component";
import { IActions } from "@interface/common.interface";

import { IBankAccount } from "@interface/form-inventory-utilities";
import ActionButtons from "@components/common/ActionButtons";

type GetActionEventsFn = (row: IBankAccount) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
};

export const getColumns = ({ getActionEvents }: GetColumnsParams): TableColumn<IBankAccount>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IBankAccount>[] = [
    {
      name: "Bank",
      selector: (row) => (row?.bank?.bankName ? row?.bank?.bankName : "N/A"),
      ...commonSetting,
    },
    {
      name: "Bank Account Name",
      selector: (row) => row.bankAccountName,
      ...commonSetting,
    },
    {
      name: "Bank Account Number",
      selector: (row) => row.bankAccountNumber,
      ...commonSetting,
    },
    {
      name: "Division",
      selector: (row) => (row?.division?.divisionName ? row?.division?.divisionName : "N/A"),
      ...commonSetting,
    },
    {
      name: "Area",
      selector: (row) => (row?.area?.areaName ? row?.area?.areaName : "N/A"),
      ...commonSetting,
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={getActionEvents(row)} />,
    });
  }

  return columns;
};
