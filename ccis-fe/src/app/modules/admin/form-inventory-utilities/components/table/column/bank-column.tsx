import { TableColumn } from "react-data-table-component";
import { IActions } from "@interface/common.interface";

import { IBank } from "@interface/form-inventory-utilities";
import ActionButtons from "@components/common/ActionButtons";

type GetActionEventsFn = (row: IBank) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
};

export const getColumns = ({ getActionEvents }: GetColumnsParams): TableColumn<IBank>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IBank>[] = [
    {
      name: "Bank Code",
      selector: (row) => row.bankCode,
      ...commonSetting,
    },
    {
      name: "Bank Name",
      selector: (row) => row.bankName,
      ...commonSetting,
    },
    {
      name: "Bank Type",
      selector: (row) => (row?.bankType?.bankTypeName ? row?.bankType?.bankTypeName : "N/A"),
      ...commonSetting,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={getActionEvents(row)} />,
    });
  }

  return columns;
};
