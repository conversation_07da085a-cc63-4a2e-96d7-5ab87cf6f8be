// For specific React components 
import React, { Fragment, useEffect, useState } from "react";
// Selector for Redux hooks to access state
import { useSelector } from "react-redux";
// Table Components, specifically the column
import { TableColumn } from "react-data-table-component";
// Other GUI elements and functions
import ActionButtons from "@components/common/ActionButtons";
import Table from "@components/common/Table";
import { canPerformOperation } from "@helpers/permissions";
import { confirmDelete, confirmSaveOrEdit } from "@helpers/prompt";
// Interface Actions
import { IActions } from "@interface/common.interface";
// Permissions and Roles
import { PermissionType, UserRoles } from "@interface/routes.interface";
import { IUserRPermission } from "@interface/user.interface";

// Interface for the Remittance Type
import { IUtilitiesRemittanceType } from "@interface/utilities.interface";
// Reducer for connecting the service to the Saga
import { useRemittanceTypeManagementActions } from "@state/reducer/utilities-remittance-type";

// Other misc. imports
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";

// Import the form that would be used for the table
import CreateUpdateRemittanceTypeForm from "../Forms/CreateUpdateRemittanceTypeForm";

type Props={isOpen:boolean;onClose:()=>void;onExport:(p:{departmentId:number|null;status:string;dateFrom:string;dateTo:string;fileType:'pdf'|'csv'})=>void;departments:{id:number;name:string}[]};
const ExportReportModal=({isOpen,onClose,onExport,departments}:Props)=>!isOpen?null:(
  <div className="fixed inset-0 z-50 grid place-items-center bg-black/30">
    <div className="w-full max-w-md rounded bg-white p-4 shadow-lg space-y-3">
      <h3 className="text-lg font-semibold">Generate Reports</h3>
      <form className="grid gap-3"><select className="select select-bordered">{departments.map(d=><option key={d.id} value={d.id}>{d.name}</option>)}</select><select className="select select-bordered"><option>All</option><option>Resolved</option><option>Unresolved</option></select><div className="grid grid-cols-2 gap-2"><input type="date" className="input input-bordered"/><input type="date" className="input input-bordered"/></div><select className="select select-bordered"><option value="pdf">PDF</option><option value="csv">CSV</option></select></form>
      <div className="flex justify-end gap-2"><button className="btn" onClick={onClose}>Cancel</button><button className="btn btn-primary" onClick={()=>onExport({departmentId:null,status:'All',dateFrom:'',dateTo:'',fileType:'pdf'})}>Export</button></div>
    </div>
  </div>);

// Setting up the table
const RemittanceTypeTable: React.FC = () => {
  //Setting yp the settings for the table, as well as variables
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<IUtilitiesRemittanceType>(
    {} as IUtilitiesRemittanceType
  );
  const [searchText, setSearchText] = useState<string>("");

  //For the Remittance type and the loading service
  //First line is important to check for the actual data for remittance types.
  const remittanceTypes = useSelector(
    (state: RootState) => state?.utilitiesRemittanceTypes?.remittanceTypes
  );
  const loading = useSelector(
    (state: RootState) =>
      state.utilitiesRemittanceTypes?.getRemittanceType?.loading
  );

  //For user permissions to view this table, and/or modifying it
  const user: IUserRPermission = useSelector((state: RootState) => {
    const userData = state?.auth?.user?.data;
    const roles = userData?.roles ?? [];
    return {
      ...userData,
      roles,
    } as IUserRPermission; // Type assertion to match IUserRPermission
  });

  //For the actions that would be used for the table
  const {
    getRemittanceType,
    setSelectedRemittanceType,
    postRemittanceType,
    putRemittanceType,
    destroyRemittanceType,
  } = useRemittanceTypeManagementActions();
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  //For the Edit button
  const actionEvents: IActions<IUtilitiesRemittanceType>[] = [
    {
      name: "Edit",
      event: (row: IUtilitiesRemittanceType, index: number) => {
        const data = {
          id: row.id,
          remittanceTypeCode: row.remittanceTypeCode,
          remittanceTypeName: row.remittanceTypeName,
          description: row.description,
        };
        setSelectedRemittanceType({ data: data, index: index });
        setInitialValues(data);
        setIsEditMode(true);
        setModalOpen(true);
      },
      icon: CiEdit,
      color: "primary",
      disabled: false,
    },
    //For the Delete button
    {
      name: "Delete",
      event: (row: IUtilitiesRemittanceType, index: number) => {
        const action = confirmDelete(row.remittanceTypeName);
        action.then((value) => {
          if (value.isConfirmed) {
            destroyRemittanceType({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
      //Below are the codes for disabling deletes except for specific users
      //Specifically, the superadmin
      //disabled: true,
      disabled: !canPerformOperation(
        user,
        PermissionType.COOPERATIVES_TYPE_DELETE,
        [UserRoles.admin]
      ),
    },
  ];
  //For the columns that would be used for the table
  const columns: TableColumn<IUtilitiesRemittanceType>[] = [
    {
      name: "Remittance Type Code",
      selector: (row) => row.remittanceTypeCode,
      ...commonSetting,
    },
    {
      name: "Remittance Type Name",
      cell: (row) => row.remittanceTypeName,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => (
        <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />
      ),
    },
  ];

  //This is for the create modal, 
  // mainly to initialize the values 
  const handleToggleModal = () => {
    setModalOpen((prev) => !prev);
    setIsEditMode(false);
    setInitialValues({
      id: 0,
      remittanceTypeCode: "",
      remittanceTypeName: "",
      description: "",
    });
  };

  const handleSubmit = async (values: IUtilitiesRemittanceType) => {
    if (isEditMode) {
      const isConfirmed = await confirmSaveOrEdit(
        `Do you want to edit the Remittance Type Name: "${values.remittanceTypeName} ?"`
      );
      if (isConfirmed) {
        putRemittanceType(values);
      }
    } else {
      const isConfirmed = await confirmSaveOrEdit(
        `Do you want to save the Remittance Type Name: "${values.remittanceTypeName} ?"`
      );
      if (isConfirmed) {
        postRemittanceType(values);
      }
    }
  };

  //For filtering stuff when searching, 
  // this would be attached to the search text
  useEffect(() => {
    getRemittanceType({
      params: {
        filter: searchText,
      }
    });
  }, [searchText]);

  // Actual HTML output
  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">
        Remittance Types
      </div>
      <Table
        className="h-[400px] "
        columns={columns}
        data={remittanceTypes}
        createLabel="Create Remittance Type"
        onCreate={handleToggleModal}
        loading={loading}
        disabled={false}
        onSearch={setSearchText}
        multiSelect={false}
        searchable
      />
      <CreateUpdateRemittanceTypeForm
        isOpen={modalOpen}
        isEditMode={isEditMode}
        initialValues={initialValues}
        onClose={handleToggleModal}
        onSubmits={handleSubmit}
      />
    </Fragment>
  );
};

// for exporting the table element to other modules
export default RemittanceTypeTable;
