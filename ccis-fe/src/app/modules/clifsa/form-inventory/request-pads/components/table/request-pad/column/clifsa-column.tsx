import { TableColumn } from "react-data-table-component";
import { IGamPadRequest } from "@interface/gam-request-pads";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";

type GetActionEventsFn = (row: IGamPadRequest) => IActions<any>[];

export const getColumns = (getActionEvents?: GetActionEventsFn): TableColumn<IGamPadRequest>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IGamPadRequest>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Request No.",
      cell: (row) => row.id,
      ...commonSetting,
    },
    {
      name: "Request Date",
      cell: (row) =>
        row?.createdAt
          ? new Date(row?.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
            })
          : "",
      ...commonSetting,
    },
    {
      name: "Requested By",
      cell: (row) => (
        <span>
          {row.createdBy?.firstname}
          {row.createdBy?.lastname}
        </span>
      ),
      ...commonSetting,
    },
    {
      name: "Series From",
      cell: (row) => row.seriesFrom,
      ...commonSetting,
    },
    {
      name: "Series To",
      cell: (row) => row.seriesTo,
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.numberOfPads,
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={getTextStatusColor(row.status ?? "")}>
          {capitalizeFirstLetterWords(row.status ?? "", "_")}
        </Typography>
      ),
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
