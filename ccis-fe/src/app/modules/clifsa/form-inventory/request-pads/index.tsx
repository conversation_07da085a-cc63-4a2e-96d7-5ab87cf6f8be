import React, { ReactNode } from "react";

import Tabs from "@components/common/Tabs";
import RequestTab from "./components/Tabs/RequestTab";
import ApprovedTab from "./components/Tabs/ApprovedTab";

const ClifsaRequestForms: React.FC = () => {
  const headers: string[] = ["Requests", "Approved"];
  const contents: ReactNode[] = [<RequestTab />, <ApprovedTab />];

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">Requests Pads</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default ClifsaRequestForms;
