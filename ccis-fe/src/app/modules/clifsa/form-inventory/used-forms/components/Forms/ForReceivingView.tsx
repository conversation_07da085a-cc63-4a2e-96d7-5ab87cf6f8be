import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import {
  getTransmittalTrailService,
  putFormTransmittalTrailService,
} from "@services/form-inventory-transmittal/form-inventory-transmittal.service";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { VerifyFormsSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { useFormik } from "formik";
import { FormStatus } from "@enums/form-status";
import { useUserManagementActions } from "@state/reducer/users-management";
import { findItem } from "@helpers/array";

const ViewFormReceiving: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { getUsers } = useUserManagementActions();
  const divisions = useSelector(
    (state: RootState) => state.formInventoryUtilitiesDivisions.divisions
  );
  const formTypes = useSelector(
    (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
  );
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const filterUser = "";

  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();

  const formik = useFormik({
    initialValues: {
      status: "",
    },
    validationSchema: VerifyFormsSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit(
        "Are you sure you want to receive this transmittal?"
      );
      if (isConfirmed) {
        try {
          if (data?.latestFormTransmittalTrail?.id) {
            const payload = {
              ...values,
            };
            await putFormTransmittalTrailService(
              data.latestFormTransmittalTrail?.id,
              payload
            );
            toast.success("Received form successfully");
            resetForm();
            navigate(ROUTES.CLIFSAADMIN.clifsaAdminNewForm.key);
          } else {
            toast.error(
              "Failed to process form: Form Transmittal Trail ID is undefined"
            );
          }
        } catch (error) {
          toast.error("Failed to approve form");
        }
      }
    },
  });

  const fetchForm = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getTransmittalTrailService(Number(id));
        if (response?.data) {
          setData(response.data);
        }
      }
    } catch (error) {
      toast.error("Failed to load. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
  }, []);

  useEffect(() => {
    getUsers({ filter: filterUser });
  }, []);
  return loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button
        classNames="btn bg-slate-600 btn-sm"
        onClick={() =>
          navigate(ROUTES.CLIFSAADMIN.clifsaAdminNewForm.key)
        }
      >
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">
          FORM DETAILS
        </Typography>

        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full">
            <div className="flex w-full flex-col">
              <div className="divider divider-start">Series Overview</div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-3 grid-flow-col gap-4">
                <div className="p-2">
                  <p className="text-sm">Released By</p>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    <p>
                      {data?.createdBy?.firstname}{" "}
                      {data?.createdBy?.middlename}{" "}
                      {data?.createdBy?.lastname}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Area Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>
                      {String(
                        findItem(
                          area,
                          "id",
                          Number(data?.releasedAreaId),
                          "areaName"
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 w-auto text-sm">
                    <p>
                    {String(
                        findItem(
                          users,
                          "id",
                          Number(data?.releasedToId),
                          undefined,
                          (user) =>
                            `${user.firstname} ${user.middlename || ""} ${user.lastname}`
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Date Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>
                      {data?.createdAt
                        ? new Date(data.createdAt).toLocaleDateString("en-US")
                        : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300 p-2">
              <div className="p-6">
                <div className="grid grid-cols-5 gap-4">
                  <div className="p-2">
                    <p className="text-sm">Transmittal No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.transmittalNumber}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Division</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>
                      {String(
                        findItem(
                          divisions,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.divisionId),
                          "divisionName"
                        ) || "N/A"
                      )}
                      </p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Type</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>
                      {String(
                        findItem(
                          formTypes,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.formTypeId),
                          "formTypeCode"
                        ) || "N/A"
                      )}
                      </p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Area</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>
                      {String(
                        findItem(
                          area,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.areaId),
                          "areaName"
                        ) || "N/A"
                      )}
                      </p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">ATP No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 mb-4 flex w-full">
                <div className="overflow-auto max-h-64 w-full">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4 sticky top-0 z-10">
                      <tr>
                        <th className="p-4 text-sm border-zinc-100">
                          Pad Number
                        </th>
                        <th className="p-4 text-sm">Series From</th>
                        <th className="p-4 text-sm">Series To</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data?.padAssignments?.map((assignment) => (
                        <tr key={assignment.id}>
                          <td className="p-4 text-sm border border-slate-100 text-center">
                            {assignment.padNumber}
                          </td>
                          <td className="p-4 text-sm border border-slate-100 text-center">
                            {assignment.seriesFrom}
                          </td>
                          <td className="p-4 text-sm border border-slate-100 text-center">
                            {assignment.seriesTo}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="divider divider-horizontal"></div>
                <div className="bg-slate-50 p-4 min-w-96 w-full">
                  <div className="p-2 flex justify-center bg-white rounded mb-2">
                    Remarks
                  </div>
                  <div className="bg-white p-4 text-sm rounded">
                    {data?.remarks?.split("\n").map((line, index) => (
                      <p key={index}>-{line}</p>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center gap-2 mt-4">
          <Button
            type="submit"
            classNames="bg-sky-500 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
            onClick={() => {
              formik.setFieldValue("status", FormStatus.RECEIVED);
              formik.handleSubmit();
            }}
          >
            Receive
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ViewFormReceiving;
