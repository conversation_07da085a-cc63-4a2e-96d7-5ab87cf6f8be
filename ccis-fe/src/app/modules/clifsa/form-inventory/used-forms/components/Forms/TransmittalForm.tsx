import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { getAreaAdminsService } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.service";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import Loader from "@components/Loader";
import { Form, FormikProvider, useFormik } from "formik";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { getTransmittalService } from "@services/form-inventory-transmittal/form-inventory-transmittal.service";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { AreaCode, FormStatus } from "@enums/form-status";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import { useUserManagementActions } from "@state/reducer/users-management";
import { navigateBack } from "@helpers/navigatorHelper";
import { findItem, formatSelectOptions } from "@helpers/array";
import { releasedVia } from "@constants/global-constant-value";
import Select from "@components/form/Select";
import { useReleasedMethodActions } from "@state/reducer/form-inventory-utilities-released-methods";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { TFormTransmittalOutgoingPayload } from "@state/types/form-inventory-transmittal";
import { formatStringAtoZ0to9 } from "@helpers/text";
import { CreateTransmittalAdminOutgoingSchema } from "@services/form-inventory-transmittal/form-inventory-transmittal.schema";
import { useLocationActions } from "@state/reducer/form-inventory-utilities-location";
import { getLocationsService } from "@services/form-inventory-utilities-location.ts/form-inventory-utilities-location";

const ViewTransmittalForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const [formContainers, setFormContainers] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [postTrailData, setPostTrailData] = useState<TFormTransmittalOutgoingPayload>({
    id: 0,
    releasedArea: 0,
    releasedTo: 0,
    status: FormStatus.RELEASED,
    releasedMethodId: undefined,
    deliveredBy: "",
  });
  const [releasedMethodText, setReleasedMethodText] = useState<string>("");
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const ReleasedMethod = useSelector((state: RootState) => state.formInventoryUtilitiesReleasedMethods.releasedMethods);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const filterPosition = "";
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getLocations } = useLocationActions();

  const { getAreas } = useAreaActions();
  const { getUsers } = useUserManagementActions();
  const { getPosition } = usePositionsManagementActions();
  const { getReleasedMethods } = useReleasedMethodActions();
  const { postFormTransmittalTrail, clearSelectedTransmittalForm } = useTransmittalFormActions();

  const profile = useSelector((state: RootState) => state.profile.profile);
  const [areaAdmins, setAreaAdmins] = useState<
    {
      user_area_id: number;
      user_id: number;
      admin_name: string;
      branch_name: string;
    }[]
  >([]);
  const postTrailSuccess = useSelector((state: RootState) => state.formInventoryTransmittal.postFormTransmittalTrail?.success);
  const [_selectedAreaCode, setSelectedAreaCode] = useState<AreaCode | "">("");
  const [locationOptions, setLocationOptions] = useState<any[]>([]);

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: postTrailData,

    validationSchema: CreateTransmittalAdminOutgoingSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit("Are you sure you want to release this transmittal?");

      if (isConfirmed) {
        try {
          if (!id) {
            toast.error("Missing form transmittal ID.");
            return;
          }

          await postFormTransmittalTrail(values as TFormTransmittalOutgoingPayload);
          resetForm();
        } catch (error) {
          toast.error("Failed to release transmittal. Please try again.");
        }
      }
    },
  });

  const fetchForm = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getTransmittalService(Number(id));
        if (response?.data) {
          setData(response.data);
        }
      }
    } catch (error) {
      toast.error("Failed to load proposal data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleAreaChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const areaCode = e.target.value as AreaCode;
    setSelectedAreaCode(areaCode);

    try {
      const { data } = await getLocationsService({
        type: areaCode,
        filter: "",
      });
      setLocationOptions(data || []);
    } catch (error) {
      toast.error("Failed to load locations for selected area.");
    }
  };

  useEffect(() => {
    if (postTrailSuccess) {
      navigate(ROUTES.CLIFSAADMIN.clifsaAdminNewForm.key);
      clearSelectedTransmittalForm();
    }
  }, [postTrailSuccess]);
  useEffect(() => {
    const fetchAdmins = async () => {
      try {
        const response = await getAreaAdminsService();
        setAreaAdmins(response.data || []);
      } catch (error) {
        toast.error("Failed to load area admins. Please try again.");
      }
    };

    fetchAdmins();
    getReleasedMethods({ filter: "" });
  }, []);

  useEffect(() => {
    getUsers({ filter: "" });
    fetchForm();
  }, [id]);

  //Fetches already selected form
  useEffect(() => {
    if (data && !formContainers.some((f) => f.id === data.id)) {
      setFormContainers((prev) => [...prev, data]);
    }
  }, [data]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    getLocations({
      params: {
        type: AreaCode.LUZON,
        filter: "",
      },
    });
  }, []);

  useEffect(() => {
    getPosition({ filter: filterPosition });
    getUsers;
  }, []);

  const releasedViaOption = formatSelectOptions(ReleasedMethod, "releasedMethodName");
  const defaultOption = {
    value: "",
    text: "Please select a value",
    disabled: !formik.values.releasedMethodId ? false : true,
  };

  const releasedViaOptionOptionsWithDefault = [defaultOption, ...releasedViaOption];

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event.target.value;
    const selectedText = event.target.options[event.target.selectedIndex].text;
    const initialHandedBy = profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A";
    const conditionReleasedVia = selectedText === releasedVia.thirdPartyPerson || selectedText === releasedVia.onHand;

    if (selectedValue === "") {
      return;
    }

    const updatedValue = Number(selectedValue);

    setPostTrailData((prevData) => ({
      ...prevData,
      releasedMethodId: updatedValue,
      releasedArea: formik.values.releasedArea,
      deliveredBy: conditionReleasedVia ? initialHandedBy : "",
      releasedTo: formik.values.releasedTo,
      trackingNo: conditionReleasedVia ? " " : "",
      id: Number(data?.id),
    }));

    // Update formik values
    formik.setFieldValue("releasedMethodId", updatedValue);
    formik.setFieldValue("deliveredBy", conditionReleasedVia ? initialHandedBy : "");

    setReleasedMethodText(selectedText);
  };

  const handleTextFieldChange = (name: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    // Update both formik and postTrailData state
    formik.setFieldValue(`postTrailData.${name}`, value);
    setPostTrailData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  useEffect(() => {
    if (!data?.releasedAreaId) return;

    const matchedAdmin = areaAdmins.find((admin) => admin.user_area_id === data.releasedAreaId);
    // Only update if releasedTo is not already correct
    if (matchedAdmin && data.releasedToId !== matchedAdmin.user_id) {
      setData((prevData) => ({
        ...prevData,
        releasedToId: matchedAdmin.user_id,
      }));
    }
  }, [data?.releasedAreaId, areaAdmins]);

  return loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <div>
        <Button classNames="btn bg-slate-600 btn-sm" onClick={() => navigateBack()}>
          Back
        </Button>
        <div className="mx-6">
          <Typography className="mt-6 text-primary uppercase font-poppins-semibold-">Transmittal Details</Typography>
          <div className=" mt-8">
            <FormikProvider value={formik}>
              <Form>
                <div className="flex w-full flex-col">
                  <div className="divider divider-start uppercase">Assignee Details</div>
                </div>
                <div>
                  <div className="p-6">
                    <div className="grid grid-cols-4 gap-4">
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released by</p>
                        <div className="w-60 text-sm">
                          <TextField disabled className="w-auto" size="sm" value={profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A"} />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">GEO Division</p>
                        <div className="w-60 text-sm">
                          <Select
                            name="areaReleased"
                            options={[
                              { value: AreaCode.LUZON, text: "Luzon" },
                              { value: AreaCode.VISAYAS, text: "Visayas" },
                              { value: AreaCode.MINDANAO, text: "Mindanao" },
                            ]}
                            placeholder="Select Area"
                            onChange={handleAreaChange}
                            size="sm"
                          />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Release Area</p>
                        <div className="w-60 text-sm">
                          <Select
                            name="releasedArea"
                            options={locationOptions.map((loc) => ({
                              value: loc.id,
                              text: loc.locationName,
                            }))}
                            placeholder="Select Location"
                            size="sm"
                            onChange={(e) => {
                              const locationId = Number(e.target.value);
                              const selectedLocation = locationOptions.find((loc) => loc.id === locationId);
                              const assignedUser = selectedLocation?.users?.[0];

                              formik.setFieldValue("releasedArea", locationId);
                              formik.setFieldValue("releasedTo", assignedUser?.id || 0);

                              setPostTrailData((prev) => ({
                                ...prev,
                                releasedArea: locationId,
                                releasedTo: assignedUser?.id || 0,
                              }));
                            }}
                            value={formik.values.releasedArea}
                            disabled={!_selectedAreaCode} // Disable if no area code is selected
                          />
                        </div>
                      </div>

                      <div className="p-2 flex-1">
                        <p className="text-sm">Released To</p>
                        <div className="w-60 text-sm">
                          <Select
                            name="releasedTo"
                            options={(() => {
                              const selectedLocation = locationOptions.find((loc) => loc.id === formik.values.releasedArea);

                              if (!selectedLocation?.users || selectedLocation.users.length === 0) {
                                return [{ value: "", text: "No users available", disabled: true }];
                              }

                              return [
                                ...selectedLocation.users.map((user: any) => ({
                                  value: user.id,
                                  text: `${user.firstname || ""} 
                                         ${user.middlename || ""} 
                                         ${user.lastname || ""}
                                         ${"-"}
                                         ${user.position.positionName || ""}
                                         `.trim(),
                                })),
                              ];
                            })()}
                            placeholder="Select User"
                            size="sm"
                            value={formik.values.releasedTo}
                            onChange={(e) => {
                              const userId = Number(e.target.value);
                              formik.setFieldValue("releasedTo", userId);

                              setPostTrailData((prev) => ({
                                ...prev,
                                releasedTo: userId,
                              }));
                            }}
                            disabled={!formik.values.releasedArea} // Disable if no area is selected
                          />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Date Released</p>
                        <div className="w-60 text-sm">
                          <TextField
                            className="w-48"
                            value={new Date().toLocaleDateString("en-US", {
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            })}
                            size="sm"
                            disabled
                          />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Releases Via</p>
                        <div className="w-60 text-sm">
                          <Select
                            name="releasedMethodId"
                            options={releasedViaOptionOptionsWithDefault}
                            placeholder=""
                            className="w-1/4"
                            value={1}
                            size="sm"
                            onChange={handleSelectChange} // Capture selected value
                            error={(formik.touched.releasedMethodId && !!formik.errors?.releasedMethodId) || formik.values.releasedMethodId === 0}
                            errorText={formik.errors.releasedMethodId}
                            required
                          />
                        </div>
                      </div>
                      {formatStringAtoZ0to9(releasedMethodText) === formatStringAtoZ0to9(releasedVia.onHand) ||
                      formatStringAtoZ0to9(releasedMethodText) === formatStringAtoZ0to9(releasedVia.thirdPartyPerson) ? (
                        <div className="p-2 flex-row w-1/3 ">
                          <p className="text-sm">Handed By</p>
                          <div className="w-60 text-sm">
                            <TextField
                              className="w-48"
                              size="sm"
                              name="deliveredBy"
                              value={profile ? `${profile.firstname || "N/A"} ${profile.middlename || ""} ${profile.lastname || ""}`.trim() : "N/A"}
                              onChange={handleTextFieldChange("deliveredBy")}
                              error={formik.touched?.deliveredBy && !!formik.errors.deliveredBy}
                              errorText={formik.errors.deliveredBy}
                            />
                          </div>
                        </div>
                      ) : null}
                      {formatStringAtoZ0to9(releasedMethodText) === formatStringAtoZ0to9(releasedVia.mailCourier) ? (
                        <>
                          <div className="p-2 flex-1">
                            <p className="text-sm">Courier Service Name</p>
                            <div className="w-60 text-sm">
                              <TextField
                                className="w-48"
                                placeholder="Courier Service Name"
                                size="sm"
                                name="deliveredBy"
                                onChange={handleTextFieldChange("deliveredBy")}
                                value={formik.values.deliveredBy}
                                error={formik.touched.deliveredBy && !!formik.errors.deliveredBy}
                                errorText={formik.errors.deliveredBy}
                              />
                            </div>
                          </div>
                          <div className="p-2 flex-1">
                            <p className="text-sm">Tracking Number</p>
                            <div className="w-60 text-sm">
                              <TextField
                                className="w-48"
                                name="trackingNo"
                                size="sm"
                                placeholder="Enter Tracking Number"
                                value={formik.values.trackingNo}
                                onFocus={(e) => {
                                  if (formik.values.trackingNo === " ") {
                                    e.target.value = "";
                                  }
                                }}
                                onChange={handleTextFieldChange("trackingNo")}
                                error={formik.touched.trackingNo && !!formik.errors.trackingNo}
                                errorText={formik.errors.trackingNo}
                              />
                            </div>
                          </div>
                        </>
                      ) : null}
                    </div>
                  </div>
                </div>

                {/* for releasedVia */}
                <div>
                  <div className="p-6">
                    <div className="flex flex-row flex-wrap gap-4"></div>
                  </div>
                </div>
                <div className="flex w-full flex-col">
                  <div className="divider divider-start uppercase">Series Overview</div>
                </div>
                <div>
                  <div className="p-6">
                    <div className="flex flex-wrap gap-4">
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released by</p>
                        <div className="w-60 text-sm">
                          <TextField
                            disabled
                            className="w-auto"
                            size="sm"
                            value={data?.createdBy ? `${data.createdBy.firstname || "N/A"} ${data.createdBy.middlename || ""} ${data.createdBy.lastname || ""}`.trim() : "N/A"}
                          />
                        </div>
                      </div>

                      <div className="p-2 flex-1">
                        <p className="text-sm">Area Released</p>
                        <div className="w-60 text-sm">
                          <TextField className="w-48" disabled size="sm" value={String(findItem(area, "id", Number(data?.releasedAreaId), "areaName") || "N/A")} />
                        </div>
                      </div>
                      <div className="p-2 flex-1">
                        <p className="text-sm">Released To</p>
                        <div className="w-60 text-sm">
                          <TextField className="w-48" disabled size="sm" value={areaAdmins.find((admin) => admin.user_id === data?.releasedToId)?.admin_name || "N/A"} />
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* forms with pads assignment  */}
                  {formContainers.map((form, index) => (
                    <div key={form.id || index} className="border rounded border-slate-300 mt-4 mb-4">
                      <div className="flex flex-wrap gap-4 p-4 justify-center">
                        <div className="flex-1">
                          <p className="text-sm">Transmittal No.</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">{form.transmittalNumber ?? ""}</div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm">Division</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">
                            {String(findItem(divisions, "id", Number(data?.padAssignments?.[0]?.form?.divisionId), "divisionName") || "N/A")}
                          </div>
                        </div>
                        <div className="flex-1 w-full">
                          <p className="text-sm">Type</p>
                          {/* <div className="border-b-2 border-slate-300 w-32 text-sm mt-2 truncate"> */}
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2 whitespace-nowrap">
                            {String(findItem(formTypes, "id", Number(data?.padAssignments?.[0]?.form?.formTypeId), "formTypeCode") || "N/A")}
                          </div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm">Area</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">{String(findItem(area, "id", Number(data?.padAssignments?.[0]?.form?.areaId), "areaName") || "N/A")}</div>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm">ATP No.</p>
                          <div className="border-b-2 border-slate-300 w-32 text-sm mt-2">{form.padAssignments?.[0]?.form?.atpNumber ?? "Auto Generated"}</div>
                        </div>
                      </div>

                      <div className="flex justify-center gap-2 p-2">
                        <div className="flex-1">
                          <div className="overflow-auto max-h-64">
                            <table className="w-full s">
                              <thead className="bg-gradient-to-r from-zinc-100 to-indigo-50 p-4 sticky top-0 z-10">
                                <tr>
                                  <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                                  <th className="p-4 text-sm">Series From</th>
                                  <th className="p-4 text-sm">Series To</th>
                                </tr>
                              </thead>
                              <tbody>
                                {form.padAssignments && form.padAssignments.length > 0 ? (
                                  form.padAssignments.map(
                                    (
                                      pad: {
                                        padNumber: string;
                                        seriesFrom: string;
                                        seriesTo: string;
                                      },
                                      idx: number
                                    ) => (
                                      <tr key={idx}>
                                        <td className="p-4 text-sm border border-slate-100">{pad.padNumber}</td>
                                        <td className="p-4 text-sm border border-slate-100">{pad.seriesFrom}</td>
                                        <td className="p-4 text-sm border border-slate-100">{pad.seriesTo}</td>
                                      </tr>
                                    )
                                  )
                                ) : (
                                  <tr>
                                    <td colSpan={4} className="p-4 text-sm text-center text-zinc-400">
                                      No pad assignments available.
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div className="divider divider-horizontal"></div>
                        <div className="flex-1 bg-slate-100 p-4">
                          <div className="p-2 flex justify-center bg-white mb-2">Remarks</div>
                          <div className="bg-white p-4 text-sm">
                            <textarea disabled placeholder="Input remarks here" cols={40} rows={5} className="w-full p-2" value={form.remarks || ""} />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {/* ))} */}
                </div>

                <div className="flex justify-center mt-6">
                  <Button classNames="btn bg-slate-400 btn-sm mr-2 w-48" onClick={() => navigateBack()}>
                    Cancel
                  </Button>
                  <Button
                    classNames="btn btn-sm w-48 bg-primary"
                    type="button"
                    onClick={() => {
                      formik.submitForm();
                    }}
                  >
                    Release
                  </Button>
                </div>
              </Form>
            </FormikProvider>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewTransmittalForm;
