import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { FaCircleDot, FaClockRotateLeft, FaPrint } from "react-icons/fa6";
import { LiaDotCircleSolid } from "react-icons/lia";
import { useNavigate, useParams } from "react-router-dom";

import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import {
  getAreaAdminsService,
  getFormActivityLogsService,
} from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.service";
import {
  IFormActivityLogs,
  IFormTransmittal,
} from "@interface/form-inventory.interface";
import Select from "@components/form/Select";
import { getTransmittalService } from "@services/form-inventory-transmittal/form-inventory-transmittal.service";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { useUserManagementActions } from "@state/reducer/users-management";

import httpClient from "@clients/httpClient";
import { RoleType } from "@enums/form-status";
import { findItem } from "@helpers/array";
import Pagination from "@modules/admin/form-inventory-and-tracking/new-forms/incoming/components/pagination";

const ViewReleasedForms: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const [activityLogs, setActivityLogs] = useState<IFormActivityLogs | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 1; // Number of items per page
  const totalPages =
    data && data.padAssignments
      ? Math.ceil(data.padAssignments.length / itemsPerPage)
      : 0;

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  const [areaAdmins, setAreaAdmins] = useState<
    {
      user_area_id: number;
      user_id: number;
      admin_name: string;
      branch_name: string;
    }[]
  >([]);

  const users = useSelector((state: RootState) => state.usersManagement.users);
  const paginatedPadAssignments =
    data?.padAssignments?.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    ) || [];

  const divisions = useSelector(
    (state: RootState) => state.formInventoryUtilitiesDivisions.divisions
  );
  const formTypes = useSelector(
    (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
  );
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getUsers } = useUserManagementActions();

  const fetchForm = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getTransmittalService(Number(id)); // Use new service to fetch proposal by ID
        if (response?.data) {
          setData(response.data); // Set fetched data to state

          // Get the current pad assignment ID
          const currentPadAssignment =
            response.data.padAssignments?.[currentPage - 1];
          if (currentPadAssignment) {
            const padAssignmentId = currentPadAssignment.id;

            // Fetch activity logs using the new endpoint
            const activityLogsResponse = await getFormActivityLogsService(
              padAssignmentId
            );
            if (activityLogsResponse?.data) {
              setActivityLogs(activityLogsResponse.data);
            }
          }
        }
      }
    } catch (error) {
      toast.error("Failed to load proposal data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };
  const handlePrintPDF = async () => {
    try {
      if (!data?.id) {
        toast.error("Missing Form Transmittal ID.");
        return;
      }
      const response: any = await httpClient.post(
        "/form-transmittal/export-letter/outgoing-cashier",
        { formTransmittalId: data.id },
        { responseType: "blob" }
      );

      // Use the same blob creation pattern as the working component
      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const handlePrintReportPDF = async () => {
    try {
      if (!data?.id) {
        toast.error("Missing Form Transmittal ID.");
        return;
      }
      const formTransmittalIds = Array.isArray(data?.id) ? data.id : [data?.id];

      const response: any = await httpClient.post(
        "/form-transmittal/export-report/outgoing-cashier",
        { formTransmittalIds },
        { responseType: "blob" }
      );

      // Use the same blob creation pattern as the working component
      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");

      // Clean up the URL after a delay
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const hasClifsaAssignment = data?.padAssignments?.some((assignment) => {
    const areaName = area.find(
      (a) => a.id === assignment.form?.areaId
    )?.areaName;
    return areaName === RoleType.CLIFSA;
  });

  useEffect(() => {
    const fetchAdmins = async () => {
      try {
        const response = await getAreaAdminsService();
        setAreaAdmins(response.data || []);
      } catch (error) {
        toast.error("Failed to load area admins. Please try again.");
      }
    };

    fetchAdmins();
  }, []);

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    getUsers({ filter: "" }); // Fetch users for releasedToId
  }, []);

  useEffect(() => {
    if (data) {
      const fetchActivityLogs = async () => {
        const currentPadAssignment = data.padAssignments?.[currentPage - 1];
        if (currentPadAssignment) {
          const padAssignmentId = currentPadAssignment.id;
          const activityLogsResponse = await getFormActivityLogsService(
            padAssignmentId
          );
          if (activityLogsResponse?.data) {
            setActivityLogs(activityLogsResponse.data);
          }
        }
      };
      fetchActivityLogs();
    }
  }, [currentPage, data]);

  useEffect(() => {
    if (!data?.releasedAreaId) return;

    const matchedAdmin = areaAdmins.find(
      (admin) => admin.user_area_id === data.releasedAreaId
    );

    // Only update if releasedTo is not already correct
    if (matchedAdmin && data.releasedToId !== matchedAdmin.user_id) {
      setData((prevData) => ({
        ...prevData,
        releasedToId: matchedAdmin.user_id,
      }));
    }
  }, [data?.releasedAreaId, areaAdmins]);
  return loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button
        classNames="btn bg-slate-600 btn-sm"
        onClick={() =>
          navigate(ROUTES.CLIFSAADMIN.clifsaAdminNewForm.key)
        }
      >
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold uppercase">
          Transmittal Released Details
        </Typography>
        <div className="flex justify-end gap-2">
          <Button
            classNames="btn btn-sm bg-slate-600 text-slate-900"
            onClick={handlePrintPDF}
          >
            <FaPrint /> Print Transmittal Letter
          </Button>

          {hasClifsaAssignment && (
            <Button
              classNames="btn btn-sm bg-slate-600 text-slate-900"
              onClick={handlePrintReportPDF}
            >
              <FaPrint /> Print Transmittal Report
            </Button>
          )}
        </div>
        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-5/6 ">
            <div className="flex justify-end gap-2">
              <div>
                <p className="mt-1">Filter by:</p>
              </div>
              <div>
                <Select
                  className="select select-bordered select-sm w-full max-w-xs"
                  value={currentPage}
                  options={data?.padAssignments?.map((assignment, index) => ({
                    text: `Pad #: ${assignment.padNumber}`,
                    value: index + 1,
                  }))}
                  onChange={(e) => handlePageChange(Number(e.target.value))}
                />
              </div>
            </div>
            <div className="flex w-full flex-col">
              <div className="divider divider-start">Series Overview</div>
            </div>
            <div className="border rounded border-slate-300 p-6">
              <div className="grid grid-rows-2 grid-flow-col gap-4">
                <div className="p-2">
                  <p className="text-sm">Transmittal No.</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{data?.transmittalNumber}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released By</p>
                  <div className="border-b-2 border-slate-300 max-w-40 text-sm">
                    {" "}
                    <p>
                      {data?.createdBy
                        ? `${data?.createdBy?.firstname} ${
                            data?.createdBy?.middlename || ""
                          } ${data?.createdBy?.lastname}`
                        : "N/A"}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Division</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>
                      {String(
                        findItem(
                          divisions,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.divisionId),
                          "divisionName"
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Type</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>
                      {String(
                        findItem(
                          formTypes,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.formTypeId),
                          "formTypeCode"
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>

                <div className="p-2">
                  <p className="text-sm">Area Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>
                      {String(
                        findItem(
                          area,
                          "id",
                          Number(data?.releasedAreaId),
                          "areaName"
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>
                      {String(
                        findItem(
                          users,
                          "id",
                          Number(data?.releasedToId),
                          undefined,
                          (user) =>
                            `${user.firstname} ${user.middlename || ""} ${user.lastname}`
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Area</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>
                      {String(
                        findItem(
                          area,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.areaId),
                          "areaName"
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">ATP No.</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {" "}
                    <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                  </div>
                </div>
              </div>
              <div className="p-2">
                <p className="text-sm">Released Date</p>
                <div className="border-b-2 border-slate-300 w-32 text-sm">
                  {" "}
                  <p>
                    {data?.createdAt
                      ? new Date(data.createdAt).toLocaleDateString("en-US", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                      : "N/A"}
                  </p>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300 p-2 mt-4 mb-4 flex w-full">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4">
                  <tr>
                    <th className="p-4 text-sm border-zinc-100">Pad Number</th>
                    <th className="p-4 text-sm">Series From</th>
                    <th className="p-4 text-sm">Series To</th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedPadAssignments.map((assignment) => (
                    <tr key={assignment.id}>
                      <td className="p-4 text-sm border border-slate-100 text-center">
                        {assignment.padNumber}
                      </td>
                      <td className="p-4 text-sm border border-slate-100 text-center">
                        {assignment.seriesFrom}
                      </td>
                      <td className="p-4 text-sm border border-slate-100 text-center">
                        {assignment.seriesTo}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <div className="divider divider-horizontal"></div>
              <div className="bg-slate-50 p-4 min-w-96 w-full">
                <div className="p-2 flex justify-center bg-white rounded mb-2">
                  Remarks
                </div>
                <div className="bg-white p-4 text-sm rounded">
                  {data?.remarks?.split("\n").map((line, index) => (
                    <p key={index}>-{line}</p>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex w-full flex-col p-4 border border-slate-300 rounded">
              <div className="divider divider-start uppercase">
                Release Details
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm">Released By</p>
                  <div className="border-b-2 border-slate-300 w-auto text-sm">
                    {data?.releasedTo
                      ? `${data?.releasedTo?.firstname} ${
                          data?.releasedTo?.middlename || ""
                        } ${data?.releasedTo?.lastname}`
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 w-full text-sm">
                  {data?.latestFormTransmittalTrail?.releasedTo
                      ? `${data?.latestFormTransmittalTrail?.releasedTo?.firstname} ${
                          data?.latestFormTransmittalTrail?.releasedTo?.middlename || ""
                        } ${data?.latestFormTransmittalTrail?.releasedTo?.lastname}`
                      : "N/A"}
                  </div>
                </div>
                <div>
                  <p className="text-sm">Date Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    {data?.latestFormTransmittalTrail?.createdAt
                      ? new Date(data.latestFormTransmittalTrail?.createdAt).toLocaleDateString("en-US", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                      : "N/A"}
                  </div>
                </div>
              </div>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>

            <div className="col-span-1 rounded border border-slate-300 max-w-3/5 p-4 max-h-screen overflow-y-auto">
            <div className="border-b-2 border-slate-200 p-4 justify-center flex gap-2 mb-2">
              <FaClockRotateLeft className="text-sm mt-1" /> <p>Status</p>
            </div>
            {Array.isArray(activityLogs) &&
              activityLogs
              .slice()
              .reverse()
              .map((log, index) => (
                <React.Fragment key={log.id}>
                <div className="flex gap-4">
                  <div className="flex gap-4">
                  <div>
                    <div className="text-xs text-end">
                    {new Date(log.created_at).toLocaleDateString(
                      "en-US",
                      {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                      }
                    )}
                    </div>
                    <div className="text-xs text-slate-400 text-end">
                    {new Date(log.created_at).toLocaleTimeString(
                      "en-US",
                      {
                      hour: "2-digit",
                      minute: "2-digit",
                      }
                    )}
                    </div>
                  </div>
                  <div>
                    <div>
                    {index === 0 ? (
                      <FaCircleDot className="text-primary size-6 mt-1" />
                    ) : (
                      <LiaDotCircleSolid className="text-zinc-400 size-6 mt-1" />
                    )}
                    {index !== activityLogs.length - 1 && (
                      <div className="flex mt-1 flex-col items-center">
                      <div className="h-8 border-l-2 border-slate-300"></div>
                      </div>
                    )}
                    </div>
                  </div>
                  </div>

                  <div>
                  <div>{log.description}</div>
                  <div className="text-xs">
                    {log.causer.firstname} {log.causer.lastname} |{" "}
                    {log.causer.position.positionName}
                  </div>
                  </div>
                </div>
                </React.Fragment>
              ))}
            </div>
        </div>
      </div>
    </div>
  );
};

export default ViewReleasedForms;
