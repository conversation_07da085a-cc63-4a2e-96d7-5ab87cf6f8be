import React, { ReactNode } from "react";


import Tabs from "@components/common/Tabs";

import ReturnPadsTab from "./components/ReturnPadsTab";
import InventoryTab from "./components/InventoryTab";
import ForReceivingTab from "./components/ForReceivingTab";

const ClifsaUsedForms: React.FC = () => {
    const headers: string[] = ["For Receiving", "Return Pad", "Inventory"];
    const contents: ReactNode[] = [<ForReceivingTab />, <ReturnPadsTab/>, <InventoryTab />];
  
  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard /{" "}
        <span className="text-primary font-poppins-semibold ">Used Forms</span> 
      </div>
      <Tabs
        headers={headers}
        contents={contents}
        size="md"
        headerClass="w-52"
        fullWidthHeader={false}
      />
    </div>
  );
};

export default ClifsaUsedForms;
