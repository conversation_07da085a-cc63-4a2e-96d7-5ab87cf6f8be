// Important imports
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import { Form, FormikProvider, useFormik } from "formik";
import React, { useEffect } from "react";

// Importing the Remittance Type from the utilities interface 
import { capitalizeFirstLetterWords } from "@helpers/text";
import { IUtilitiesRemittanceType } from "@interface/utilities.interface";

// Schema to be used for both create and edit
import {
  CreateRemittanceTypeSchema,
  EditRemittanceTypeSchema,
} from "@services/utilities-remittance-type/utilities-remittance-type.schema";
// Toast for the in-page notifications
import { toast } from "react-toastify";

type ExportReportModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onExport: (payload: any) => void;
  departments: { id: number; name: string }[];
};

const ExportReportModal: React.FC<ExportReportModalProps> = ({
  isOpen,
  onClose,
  onExport,
  departments,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 grid place-items-center bg-black/30">
      <div className="w-full max-w-md rounded bg-white p-5 shadow space-y-4">
        <h3 className="text-lg font-semibold">Generate Reports</h3>
        <div className="space-y-2">
          <div>
            <label className="block text-sm font-medium mb-1">Department</label>
            <select className="w-full border rounded p-2">
              <option>— Select —</option>
              {departments.map((dept) => (
                <option key={dept.id} value={dept.id}>
                  {dept.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Status</label>
            <select className="w-full border rounded p-2">
              <option>All</option>
              <option>Resolved</option>
              <option>Unresolved</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Period</label>
            <div className="grid grid-cols-2 gap-2">
              <input type="date" className="border rounded p-2" placeholder="From" />
              <input type="date" className="border rounded p-2" placeholder="To" />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">File type</label>
            <select className="w-full border rounded p-2">
              <option>PDF</option>
              <option>CSV</option>
            </select>
          </div>
        </div>
        <div className="flex justify-end gap-2 pt-2">
          <button className="px-4 py-2 rounded border" onClick={onClose}>
            Cancel
          </button>
          <button className="px-4 py-2 rounded bg-primary text-white" onClick={onExport}>
            Export
          </button>
        </div>
      </div>
    </div>
  );
};
export default ExportReportModal;

