import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { FC, useState } from "react";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (remarks: string) => void;
  title?: string;
  description?: string;
  placeholder?: string;
  ticketId?: number;
};

const ForApprovalModal: FC<Props> = ({
  isOpen,
  onClose,
  onSubmit,
  title,
  description,
  placeholder,
}) => {
  const [remarks, setRemarks] = useState("");

  const handleSubmit = () => {
    onSubmit(remarks);
    setRemarks(""); // Clear the textarea after submission
    onClose();
  };

  const handleCancel = () => {
    setRemarks(""); // Clear the textarea on cancel
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} showCloseButton={false} showHeader={false} modalContainerClassName="max-w-lg" className="backdrop-blur-sm">
      <div className="pt-6">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-3">{title}</h2>
          <p className="text-sm text-gray-600 leading-relaxed">{description}</p>
        </div>

        {/* Remarks Section */}
        <div className="mb-6">
          <label className="block text-sm font-semibold text-gray-700 mb-3">Remarks</label>
          <textarea
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
            placeholder={placeholder}
            className="w-full px-3 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-sm"
            rows={6}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end">
          <Button type="button" onClick={handleCancel} variant="custom" classNames="w-full">
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} classNames="w-full mt-4 bg-primary hover:bg-primary-dark text-white text-xs">
            Submit
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ForApprovalModal;
