//Imports are placed here:
import React from "react";
import ColumnChart from "../IncomingOutgoingCashierDashboard.tsx/charts/columnChart";
import PriorityLevelChart from "./components/PriorityLevel";
import DueDateList from "./components/DueDateList";
import RequestList from "./components/RequestList";
//This will import the managers and the ticket list
import { useDepartmentsManagementActions } from "@state/reducer/utilities-departments";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { IGraphTicket } from "@interface/departmental-ticketing-interface";
import { IUtilitiesDepartment } from "@interface/utilities.interface";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import Loader from "@components/Loader";
//For Mobile Devices
import useIsMobile from "@hooks/useIsMobile";
//For the Button of Generate Report
import { MdBarChart } from "react-icons/md";
import Button from "@components/common/Button";
import { useState } from "react";
import ExportReportModal from "./components/modals/ExportReportModal";

//Dashboard Component here
const RequestorDashboard: React.FC = () => {
  //* For use to open and close the modal for export
  const [open, setOpen] = useState(false);
  //Check if the user is using a mobile device: (???)
  const isMobile = useIsMobile();

  // ColumnChart data
  // Display all departments, then display all tickets with their respective ids
  const { getDepartment } = useDepartmentsManagementActions();
  const { getGraphTicket } = useTicketActions();
  const departments = useSelector((state: RootState) => state.utilitiesDepartments?.getDepartment?.data || []);
  const graphTickets = useSelector((state: RootState) => state.departmentalTicketing?.graphTickets || []);
  const loading = useSelector((state: RootState) => state.departmentalTicketing?.getGraphTicket?.loading);
  const error = useSelector((state: RootState) => state.departmentalTicketing?.getGraphTicket?.error);

  //First, we sort the departments by ID
  const dept: IUtilitiesDepartment[] = departments;
  const sortedDepartments = [...dept].sort((a: any, b: any) => a.id - b.id);

  //Then we map through the sorted departments and calculate both resolved and unresolved percentages
  const resolvedData = sortedDepartments.map((department: any) => {
    const departmentTickets = graphTickets.filter((ticket: IGraphTicket) => ticket.toDepartmentId === department.id);
    const totalTickets = departmentTickets.length;
    const resolvedTickets = departmentTickets.filter((ticket: IGraphTicket) => ticket.status === "RESOLVED").length;
    
    return totalTickets > 0 ? Math.round((resolvedTickets / totalTickets) * 100) : 0;
  });

  // const unresolvedData = sortedDepartments.map((department: any) => {
  //   const departmentTickets = graphTickets.filter((ticket: IGraphTicket) => ticket.toDepartmentId === department.id);
  //   const totalTickets = departmentTickets.length;
  //   const unresolvedTickets = departmentTickets.filter((ticket: IGraphTicket) => ticket.status === "UNRESOLVED").length;
    
  //   return totalTickets > 0 ? Math.round((unresolvedTickets / totalTickets) * 100) : 0;
  // });

  //Then put into categories the sorted Department Names:
  
  let categories = sortedDepartments.map((department: any) => department.departmentName);
  
  categories = categories.map((category: string) => {
    if ((category.split(" ").length > 1) || category.includes("-")) {
      return category.split(" ").filter(word => word !== "-");
    } else {
      return category;
    }
  });
  categories = categories.filter(category => category.length > 0);

  //* Create custom tooltip configuration
  const customTooltip = {
    custom: function({ series, seriesIndex, dataPointIndex, w }: { 
      series: number[][], 
      seriesIndex: number, 
      dataPointIndex: number, 
      w: any 
    }) {
      const resolvedPercentage = series[seriesIndex][dataPointIndex];
      const unresolvedPercentage = 100 - resolvedPercentage;
      const departmentName = w.globals.labels[dataPointIndex];
      
      // Calculate total tickets for this department
      const department = sortedDepartments[dataPointIndex];
      const departmentTickets = graphTickets.filter((ticket: IGraphTicket) => ticket.toDepartmentId === department.id);
      const totalTickets = departmentTickets.length;
      
      return `
        <div style="background: #fff; padding: 8px 12px; border: 1px solid #e3e3e3; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-family: inherit; font-size: 10px;">
          <div style="font-weight: 600; margin-bottom: 4px; color: #333;">${departmentName} (Total: ${totalTickets})</div>
          <div style="color: #1d2afc; margin: 2px 0;">
            <span style="display: inline-block; width: 8px; height: 8px; background: #1d2afc; border-radius: 50%; margin-right: 6px;"></span>
            Resolved: <strong>${resolvedPercentage}%</strong>
          </div>
          <div style="color: #ff4560; margin: 2px 0;">
            <span style="display: inline-block; width: 8px; height: 8px; background: #ff4560; border-radius: 50%; margin-right: 6px;"></span>
            Unresolved: <strong>${unresolvedPercentage}%</strong>
          </div>
        </div>
      `;
    }
  };

  const series = [
    {
      name: "Resolved (%)",
      data: resolvedData,
    },
  ];
  const colors = ["#1d2afc"]; 

  // const categories = ["Business", "Cashier", "EDP", "Finance", "Infrastructure", "Marketing", "Sales", "SysDev", "Underwriting", "Purchase", "OOP", "Human Resource"];
  // const series = [
  //   {
  //     name: "Tickets",
  //     data: [65, 80, 55, 70, 45, 60, 75, 50, 85, 40, 90, 70], // Percentages
  //   },
  // ];
  // const colors = ["#1d2afc"];
  
  //For executing function to get all departments
  useEffect(() => {
    getDepartment({
      filter: "",
    });
    getGraphTicket({
      params: {
        relations: `toDepartment`,
        condition: `status[orWhere]=unresolved,resolved`,
      }
    });
  }, []);
  

if (loading) {
  return (
    <div className="flex flex-1 w-full items-center justify-center">
      <Loader />
    </div>
  );
} else if (error) {
  return (
    <div className="flex flex-1 w-full items-center justify-center">
      <div className="p-4 text-red-500 text-center">
        <p>Failed to load data. Please try again.</p>
      </div>
    </div>
  );
} else if (!graphTickets || graphTickets.length === 0) {
  return (
    <div className="flex flex-1 w-full items-center justify-center">
      <div className="p-4 text-gray-500 text-center">
        <p>No data found.</p>
      </div>
    </div>
  );
}

  return (
    <div>
      {/* This is for the Export Report Modal, specifically to display when it is activated */}
      <div className="flex items-center justify-between p-4">
        {/* I have to add the button to generate reports */}
        <h1 className="text-2xl font-bold">Ticket Resolution Status By Department</h1>
        <Button classNames="inline-flex items-center gap-2 !text-primary" onClick={() => setOpen(true)}><MdBarChart size={18}/>Generate Reports</Button>
      </div>
      <ExportReportModal isOpen={open} onClose={() => setOpen(false)} onExport={(payload) => {/* handle export */}} departments={departments.map(d => ({ id: d.id, name: d.departmentName }))}/>
      
      {/* Column Chart here */}
      <div className={`h-fit shadow rounded-md px-4 py-2 ${isMobile && 'm-2'}`}>
        <div className="overflow-x-auto">
          <div className={`w-full space-y-4 ${isMobile ? 'mx-1' : 'mx-4'}`} style={{ minWidth: categories.length > 10 ? `${categories.length * (isMobile ? 60 : 80)}px` : 'auto' }}>
            <ColumnChart 
              scrollbar={true} 
              categories={categories} 
              series={series} 
              colors={colors} 
              slantText={isMobile} 
              useGradient 
              title=" " 
              customTooltip={customTooltip}
            />
            {/* Additional customTootip made here */}
          </div>
        </div>
      </div>
      {/* Below this are the dashboard components */}
      {isMobile ? 
      //For Mobile View
      <div className={'flex flex-col space-y-4'}>
        {/* Request List that differ depending on user role */}
        <div className="w-full p-2">
          <PriorityLevelChart />
        </div>
        <div className="w-full p-2">
          <DueDateList />
        </div>
        <div className="w-full p-2">
          <RequestList />
        </div>
      </div>
      :
      //For Desktop View
      <div className="flex gap-4 mt-4">
        {/* Request List that differ depending on user role */}
        <div className="w-full">
          <RequestList />
        </div>
        <div className="w-2/3">
          <div>
            <PriorityLevelChart />
          </div>
          <div>
            <DueDateList />
          </div>
        </div>
      </div>
      }
      
    </div>
  );
};

export default RequestorDashboard;
