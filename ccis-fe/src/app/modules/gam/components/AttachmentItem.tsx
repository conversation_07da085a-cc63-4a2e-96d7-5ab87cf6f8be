import { FaRegFilePdf } from "react-icons/fa";

type AttachmentItemProps = {
  filepath?: string;
  mimeType?: string;
  filename?: string;
  size?: number | string;
  isForViewing?: boolean;
};

const AttachmentItem = ({ filepath = "", mimeType = "", filename = "", size = 0, isForViewing = false }: AttachmentItemProps) => {
  const fullPath = `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${filepath}`;
  const fileSize = typeof size === "string" ? parseInt(size) : size;

  const handleClick = () => {
    if (!isForViewing) {
      window.open(`${fullPath}`, "_blank"); // Can also be an image URL
    }
  };

  return (
    <div>
      {/* PDF */}
      {mimeType === "application/pdf" && (
        <div className={`bg-[#f5f5f5] rounded px-4 py6 w-60 h-14 flex items-center gap-3 shadow-md ${!isForViewing && "cursor-pointer"}`} onClick={handleClick}>
          <FaRegFilePdf size={30} />
          <div className="flex flex-col">
            <span className="text-sm font-semibold">{filename}</span>
            <span className="text-xs">{(fileSize / 1024).toFixed(2)} KB</span>
          </div>
        </div>
      )}

      {["image/jpeg", "image/png", "image/jpg"].includes(mimeType) && <img className="h-30 w-56 object-cover" src={fullPath} />}
    </div>
  );
};

export default AttachmentItem;
