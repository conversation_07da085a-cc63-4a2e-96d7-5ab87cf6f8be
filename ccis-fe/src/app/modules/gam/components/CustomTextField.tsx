import { FC, useState, useEffect, ChangeEvent, FocusEvent } from "react";
import TextField, { TTextFieldProps } from "@components/form/TextField";

// Extend the existing TextField props and add suggestion-specific ones
type CustomTextFieldProps = TTextFieldProps & {
  suggestionOptions: any[];
  getOptionLabel: (item: any) => string;
  getOptionValue: (item: any) => string | number;
  onInputChange?: (e: ChangeEvent<HTMLInputElement>) => void;
};

const CustomTextField: FC<CustomTextFieldProps> = ({ suggestionOptions, getOptionLabel, getOptionValue, value = "", onChange, name, onInputChange, ...props }) => {
  const [inputValue, setInputValue] = useState<string>(value.toString());
  const [filteredSuggestions, setFilteredSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  useEffect(() => {
    setInputValue(value?.toString() ?? "");
  }, [value]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    setInputValue(val);

    const filtered = suggestionOptions.filter((item) => getOptionLabel(item).toLowerCase().includes(val.toLowerCase()));
    setFilteredSuggestions(filtered);
    setShowSuggestions(true);
    onInputChange?.(e);
  };

  const handleSelect = (item: any) => {
    const label = getOptionLabel(item);
    setInputValue(label);
    setShowSuggestions(false);

    const fakeEvent = {
      target: {
        name: name || "",
        value: getOptionValue(item),
        item,
      },
    } as unknown as ChangeEvent<HTMLInputElement>;

    if (onChange) {
      onChange(fakeEvent);
    }
  };

  const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
    if (props.onBlur) props.onBlur(e);
    setTimeout(() => setShowSuggestions(false), 100);
  };

  useEffect(() => {
    if (suggestionOptions.length > 0 && value) {
      // If value is a raw ID, find the matching item and display its label
      const matchedItem = suggestionOptions.find((item) => getOptionValue(item).toString() === value?.toString());

      if (matchedItem) {
        setInputValue(getOptionLabel(matchedItem));
      } else {
        setInputValue(value?.toString() ?? "");
      }
    }
  }, [value, suggestionOptions]);

  useEffect(() => {
    if (inputValue.trim() === "") {
      setFilteredSuggestions([]);
      return;
    }

    const filtered = suggestionOptions.filter((item) => getOptionLabel(item).toLowerCase().includes(inputValue.toLowerCase()));
    setFilteredSuggestions(filtered);

    // Only show suggestions if there's something to show
    setShowSuggestions(filtered.length > 0);
  }, [inputValue, suggestionOptions]);

  return (
    <div className="relative w-full">
      <TextField name={name} value={inputValue} onChange={handleChange} onBlur={handleBlur} {...props} />

      {showSuggestions && filteredSuggestions.length > 0 && (
        <ul className="absolute list-none top-full left-0 z-10 w-full bg-base-100 border border-base-300 shadow rounded-box mt-1 max-h-60 overflow-auto">
          {filteredSuggestions.map((item, i) => (
            <li key={i} className=" py-2 hover:bg-base-200 cursor-pointer text-sm" onMouseDown={() => handleSelect(item)}>
              {getOptionLabel(item)}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default CustomTextField;
