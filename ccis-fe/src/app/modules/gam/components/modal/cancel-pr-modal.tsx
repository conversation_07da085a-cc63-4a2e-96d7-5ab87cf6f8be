import Button from "@components/common/Button";
import FileDropzone from "@components/common/FileDropzone";
import Modal from "@components/common/Modal";
import Typography from "@components/common/Typography";
import TextArea from "@components/form/TextArea";
import TextField from "@components/form/TextField";
import { formatWordDateDDMMYYY } from "@helpers/date";
import { FaCloudArrowUp } from "react-icons/fa6";
import { MdDeleteForever } from "react-icons/md";
import AttachmentItem from "../AttachmentItem";
import { FormikProps } from "formik";
import { TCancelReceipt } from "@state/types/form-inventory-transmittal";

type CancelModalProps = {
  isFormOpen: boolean;
  handleToggleFormModal: () => void;
  prDate: string;
  files: Array<File>;
  imagePreview: string | ArrayBuffer | null;
  handleFile: (acceptedFiles: File[]) => void;
  handleClear: () => void;
  isAllowedSize: boolean;
  formik: FormikProps<TCancelReceipt>;
};

const CancelPRModal = ({ isFormOpen, handleToggleFormModal, prDate, files, imagePreview, handleFile, handleClear, isAllowedSize, formik }: CancelModalProps) => {
  return (
    <Modal title="Cancel Receipt" modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={isFormOpen} onClose={handleToggleFormModal}>
      <form onSubmit={formik.handleSubmit}>
        <div className="grid grid-cols-2 gap-4">
          <div className=" flex flex-col gap-2">
            <span className="text-sm text-[#474747CC]">PR Date</span>
            <TextField name="issuedBy" size="sm" placeholder={"PR Date"} value={formatWordDateDDMMYYY(prDate, true)} onChange={() => {}} disabled />
          </div>

          <div className=" flex flex-col gap-2">
            <span className="text-sm text-[#474747CC]">Cancel Date</span>
            <TextField name="cancelledAt" size="sm" placeholder={"Cancel Date"} value={formatWordDateDDMMYYY(formik.values.cancelledAt, true)} onChange={() => {}} disabled />
          </div>

          <div className=" flex flex-col gap-2 col-span-3">
            <span className="text-sm text-[#474747CC]">Remarks</span>
            <TextArea name="remarks" placeholder="Your remarks..." value={formik.values.remarks} onChange={formik.handleChange} />
            {formik.touched.remarks && formik.errors.remarks && <span className="text-red-500">{formik.errors.remarks}</span>}
          </div>

          <div className=" flex flex-col gap-2 col-span-3">
            <span className="text-sm text-[#474747CC]">Please scan or upload attachments:</span>
            {formik.values.attachments.length !== 0 && formik.values.attachments.length < 2 ? (
              imagePreview ? (
                <div className="flex items-center gap-2">
                  <img src={imagePreview as string} alt="Image Preview" width={200} />
                  <Button variant="danger" classNames="rounded-full" onClick={handleClear}>
                    <MdDeleteForever />
                  </Button>
                </div>
              ) : (
                <div className="">
                  {formik.values.attachments.map((attachment) => (
                    <div key={attachment.file?.name} className="flex items-center gap-2">
                      <AttachmentItem filename={attachment.file?.name} mimeType={attachment.file?.type} size={attachment.file?.size} isForViewing={true} />
                      <Button variant="danger" classNames="rounded-full" onClick={handleClear}>
                        <MdDeleteForever />
                      </Button>
                    </div>
                  ))}
                </div>
              )
            ) : (
              <>
                <div className="bg-sky-100 border border-dashed rounded cursor-pointer">
                  <FileDropzone height={150} setFiles={handleFile}>
                    <div className="flex flex-1 flex-col items-center">
                      <FaCloudArrowUp size={30} className="mb-4" color="sky-500" />
                      <Typography size="xs">Drop files here or Click to upload</Typography>
                      <Typography className="text-slate-400">Supported formats: JPEG, PNG, PDF</Typography>
                    </div>
                  </FileDropzone>
                </div>
                {formik.touched.attachments && formik.errors.attachments && (
                  <span className="text-red-500">
                    {typeof formik.errors.attachments === "string"
                      ? formik.errors.attachments
                      : Array.isArray(formik.errors.attachments)
                        ? formik.errors.attachments.map((error, index) => {
                            if (typeof error === "object" && error !== null && "file" in error) {
                              return <div key={index}>{error.file}</div>;
                            }
                            return null;
                          })
                        : null}
                  </span>
                )}
              </>
            )}

            {files.length >= 2 && (
              <Typography size="sm" className="text-center text-danger">
                Minimum of 1 image or file only
              </Typography>
            )}

            {!isAllowedSize && (
              <Typography size="sm" className="text-center text-danger">
                Image size exceeds 20MB. Please select 20MB below file.
              </Typography>
            )}
          </div>

          <div className="flex flex-col gap-2 col-span-3 mt-5">
            <Button variant="primary" type="submit">
              Submit
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default CancelPRModal;
