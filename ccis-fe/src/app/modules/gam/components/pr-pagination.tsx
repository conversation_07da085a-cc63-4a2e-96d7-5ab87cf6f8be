import { IoChevronBackOutline, IoChevronForwardOutline } from "react-icons/io5";

type paginationProps = {
  handleForward: () => void;
  handleBack: () => void;
  seriesNo: number;
  seriesFrom: number;
};

const PrPagination = ({ handleForward, handleBack, seriesNo, seriesFrom }: paginationProps) => {
  const calculateSeries = () => {
    const seriesNum = seriesNo - seriesFrom + 1;
    return seriesNum.toString();
  };

  return (
    <div className="w-full flex justify-end">
      <div className="flex">
        <div onClick={handleBack} className="border border-[#a3a3a3] rounded-l-lg py-2 px-2 flex items-center justify-center cursor-pointer">
          <IoChevronBackOutline />
        </div>
        <div className="border-t border-b border-[#a3a3a3] py-2 px-4 flex items-center justify-center">{calculateSeries()}</div>
        <div onClick={handleForward} className="border rounded-r-lg py-2 px-2 border-[#a3a3a3] flex items-center justify-center cursor-pointer">
          <IoChevronForwardOutline />
        </div>
      </div>
    </div>
  );
};

export default PrPagination;
