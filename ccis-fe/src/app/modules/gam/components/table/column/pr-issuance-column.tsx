import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IPadAssignments } from "@interface/form-inventory.interface";
import { BsThreeDots } from "react-icons/bs";
import { getData, hasKey, saveData } from "@helpers/storage";
import ProgressBar from "../../progress-bar";
import { PadStatus } from "@enums/form-status";

type GetActionEventsFn = (row: IPadAssignments) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
};

export type TPadData = { padId: number; usedPads: number };

export const getColumns = ({ getActionEvents }: GetColumnsParams): TableColumn<IPadAssignments>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IPadAssignments>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        return row?.form ? row?.form?.division.divisionName : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Series From",
      cell: (row) => {
        const seriesFrom = row?.seriesFrom;
        return seriesFrom ? seriesFrom : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Series To",
      cell: (row) => {
        const seriesTo = row?.seriesTo;
        return seriesTo ? seriesTo : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => {
        let counts = { used: 0, unused: 0 };
        if (row?.padSeriesDetails) {
          counts = row?.padSeriesDetails.reduce(
            (pad, padDetails) => {
              if (padDetails.status === PadStatus.USED || padDetails.status === PadStatus.CANCELLED) {
                pad.used++;
              } else if (padDetails.status === PadStatus.UNUSED) {
                pad.unused++;
              }
              return pad;
            },
            { used: 0, unused: 0 }
          );

          // currentPad structure
          const rowPad: TPadData = {
            padId: row.id,
            usedPads: counts.used,
          };

          // check if current count used pad is not equal to 50
          if (counts.used !== 50) {
            // Check if the currentPad exist
            if (hasKey("currentPad")) {
              // Retrieve the currentPad in localStorage
              const currentPad: TPadData | null = getData<TPadData>("currentPad");

              // check if currentPad is not null
              if (currentPad !== null) {
                // if currentPad is not equal to 50 used pads
                if (currentPad.padId !== row.id && currentPad.usedPads === 50) {
                  saveData<TPadData>("currentPad", rowPad);
                }
              }
            } else {
              saveData<TPadData>("currentPad", rowPad);
            }
          }
        }

        return <ProgressBar value={counts.used} max={row.padSeriesDetails?.length} />;
      },
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        const currentPad: TPadData | null = getData<TPadData>("currentPad");

        return hasKey("currentPad") && currentPad !== null && currentPad.padId === row.id ? (
          <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />
        ) : (
          <div className="w-full cursor-not-allowed flex items-center justify-center">
            <BsThreeDots />
          </div>
        );
      },
    });
  }

  return columns;
};
