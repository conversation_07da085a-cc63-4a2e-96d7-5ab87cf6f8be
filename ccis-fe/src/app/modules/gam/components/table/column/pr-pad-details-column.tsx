import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IPadSeriesDetails } from "@interface/form-inventory.interface";
import { getTextStatusColor } from "@helpers/text";
import { PadStatus } from "@enums/form-status";
import { BsThreeDots } from "react-icons/bs";
import { formatNumber } from "@helpers/format-number";

type GetActionEventsFn = (row: IPadSeriesDetails) => IActions<any>[];

type GetColumnsParams = {
  getActionEvents?: GetActionEventsFn;
  usablePad: number;
};

export const getColumns = ({ getActionEvents, usablePad }: GetColumnsParams): TableColumn<IPadSeriesDetails>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IPadSeriesDetails>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "PR #",
      cell: (row) => {
        return row?.seriesNo ? row?.seriesNo : "-----";
      },
      ...commonSetting,
    },
    {
      name: "Coop No",
      cell: (row) => {
        return row?.cooperative !== null ? row?.cooperative?.id : "-----";
      },
      ...commonSetting,
    },
    {
      name: "Coop Name",
      cell: (row) => {
        return (
          <p className="line-clamp-2" title={row?.cooperative !== null ? row?.cooperative?.coopName : "-----"}>
            {row?.cooperative !== null ? row?.cooperative?.coopName : "-----"}
          </p>
        );
      },
      ...commonSetting,
    },
    {
      name: "Coop Branch",
      cell: (row) => {
        return row?.cooperative !== null ? row?.cooperative?.branchName : "-----";
      },
      ...commonSetting,
    },
    {
      name: "Product Name",
      cell: (row) => {
        return row?.product !== null ? row?.product?.name : "-----";
      },
      ...commonSetting,
    },
    {
      name: "Amount",
      cell: (row) => {
        return row?.paymentDetail !== null ? formatNumber(row?.paymentDetail?.amount || 0, true) : "-----";
      },
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => {
        return (
          <Typography size="xs" className={getTextStatusColor(row?.status ? row?.status : PadStatus.UNUSED)}>
            {row?.status && row?.status !== PadStatus.UNUSED
              ? row?.status
                  .replace(/_/g, " ")
                  .toLowerCase()
                  .replace(/\b\w/g, (char) => char.toUpperCase())
              : "-----"}
          </Typography>
        );
      },
    },
    {
      name: "OR #",
      cell: (row) => {
        return row?.orNumber !== null ? row?.orNumber : "-----";
      },
      ...commonSetting,
    },
    {
      name: "OR Date",
      cell: (row) => {
        return row?.orDate !== null ? row?.orDate : "-----";
      },
      ...commonSetting,
    },
  ];

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return row.id !== usablePad && row.status === PadStatus.UNUSED ? (
          <div className="w-full cursor-not-allowed flex items-center justify-center">
            <BsThreeDots />
          </div>
        ) : (
          <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />
        );
      },
    });
  }

  return columns;
};
