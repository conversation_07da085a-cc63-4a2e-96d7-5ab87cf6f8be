import React from "react";
import GenericInventoryTab from "@components/template/GenericInventoryTab";
import { RoleType } from "@enums/form-status";
import { useInventoryMetrics } from "@components/template/InventoryMetricType";

const InventoryTab: React.FC = () => {
  const { returnedMetrics } = useInventoryMetrics();
  return (
    <GenericInventoryTab
      userRole={RoleType.GAM}
      title="INVENTORY (ON-HAND)"
      description="This page lists all forms across all received series. Use filters to narrow down results by status, division, or type."
      customMetrics={returnedMetrics}
    />
  );
};

export default InventoryTab;
