import React from "react";
import Modal from "@components/common/Modal";

import LoadingButton from "@components/common/LoadingButton";
import { FaCheckCircle } from "react-icons/fa";
import { GoXCircleFill } from "react-icons/go";

type ApproverStatusModalProps = {
  isOpen: boolean;
  status: boolean;
  onClose: () => void;
  onSubmit?: () => void;
  isSubmitting?: boolean;
};
const ApproverStatusForm: React.FC<ApproverStatusModalProps> = ({ isOpen, onClose, status, onSubmit, isSubmitting }) => {
  return (
    <Modal title={status ? "Approve" : "Reject"} modalContainerClassName="max-w-3xl" titleClass="text-black  text-[30px]" isOpen={isOpen} onClose={onClose} modalBgColor="!bg-slate-100">
      <div className="flex flex-col gap-4 bg-slate-100">
        <div className="flex justify-center items-center w-full h-full  bg-slate-100">
          {status ? <FaCheckCircle size={60} color="green" className="align-center" /> : <GoXCircleFill size={80} color="red" className="align-center" />}
        </div>
        <div className="flex justify-center items-center w-full mt-3 h-full bg-slate-100 text-[30px]">{status ? "Approve Commission Structure" : "Reject Commission Structure"}</div>
        <div className="flex justify-center items-center w-full mt-5 h-full bg-slate-100 text-[20px]">{`Are you sure you want to ${status ? `Approve` : `Reject`} this commission structure?`}</div>
        <div className="flex flex-row justify-center w-full bg-slate-100">
          <div className="flex justify-center mt-10 gap-4 w-full">
            <div className="flex items-center gap-2 w-1/4">
              <LoadingButton type="submit" variant="primary" className="bg-zinc-500" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </LoadingButton>
            </div>
            <div className="flex items-center gap-2 w-1/4">
              <LoadingButton type="submit" variant={status ? "success" : "danger"} onClick={onSubmit} isLoading={isSubmitting}>
                {status ? "Approved" : "Rejected"}
              </LoadingButton>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ApproverStatusForm;
