import React, { useEffect } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import Modal from "@components/common/Modal";
import Button from "@components/common/Button";
import { toast } from "react-toastify";
import Select from "@components/form/Select";
import { ISelectOptions } from "@interface/common.interface";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IProductProposalCommissionApproval } from "@interface/product-proposal.interface";
import { ProductProposalCommisionApprovalSchema } from "@services/product-proposal/product-proposal.schema";

type ApproverModalProps = {
  isOpen: boolean;
  initialValues: IProductProposalCommissionApproval;
  onClose: () => void;
  onSubmit: (values: IProductProposalCommissionApproval) => void;
  signatoryTemplateOption: ISelectOptions[];
  onTemplateChange: (selectedSignatoryTemplate: number) => void;
};
const ApproverForm: React.FC<ApproverModalProps> = ({ isOpen, initialValues, onClose, onSubmit, signatoryTemplateOption, onTemplateChange }) => {
  const signatoryUsers = useSelector((state: RootState) => state.utilitiesSignatoryTemplate?.selectedSignatoryTemplate?.data);
  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: ProductProposalCommisionApprovalSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        await onSubmit(values);
        resetForm();
      } catch (error) {
        toast.error(`Error submitting form: ${(error as any).message}`);
      }
    },
  });
  useEffect(() => {
    if (formik.errors) {
      Object.values(formik.errors).forEach((error) => {
        toast.error(error as string);
      });
    }
  }, [formik.errors]);
  return (
    <Modal title={"Approver"} modalContainerClassName="max-w-3xl" titleClass="text-black  text-[30px]" isOpen={isOpen} onClose={onClose}>
      <div className="flex flex-row pb-6 border-b border-zinc-300 text-[18px] text-slate-500">Please Select the designated signatory to validate this request.</div>
      <FormikProvider value={formik}>
        <Form className="min-w-full my-4">
          <div className="flex-none w-full my-4">
            <div className="flex flex-row gap-2 mt-6">
              <label className=" pb-3">Select Approver</label>
            </div>

            <Select
              name="signatoryTemplateId"
              placeholder="Select "
              options={signatoryTemplateOption}
              onChange={(e) => {
                e.target.options[e.target.selectedIndex].text;
                onTemplateChange(Number(e.target.value));
              }}
              onBlur={formik.handleBlur}
              required
            />
          </div>
          <div className="flex-row w-full mt-8 bg-zinc-100 rounded  ">
            {signatoryUsers.id !== 0 && <label className="text-[20px] ">Selected Approver</label>}

            {signatoryUsers?.signatoriesTemplateUsers?.map((user) => {
              return (
                <div className="bg-zinc-100 w-full mt-4 " key={user.id}>
                  {user.user?.profilePicturePath ? (
                    <>
                      <div className="flex flex-row justify-start w-full mt-2">
                        <div className="w-15  mr-5">
                          <img src={`${import.meta.env.VITE_AWS_S3_ENDPOINT}/${user.user.profilePicturePath}`} alt="User Profile" className="rounded-full w-8 h-8 sm:w-10 sm:h-10 object-cover" />
                        </div>
                        <div className="flex flex-row justify-start items-center text-center w-full">
                          {` ${user?.user?.firstname} ${user?.user?.middlename} ${user?.user?.lastname} - ${user?.user?.position?.positionName}`.trim()}
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex flex-row justify-start w-full mt-2 ">
                        <div className="text-sm  mr-5 sm:text-sm text-center text-black bg-slate-400 h-8 w-8 sm:h-9 sm:w-10 flex items-center justify-center rounded-full">
                          {user.user?.firstname[0]}
                          {user.user?.lastname[0]}
                        </div>
                        <div className="flex flex-row justify-start items-center text-center w-full">
                          {` ${user?.user?.firstname} ${user?.user?.middlename} ${user?.user?.lastname} - ${user?.user?.position?.positionName}`.trim()}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              );
            })}
          </div>
          {signatoryUsers.id !== 0 && (
            <div className="flex justify-around mt-10 w-full">
              <div className="flex items-center gap-2">
                <Button variant="primary" classNames="rounded-xl justify-center w-32 p-4 bg-zinc-400  py-4" onClick={onClose}>
                  Cancel
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button type="submit" variant="primary" classNames=" rounded-xl justify-center w-32 py-4">
                  Set
                </Button>
              </div>
            </div>
          )}
        </Form>
      </FormikProvider>
    </Modal>
  );
};

export default ApproverForm;
