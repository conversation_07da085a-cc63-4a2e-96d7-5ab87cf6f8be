import { FC, useState, useEffect, useRef } from "react";
import Button from "@components/common/Button";
import { BiExport } from "react-icons/bi";
import { TbFileUpload } from "react-icons/tb";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import FileDropzone from "@components/common/FileDropzone";
import { FaChevronRight, FaCloudArrowUp } from "react-icons/fa6";
import Typography from "@components/common/Typography";
import { Form, FormikProvider, useFormik } from "formik";
import { AxiosResponse } from "axios";
import { showSuccess } from "@helpers/prompt";
import { postProductProposalAgreement } from "@services/proposal/proposal.service";
import dayjs from "dayjs";
import { formatDate } from "@helpers/date";
import httpClient from "@clients/httpClient";
import { apiUrl } from "@services/variables";
import Loader from "@components/Loader";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { agreementSchema } from "@services/product-proposal/product-proposal.schema";
interface ProductProposalProps {
  data: any;
}

const PartnershipAgreement: FC<ProductProposalProps> = ({ data }) => {
  const [signedPartnershipAgreementModal, setSignedPartnershipAgreementModal] = useState<boolean>();

  const handleSignedPartnershipAgreementModal = () => {
    setSignedPartnershipAgreementModal((prev) => !prev);
  };
  const cooperativeOfficers = data?.cooperative?.cooperativeOfficers;
  const [files, setFiles] = useState<Array<File>>([]);
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null>(null);
  const contentRef = useRef(null);
  const [processing, setProcessing] = useState<boolean>(false);
  const [processModal, setProcessModal] = useState<boolean>(false);
  const toggleProcess = () => setProcessModal((prev) => !prev);
  const [activeSection, setActiveSection] = useState<string>("");
  // Find primary signatory
  const primarySignatory = cooperativeOfficers?.[0]; // Select the first officer
  const [pdfFile, setPdfFile] = useState<string>();
  // Find the secondary signatory, ensuring it's not the same as the primary
  const secondarySignatory = cooperativeOfficers?.find((officer: any) => officer !== primarySignatory);

  // Format officer name with proper checks
  const formatOfficerName = (officer: any) => {
    if (!officer) return "___________________________";
    const title = officer.title ? `${officer.title} ` : "";
    const firstName = officer.firstName || "";
    const middleName = officer.middleName ? `${officer.middleName} ` : "";
    const lastName = officer.lastName || "";
    const generation = officer.generation ? ` ${officer.generation}` : "";

    return `${title}${firstName} ${middleName}${lastName}${generation}`.trim() || "___________________________";
  };

  // Get position name with fallback
  const getPositionName = (officer: any) => {
    return officer?.position?.positionName || "";
  };
  //Sections in Column 2
  const sections = {
    appointment: { label: "Appointment", ref: useRef<HTMLDivElement>(null) },
    responsibility: {
      label: "Coop Partners Responsibility",
      ref: useRef<HTMLDivElement>(null),
    },
    climbs: {
      label: "Climbs Responsibilities",
      ref: useRef<HTMLDivElement>(null),
    },
    mutual: { label: "Mutual Obligations", ref: useRef<HTMLDivElement>(null) },
    relationship: {
      label: "Relationship of the Parties",
      ref: useRef<HTMLDivElement>(null),
    },
    confidentiality: {
      label: "Confidentiality Agreement",
      ref: useRef<HTMLDivElement>(null),
    },
    indemnification: {
      label: "Indemnification Agreement",
      ref: useRef<HTMLDivElement>(null),
    },
    termination: {
      label: "Termination of Contract",
      ref: useRef<HTMLDivElement>(null),
    },
    amendments: {
      label: "Amendments and venue of suit",
      ref: useRef<HTMLDivElement>(null),
    },
    separability: {
      label: "Separability clause",
      ref: useRef<HTMLDivElement>(null),
    },
  };

  const scrollToSection = (sectionRef: React.RefObject<HTMLDivElement>, sectionName: string) => {
    sectionRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
    setActiveSection(sectionName);
  };

  const handleExport = async () => {
    try {
      setProcessing(true);
      toggleProcess();

      const id = data?.id;
      const response: any = await httpClient.get(`${apiUrl}/product-proposals/${id}/export/agreement/pdf`, { responseType: "blob" });

      const pdfBlob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(pdfBlob);
      setPdfFile(url);
    } catch (error) {
      console.log(error);
    } finally {
      setProcessing(false);
    }
  };

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles(acceptedFiles);

    const file = new FileReader();
    file.onload = () => {
      setImagePreview(file.result);
    };

    file.readAsDataURL(acceptedFiles[0]);
  };

  useEffect(() => {
    if (files) {
      const fileArray = Array.from(files).map((file) => ({
        file: file,
        label: file.name,
        description: "Description here",
      }));

      formik.setFieldValue("attachments", fileArray);
    }
  }, [files]);

  const formik = useFormik({
    initialValues: {
      agreementSignedDate: "",
      agreementSignedRemarks: "",
      status: "SIGNED",
      attachments: [],
    },
    validationSchema: agreementSchema,
    onSubmit: async (values) => {
      try {
        const status: AxiosResponse = await postProductProposalAgreement(data?.id as number, values);

        if (status) {
          handleSignedPartnershipAgreementModal();
          showSuccess("Success", "Product Proposal Agreement has been updated!").then((result) => {
            if (result.isConfirmed) {
              window.location.reload();
            }
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
  });

  return (
    <div>
      {signedPartnershipAgreementModal && (
        <Modal isOpen={signedPartnershipAgreementModal} onClose={handleSignedPartnershipAgreementModal} modalContainerClassName="max-w-3xl">
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <div className="w-full flex flex-col  ">
                <div className=" flex items-center justify-center text-center mb-10">
                  {" "}
                  <TbFileUpload size={50} className="text-primary" />
                </div>

                <div className="text-3xl text-center font-poppins-semibold mb-10"> Partnership Agreement</div>

                <div>
                  <label className="text-zinc-400">Please provide the coop signing date.</label>
                  <TextField
                    name="agreementSignedDate"
                    type="date"
                    value={formik.values.agreementSignedDate}
                    error={formik.touched.agreementSignedDate && !!formik.errors.agreementSignedDate}
                    errorText={formik.errors.agreementSignedDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    required
                  />
                </div>

                <div className="mt-4">
                  <label>Upload Signed Partnership Agreement</label>
                  <div className="border border-zinc-400 rounded-xl mt-2 w-full h-60 flex items-center justify-center">
                    <FileDropzone setFiles={handleFile} height={200}>
                      {files.length === 0 && (
                        <div className="flex flex-1 flex-col items-center">
                          <FaCloudArrowUp size={30} className="mb-4" />
                          <Typography>Click or drag and drop to upload your profile</Typography>
                          <Typography className="text-slate-400">PNG, JPG (Max 20MB)</Typography>
                        </div>
                      )}

                      {imagePreview && (
                        <div className="flex flex-1 flex-col items-center  p-4 mb-4 rounded-md h-full w-full  ">
                          <img src={imagePreview as string} alt="Image Preview" className="w-full h-full object-contain" />
                        </div>
                      )}
                    </FileDropzone>
                  </div>
                  {formik.touched.attachments && formik.errors.attachments && <div className="text-red-500 text-sm mt-2">{formik.errors.attachments}</div>}
                </div>
                <div className="flex items-center justify-center gap-4 w-full mt-4">
                  <Button classNames="w-40 rounded-lg bg-zinc-500" onClick={handleSignedPartnershipAgreementModal}>
                    Cancel
                  </Button>
                  <Button type="submit" classNames="w-40 rounded-lg bg-info">
                    Submit
                  </Button>
                </div>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}
      {processModal && (
        <Modal isOpen={processModal} onClose={toggleProcess} showCloseButton={!processing} modalContainerClassName="!max-w-6xl">
          {processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <Loader />
              <Typography>Processing...</Typography>
            </div>
          )}
          {!processing && (
            <div className="flex flex-1 flex-col justify-center items-center">
              <embed src={pdfFile} className="h-[500px]" width="100%" />
            </div>
          )}
        </Modal>
      )}

      <div className="p-4">
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name ?? ""} </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Date Notarized</div>
              <div className="w-2/3 text-black">
                <div className="w-2/3 text-black">{formatDate(data?.proposalNotarization?.agreementNotarizationDate, "d MMMM yyyy") ?? "----"}</div>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Notarized Status</div>
              <div className="w-2/3 text-black">
                {" "}
                {/* <span
                  className={`p-1 rounded-md text-sm px-4 ${
                    data?.proposalNotarization?.agreementNotarizationStatus === "NOTARIZED"
                      ? "bg-green-50 text-green-400"
                      : data?.proposalNotarization?.agreementNotarizationStatus === "FOR_REVISION"
                      ? "bg-red-50 text-red-400"
                      : "bg-yellow-50 text-yellow-400"
                  }`}
                >
                  {capitalizeFirstLetterWords(data.proposalNotarization?.agreementNotarizationStatus || "Pending", "_")}
                </span> */}
                <span className={`${getTextStatusColor(data.proposalNotarization?.agreementNotarizationStatus || "PENDING")}`}>
                  {capitalizeFirstLetterWords(data.proposalNotarization?.agreementNotarizationStatus || "PENDING")}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end mb-2">
        <Button onClick={handleExport} disabled={processing} type="button" variant="primary" outline classNames="text-xs flex items-center justify-center gap-2">
          <BiExport size={18} className="flex items-center justify-center" />
          {processing ? "Processing..." : "Export"}
        </Button>
      </div>
      <div className="min-h-[50rem] w-full flex  justify-center gap-4  ">
        <div className="w-1/3 min-h-[50rem] border border-zinc-200 p-4 ">
          {/* COL1 */}
          <div className="w-72 xl:flex flex-col text-start text-sm relative">
            <div className="w-full text-sm text-zinc-400 font-poppins-semibold py-4">Partnership Agreement</div>
            {Object.entries(sections).map(([key, { label, ref }]) => (
              <details key={key} className="group">
                <summary
                  onClick={() => scrollToSection(ref, key)}
                  className={`w-full items-center text-zinc-500 hover:bg-zinc-100 p-4 rounded-md cursor-pointer flex gap-2 ${activeSection === key ? "font-poppins-semibold text-black" : ""}`}
                >
                  <FaChevronRight />
                  {label.toUpperCase().replace("_", " ")}
                </summary>
              </details>
            ))}
          </div>
        </div>
        {/* COL2 */}
        <div ref={contentRef} className="w-full min-h-[50rem] h-96 border border-zinc-200 p-6 pt-6 text-justify overflow-y-auto scroll-mt-96">
          <p className="text-center font-poppins-semibold text-xl"> SALES PARTNERSHIP AGREEMENT </p>
          <br />
          <p className="font-poppins-semibold"> KNOW ALL MEN BY THESE PRESENTS </p>
          <br />
          <p>
            This MEMORANDUM OF AGREEMENT entered into this day of{" "}
            <span className="font-poppins-semibold">
              {" "}
              {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "d") : "______"},{" "}
              {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "yyyy") : "______"} at{" "}
              {data?.proposalNotarization?.agreementNotarizationDate ? formatDate(data.proposalNotarization.agreementNotarizationDate, "MMMM") : "______"}
            </span>{" "}
            by and between:
          </p>
          <br />
          <p>
            <span className="font-poppins-semibold">CLIMBS LIFE AND GENERAL INSURANCE COOPERATIVE</span> , a cooperative duly organized and existing under and by virtue of the laws of the Republic of
            the Philippines with principal office located at CLIMBS Compound, Upper Zone 5, National Highway, Brgy. Bulua, 9000 Cagayan de Oro City, and duly represented in this act by its President
            &amp; Chief Executive Officer, <span className="font-poppins-semibold">MR. NOEL D. RABOY</span> , and hereinafter referred to as <span className="font-poppins-semibold">CLIMBS</span> ;
            <span className="flex justify-center p-4 font-poppins-semibold"> AND</span>
            <span className="font-poppins-semibold">{data?.cooperative?.coopName} </span> , a cooperative duly organized and existing under and by virtue of the laws of the Republic of the Philippines
            with principal office located at
            {""}{" "}
            <span className="font-poppins-semibold">
              {data?.cooperative?.streetAddress}, {data?.cooperative?.province}, {data?.cooperative?.city}{" "}
            </span>{" "}
            , represented herein by its Chairperson, _____________________, hereinafter referred to as <span className="font-poppins-semibold">COOP PARTNER</span>;
          </p>
          <br />
          <p className="text-center"> WITNESSETH:</p>
          <p>
            WHEREAS, <span className="font-poppins-semibold">CLIMBS</span> is a composite insurance company duly authorized by the Philippine Insurance Commission to offer life insurance, non-life
            insurance and health care products.
            <br />
            <br />
            WHEREAS, the <span className="font-poppins-semibold">COOP PARTNER </span>is a multipurpose cooperative duly licensed by the Cooperative Development Authority to offer various mutual
            benefit products, which includes financial aid and lending services, to its members and is officially recognized as a cooperative member-investor of CLIMBS.
            <br />
            <br />
            WHEREAS, <span className="font-poppins-semibold">COOP PARTNER </span> agreed to sell and promote designated products and services of <span className="font-poppins-semibold">CLIMBS</span>{" "}
            as listed in ANNEX “A” hereof, which is considered an integral part of this Agreement, for the period and on the terms and conditions set forth herein.
            <br />
            <br />
            NOW, THEREFORE, for and in consideration of the foregoing premises, the Parties have agreed and by these presents do hereby agree as follows:
          </p>
          <br /> <br />
          <div>
            <div ref={sections.appointment.ref}>
              1. <span className="font-poppins-semibold underline">APPOINTMENT</span>
              <p className="mx-8">
                <br />
                <span className="font-poppins-semibold">CLIMBS</span> hereby appoints <span className="font-poppins-semibolf">COOP PARTNER</span> as an authorized distributor of{" "}
                <span className="font-poppins-semibold">CLIMBS Products</span> (hereinafter referred to as <span className="font-poppins-semibold">PRODUCT</span> ) with the non-assignable,
                non-exclusive right to promote and sell the same and the <span className="font-poppins-semibold">COOP PARTNER </span> hereby accepts such designation and appointment.
              </p>
            </div>
            <br /> <br />
            <div ref={sections.responsibility.ref}>
              2. <span className="font-poppins-semibold underline">COOP PARTNER’s RESPONSIBILITY</span>
              <p className="mx-8">
                <br />
                2.1 The <span className="font-poppins-semibold">COOP PARTNER</span> is responsible in designating a specific person/s within its organization to take charge of its Cooperative
                Insurance Program. This designated person/s shall be duly authorized and/or licensed to sell life and non-life insurance products in accordance with the prevailing requirements adopted
                by the Insurance Commission and <span className="font-poppins-semibold">CLIMBS</span>.
                <br /> <br />
                2.2 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to sell all Life Insurance <span className="font-poppins-semibold">PRODUCT</span> of{" "}
                <span className="font-poppins-semibold">CLIMBS</span> exclusively, to the exclusion of Life Insurance PRODUCTS of all other Life Insurance Companies and/or Cooperatives, and in
                accordance with the Insurance Code of the Philippines including all the existing rules and regulations of the Insurance Commission pertaining to Life Insurance Selling.
                <br /> <br />
                2.3 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to sell all other <span className="font-poppins-semibold">PRODUCT</span> of{" "}
                <span className="font-poppins-semibold">CLIMBS</span> and its subsidiary/ies in accordance to its established sales guidelines, and to fully cooperate with{" "}
                <span className="font-poppins-semibold">CLIMBS</span> in promoting and marketing the said PRODUCTS among its members and other identified markets of which the cooperative intends to
                penetrate.
                <br /> <br />
                2.4 The <span className="font-poppins-semibold">COOP PARTNER</span> shall sell all the <span className="font-poppins-semibold">PRODUCTS</span> strictly in accordance to the prescribed
                provisions of the insurance policy proposed and shall not in any manner promise, propose, promote, or represent any product feature or benefit not covered under the said insurance
                policy. Master Policies issued by <span className="font-poppins-semibold">CLIMBS</span>, if any.
                <br /> <br />
                2.5 The <span className="font-poppins-semibold">COOP PARTNER</span> shall obtain from <span className="font-poppins-semibold">CLIMBS</span> all sales and underwriting training to
                effectively market, promote and sell the <span className="font-poppins-semibold">PRODUCTS</span>
                .
                <br />
                2.6 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to:
                <br /> <br />
                2.6.1 Attend sales meetings and training seminars as may be required and scheduled by <span className="font-poppins-semibold">CLIMBS </span> from time to time; and
                <br /> <br />
                2.6.2 Provide sales forecasts, and sales reports as may be requested by <span className="font-poppins-semibold">CLIMBS </span> periodically.
                <br /> <br />
                2.7 The <span className="font-poppins-semibold">COOP PARTNER</span> assumes all sales and marketing expenses in selling the PRODUCTS to its prospects.
                <br />
                2.8 The <span className="font-poppins-semibold">COOP PARTNER</span> shall be responsible in transmitting to <span className="font-poppins-semibold">CLIMBS </span> all individual
                insurance applications for policy issuance. Likewise, for group accounts, the <span className="font-poppins-semibolf">COOP PARTNER</span> shall be responsible in encoding the list of
                insured to the data base system provided by <span className="font-poppins-semibold">CLIMBS </span> , or in case no such data base system is provided by{" "}
                <span className="font-poppins-semibold">CLIMBS </span> , to encode in its own data base system the list of insured and accordingly transmit the same to{" "}
                <span className="font-poppins-semibold">CLIMBS </span> within a reasonable period of time from its completion.
                <br /> <br />
                2.9 The <span className="font-poppins-semibold">COOP PARTNER</span> shall be responsible in collecting all insurance premiums from its own clients and to remit the same to CLIMBS
                within 15 days of the following month. Premium remittance to <span className="font-poppins-semibold">CLIMBS </span> may be done via: 1. deposit to{" "}
                <span className="font-poppins-semibold">CLIMBS </span> account; 2. Pera Padala; or other payment method, online or otherwise, available to PARTNERSHIP AGREEMENT 3 the _____________
                PARTNER taking into special account its own peculiar situation and location. The corresponding deposit slip shall be scanned, or photographed and transmitted to CLIMBS within the same
                day the premium is deposited for immediate receipting.
                <br /> <br />
                2.10 That both parties agree the schedule of reporting and remittance of all transaction and collection shall not be later than 15th day of the succeeding month together with the
                corresponding remittance report with list of enrollees, date of birth, gender,loan amount, date of loan release, term of coverage, maturity of coverage, premium and list of
                beneficiaries. In event that the report and remittance is not remitted within the prescribed period, <span className="font-poppins-semibold">CLIMBS </span> shall not be liable of any
                claim. The submission of remittance listing can be uploaded through the online remittance system of <span className="font-poppins-semibold">CLIMBS </span> .
                <br /> <br />
                2.11 That in case of delayed premium refund due to disqualifications or adjustment, the provisions outlined in this contract shall prevail over any conflicting terms regarding claims
                arising from such delay. That <span className="font-poppins-semibold">CLIMBS </span> shall process claims based on terms and conditions of this contract, even if refund of premium has
                been delayed.
                <br /> <br />
                2.12 The <span className="font-poppins-semibold">COOP PARTNER</span> shall collect and collate all claim documents coming from its clients and shall only submit to{" "}
                <span className="font-poppins-semibold">CLIMBS </span> the complete set. Incomplete set of claim documents submitted to <span className="font-poppins-semibold">CLIMBS </span> shall not
                be processed and shall be returned to the <span className="font-poppins-semibold">COOP PARTNER</span> for completion.
                <br /> <br />
                2.13 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to turn over all claim proceeds to the beneficiaries of the insured and shall correspondingly have the
                receipt of the proceeds duly acknowledged by the beneficiaries and, at the same time, facilitate the procurement of the quit-claim documents from the same.
                <br /> <br />
                2.14 The <span className="font-poppins-semibold">COOP PARTNER</span> agrees to return all properties provided by <span className="font-poppins-semibold">CLIMBS </span> including
                software, supplies, collateral materials, price schedules etc. which have been provided to the
                <span className="font-poppins-semibold">COOP PARTNER</span> without charge, upon termination of this Agreement, or upon written request by{" "}
                <span className="font-poppins-semibold">CLIMBS </span> for whatever valid reasons.
                <br />
                2.15 For Life &amp; Non-life Products of <span className="font-poppins-semibold">CLIMBS </span> as stated in ANNEX “A”, once <span className="font-poppins-semibolf">COOP PARTNER</span>{" "}
                avail the <span className="font-poppins-semibolf">COOP PARTNER</span> said products for its members and/or borrowers, agrees to enroll 100% their qualified members with{" "}
                <span className="font-poppins-semibold">CLIMBS </span> exclusively.
              </p>
            </div>
            <br />
            <br />
            <div ref={sections.climbs.ref}>
              3. <span className="font-poppins-semibold underline">CLIMBS’s RESPONSIBILITIES</span>
              <br />
              <p className="mx-8">
                3.1 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide the PRODUCTS as may needed by the <span className="font-poppins-semibolf">COOP PARTNER</span> to
                distribute, the product collateral, and product trainings.
                <br /> <br />
                3.2 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide sales, marketing, administrative and underwriting training and seminars to the{" "}
                <span className="font-poppins-semibolf">COOP PARTNER</span>’s officers and staff for empowerment purposes. PARTNERSHIP AGREEMENT 4
                <br /> <br />
                3.3 <span className="font-poppins-semibold">CLIMBS </span> agrees to include <span className="font-poppins-semibolf">COOP PARTNER</span> in all of its sales contests and promotions and
                shall provide COOP PARTNER with updated information on how to qualify in the same.
                <br /> <br />
                3.4 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide a certificate of coverage to each group client that COOP PARTNER acquired indicating therein that the
                particular group and its members are covered under the Master Policy issued for a specific product availed of through the COOP PARTNER.
                <br /> <br />
                3.5 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide a claim settling facility to COOP PARTNER which will allow the settlement of claims within ten (10) to
                fifteen (15) working days from <span className="font-poppins-semibold">CLIMBS </span> receipt of the complete set of claim documents as specified under Section 2.12 hereof.
                <br /> <br />
                3.6 <span className="font-poppins-semibold">CLIMBS </span> agrees to provide a claims check, if required by the COOP PARTNER. The system of deposit and withdrawal of said fund shall be
                subject to a subsequent agreement between the COOP PARTNER and <span className="font-poppins-semibold">CLIMBS </span>.
                <br /> <br />
                3.7 <span className="font-poppins-semibold">CLIMBS </span> agrees to draw the claim check in the name of the beneficiary of the member of the COOP PARTNER.
                <br /> <br />
                3.8 <span className="font-poppins-semibold">CLIMBS </span> shall provide the COOP PARTNER with the complete management fee or each product. The management fee shall be the sole basis
                of the payment of <span className="font-poppins-semibold">CLIMBS </span> to the COOP PARTNER for its sales and marketing efforts. This Agreement confers no other benefit or
                compensation payments to the COOP PARTNER from <span className="font-poppins-semibold">CLIMBS </span> , unless the latter, from time to time, provides additional benefits or incentives
                to the former during special events like marketing contests and promotions. The management fees may be amended by <span className="font-poppins-semibold">CLIMBS </span> anytime subject
                to at least thirty (30) days prior written notice to the COOP PARTNER.
              </p>
            </div>
            <br /> <br />
            <div ref={sections.mutual.ref}>
              4. <span className="font-poppins-semibold underline">MUTUAL OBLIGATIONS</span>
              <p className="mx-8">
                4.1 The parties have agreed that all names of qualified persons submitted by COOP PARTNER for coverage shall be underwritten by <span className="font-poppins-semibold">CLIMBS </span>{" "}
                based on its standard underwriting practices and procedures.
                <br /> <br />
                4.2 <span className="font-poppins-semibold">CLIMBS </span> may decline to accept individual and/or group applications submitted by COOP PARTNER, for whatever cause which in CLIMBS’
                sole opinion renders such application unacceptable, including, but not limited to location hazards, political hazards, and moral hazards.
                <br /> <br />
                4.3 Each Group Master Policy issued under this Agreement shall be a contract between <span className="font-poppins-semibold">CLIMBS </span> and the COOP PARTNER. Third party group
                enrolees are deemed covered and bound by the governing provisions of the aforementioned Master Policy. Only a certificate of inclusion shall be issued to each third party group
                indicating therein that all its enrollees are covered under the applicable Master Policy.PARTNERSHIP AGREEMENT 5
                <br /> <br />
                4.4 The parties have agreed that for a third party group to be officially accepted as included in the CLIMBS - COOP PARTNER group insurance program, a minimum number of enrolees from
                the third party group is required. The minimum number enrollees from each new third party group shall be one hundred percent (100%) participation of all eligible members to maintain
                the insurance policy. The third group may then enrol subsequent members on a weekly basis with no minimum number required.
                <br /> <br />
                4.5 The COOP PARTNER shall make no representations and warranties to third party groups except those specifically indicated in the issued Master Policy.
                <br /> <br />
                4.6 Individual policies issued under this Agreement is a private contract between the <span className="font-poppins-semibold">CLIMBS </span> and the individual insured. The
                COOPPARTNER’s role in soliciting individual account is purely limited to sales, marketing and after sales services.
                <br /> <br />
                4.7 The parties agree and consent that <span className="font-poppins-semibold">CLIMBS </span> may collect, use and disclose personal data of insured members, as provided in this
                agreement, or obtained by <span className="font-poppins-semibold">CLIMBS </span> as a result of this agreement, and that COOP PARTNER and its insured members consent to do the same,
                for the following purposes in accordance with the Data Privacy Act of 2012: The processing and/or enrollment of policy of insured individual, and The processing of insurance claims of
                insured individual.
                <br /> <br />
                4.8 One (1) year contestability for pre-existing illness only: Any member-borrower renew his/her loan but has increased its loan amount, the excess amount will be contestable for one
                (1) year. Member-borrower insured whose last coverage have lapsed for more than twelve (12) months shall be considered as “new application” and shall be subject for contestability
                period of one (1) year and may be required to submit medical requirements as needed.
              </p>
            </div>
            <br /> <br />
            <div ref={sections.relationship.ref}>
              5. <span className="font-poppins-semibold underline">RELATIONSHIP OF THE PARTIES</span>
              <p className="mx-8">
                5.1 The COOP PARTNER and <span className="font-poppins-semibold">CLIMBS </span> agrees that the COOP PARTNER serves under this Agreement as an independent sales partner without power
                to bind, act for, or obligate <span className="font-poppins-semibold">CLIMBS </span> , whether by expression or implication except as specifically provided in this Agreement.
                <br /> <br />
                5.2 Each party hereto hereby agrees to indemnify and hold the other harmless against any negligent or intentional acts by the offending party, its representatives, employees, or
                contractees that cause damage or injury to third parties.
              </p>
              <br />
            </div>
            <br /> <br />
            <div ref={sections.confidentiality.ref}>
              6. <span className="font-poppins-semibold underline">CONFIDENTIALITY AGREEMENT</span>
              <p className="mx-8">
                <br />
                Each party agrees that it shall, at all times, regardless of termination of this Agreement, keep in strict confidence any and all information (proprietary or otherwise, written or
                verbal) relating to the other party and those of its affiliates, subsidiaries, directors, officers, employees and/or customers. Each party use utmost efforts to prevent any
                unauthorized disclosure or use of confidential information, applying the degree of care which applies to its own confidential information.
              </p>
            </div>
            <br /> <br />
            <div ref={sections.indemnification.ref}>
              7. <span className="font-poppins-semibold underline">INDEMNIFICATION AGREEMENT</span>
              <p className="mx-8">
                <br />
                Each party shall be liable and shall indemnify the other party, its affiliates, subsidiaries, officers, directors, shareholders, employees and agents for any and all claims, demands,
                losses, expenses, costs or damages of whatever nature, whether accrued or absolute, contingent or otherwise, arising out of or in connection with the defaulting party’s breach of this
                Agreement.
              </p>
            </div>
            <br /> <br />
            <div ref={sections.termination.ref}>
              8. <span className="font-poppins-semibold underline">TERMINATION OF CONTRACT</span>
              <p className="mx-8">
                8.1 This agreement shall remain in full force and effect unless amended, modified, revoked or terminated by the parties. The modification or termination of this agreement may be made
                by both parties for valid and/or legal reasons that may not undermine any provisions of this agreement, provided that a written notice will be given to the other party at least thirty
                (30) days prior hereto.
                <br /> <br />
                8.2 The revocation, cancellation or termination of this agreement shall not in any way prejudice, diminish, or abate any cause of action already accruing to{" "}
                <span className="font-poppins-semibold">CLIMBS </span> prior to or at the time of the effective date of the revocation, cancellation, or termination. In case of Termination of this
                policy, any unused premium shall be returned to COOP PARTNER, less 10% operational cost, within 30 days from date of termination. Thereafter,{" "}
                <span className="font-poppins-semibold">CLIMBS </span> shall have no further liability or obligation for any and all subsequent claims arising from events occurring after the date of
                termination. Claims arising from events occurring prior to termination shall still be considered valid;
                <br /> <br />
                8.3 This agreement shall be binding upon and shall inure to the benefit of the successors and assigns of the parties herein.
              </p>
              <br />
            </div>
            <br /> <br />
            <div ref={sections.amendments.ref}>
              9. <span className="font-poppins-semibold underline">AMENDMENTS AND VENUE OF SUIT</span>
              <p className="mx-8">
                Any amendments to this Agreement shall be in writing and only upon the mutual agreement of both parties. The Parties hereby agree that any suit, action or proceeding arising from or in
                relation to this Agreement shall be brought to the proper court with competent jurisdiction in Cagayan de Oro City to the exclusion of all other courts.
              </p>
            </div>
            <br /> <br />
            <div ref={sections.separability.ref}>
              <p className="font-poppins-semibold underline">10. SEPARABILITY CLAUSE</p>
              <p className="mx-8">
                If any term or condition of this Agreement is declared contrary to the law, the other terms and conditions hereof shall not be affected thereby and shall remain fully valid, subsisting
                and enforceable.
              </p>
            </div>
            <br />
            <span className="font-poppins-semibold">IN WITNESS WHEREOF</span>, the Parties have hereunto set their hands on the date and place first written above.
            <br /> <br />
            <div className="flex justify-center gap-4">
              <div>
                <div className="w-60">
                  <span className="font-poppins-semibold flex justify-center text-center">CLIMBS LIFE AND GENERAL INSURANCE COOPERATIVE</span>
                </div>
                <br />
                By:
                <br /> <br />
                <span className="font-poppins-semibold underline">NOEL D. RABOY</span>
                <br />
                President and CEO
                <br />
                <br />
                Attested by:
                <br />
                <span className="font-poppins-semibold underline"> RENAN P. DIAZ </span>
                <br />
                Vice President - Sales General Manager
              </div>
              <div>
                <div className="w-60">
                  <span className="font-poppins-semibold flex justify-center text-center">{data?.cooperative?.coopName}</span>
                </div>
                <br />
                By:
                <br /> <br />
                <span className="font-poppins-semibold underline">{formatOfficerName(primarySignatory)}</span>
                <br />
                <span>{getPositionName(primarySignatory)}</span>
                <br />
                <br />
                Attested by: <br />
                <br />
                <span className="font-poppins-semibold underline">{formatOfficerName(secondarySignatory)}</span>
                <br />
                <span>{getPositionName(secondarySignatory)}</span>
              </div>
            </div>
            <br />
            <br />
          </div>
        </div>
        {/* COL3 */}
        <div className="w-1/3 flex flex-col items-center justify-center h-[50rem] border border-zinc-200 p-4">
          <div className="h-1/2 w-full">
            <div className="w-full text-xl  font-poppins-semibold my-4"> Signed Agreement</div>

            {data?.proposalAgreement === null && (
              <div>
                {" "}
                <div className="p-2 text-zinc-400 text-xs rounded-md my-4">Please provide the coop signing date and upload the signed partnership agreement </div>
                <Button
                  classNames={`w-full rounded-md my-2 text-sm ${data?.proposalApproval?.status === "FOR_REVIEW" ? "bg-slate-300" : "bg-info"}`}
                  isSubmitting={data?.proposalApproval?.status === "FOR_REVIEW"}
                  onClick={handleSignedPartnershipAgreementModal}
                >
                  Upload
                </Button>
              </div>
            )}
            {data?.proposalAgreement !== null && (
              <div>
                {" "}
                <div className="flex justify-between text-sm mb-4 border-b pb-4 border-zinc-300">
                  <div>Date Coop Signed</div>
                  <div>{data?.proposalAgreement?.agreementSignedDate !== null && <span>{dayjs(data?.proposalAgreement?.agreementSignedDate).format("MMMM DD, YYYY")}</span>}</div>
                </div>
                <div className="flex flex-col gap-4 mb-4 border-b pb-4 border-zinc-300 text-sm">
                  <div>Attachment</div>

                  <div className="underline text-accent hover:cursor-pointer">
                    <a
                      href={
                        (data?.proposalAgreement?.attachments?.[0] as any)?.filepath ? `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${(data.proposalAgreement?.attachments?.[0] as any)?.filepath}` : "#"
                      }
                      target="_blank"
                      rel="noopener noreferrer" // This is important for security reasons
                    >
                      {data?.proposalAgreement?.attachments[0]?.label ? data?.proposalAgreement?.attachments[0]?.label : (formik.values?.attachments[0] as any)?.name}
                    </a>
                    <br />
                    {data?.proposalNotarization?.agreementNotarizationStatus === "NOTARIZED" && (
                      <a
                        href={
                          (data?.proposalNotarization?.attachments?.[0] as any)?.filepath
                            ? `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${(data?.proposalNotarization?.attachments?.[0] as any)?.filepath}`
                            : "#"
                        }
                        target="_blank"
                        rel="noopener noreferrer" // This is important for security reasons
                      >
                        {data?.proposalNotarization?.attachments?.[0]?.label ? data?.proposalNotarization?.attachments?.[0]?.label : (formik.values?.attachments[0] as any)?.name}
                      </a>
                    )}
                  </div>
                </div>
                <div className="w-full">
                  {data?.proposalNotarization?.agreementNotarizationStatus !== "NOTARIZED" && (
                    <Button type="submit" classNames="w-full bg-info text-sm" onClick={handleSignedPartnershipAgreementModal}>
                      Replace
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="h-1/2 w-full">
            {" "}
            <div className="w-full text-xl font-poppins-semibold py-4 border-t border-zinc-200"> REMARKS</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartnershipAgreement;
