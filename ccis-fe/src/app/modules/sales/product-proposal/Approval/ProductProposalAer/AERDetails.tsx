import { FC, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import Typography from "@components/common/Typography";

import { ProposalStatus } from "@enums/proposal-status";
import { ProductCategoryType } from "@enums/enums";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { useQuotationActions } from "@state/reducer/quotations";

import CLSPGuidelines from "./AERGuidelines/CLSPGuidelines";
import CLPPGuidelines from "./AERGuidelines/CLPPGuidelines";
import GYRTGuidelines from "./AERGuidelines/GYRTGuidelines";
import FIPGuidelines from "./AERGuidelines/FIPGuidelines";

interface ProductProposalAERProps {
  proposalData: any; // Ideally, define a proper TypeScript type instead of `any`
}

const DisplayAERDetails: FC<ProductProposalAERProps> = ({ proposalData }) => {
  const approvedAERDetails: any = proposalData || useSelector((state: RootState) => state.quotation.quotations);

  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getQuotations } = useQuotationActions();

  const getApprovedProductRevision = () => {
    // Access revisions from the correct path in your data structure
    const revisions = approvedAERDetails?.proposable?.product?.productRevisions ?? [];
    const approvedRevisions = revisions.filter((rev: any) => rev.approvalStatus === ProposalStatus.approved).sort((a: any, b: any) => b.id - a.id);
    return approvedRevisions[0];
  };

  const approvedProductRevision = getApprovedProductRevision();
  const productGuidelines = approvedProductRevision?.productGuidelines ?? [];

  const renderGuidelines = () => {
    // Access product code from the correct path
    const productCode = approvedAERDetails?.proposable?.product?.productCode;

    if (!productGuidelines?.length) {
      return <p className="text-center text-gray-500">No approved product guidelines found.</p>;
    }

    switch (productCode) {
      case ProductCategoryType.CLPP:
        return <CLPPGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      case ProductCategoryType.CLSP:
        return <CLSPGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      case ProductCategoryType.GYRT:
        return <GYRTGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      case ProductCategoryType.FIP:
        return <FIPGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      default:
        return <p className="text-center text-gray-500">No specific guideline component available for this product.</p>;
    }
  };

  useEffect(() => {
    // Fetch required data if not provided via props
    if (!proposalData) {
      getProductBenefits({ filter: "" });
      getCommissionTypes();
      getQuotations({ params: { statusFilter: ProposalStatus.approved } });
    }
  }, []);

  if (!approvedAERDetails) {
    return (
      <div className="bg-white p-10">
        <p className="text-center text-gray-500">No AER details available.</p>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="max-h-[600px] overflow-y-auto">
        <div className="flex flex-1 flex-col p-10 mt-2">
          {/* Letter Section */}
          <Typography className="flex flex-col space-y-10 !text-black text-justify">
            <p className="font-poppins-semibold">July 3, 2025</p>
            <div>
              <p className="mt-0 font-poppins-semibold underline">The Board of Directors</p>
              <p className="max-w-60 mt-0 font-poppins-semibold underline">{approvedAERDetails?.cooperative?.coopName ?? "Name of Cooperative"}</p>
              <p className="mt-0 font-poppins-semibold underline">
                {[approvedAERDetails?.cooperative?.streetAddress, approvedAERDetails?.cooperative?.city, approvedAERDetails?.cooperative?.province].filter(Boolean).join(", ") ||
                  "Address of Cooperative"}
              </p>
            </div>

            <span>Dear Valued Cooperator,</span>
            <span>Warm Cooperative Greetings!</span>
            <span>
              At CLIMBS Life and General Insurance Cooperative, we are more than just an insurance provider—we are a movement built on the principles of mutual protection, solidarity, and
              sustainability. As a cooperative insurer, we remain committed to empowering our members by providing innovative and accessible insurance solutions that secure their future, communities,
              and the environment.
            </span>
            <span>
              Founded in 1971 by the late Atty. Mordino Cua and Atty, Aquilino Pimentel, Sr., CLIMBS was created to be an alternative to traditional insurance—one that truly understands the needs of
              cooperatives and their members. Over the years, we have grown into a trusted name in microinsurance, with a strong network of over 4,000+ primary cooperatives across the Philippines.
              Today, we continue to uphold our mission by embracing Climate Insurance under our banner: "Insuring Where You Are!"
            </span>
          </Typography>

          <div className="mt-5">{renderGuidelines()}</div>
        </div>
      </div>
    </div>
  );
};

export default DisplayAERDetails;
