import { FC, useEffect } from "react";
import Typography from "@components/common/Typography";
import { ProductGuidelineType, ProposalAerGuidelines } from "@enums/proposalAerGuidelines";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { IUtilitiesProductBenefits } from "@interface/utilities.interface";

type Props = {
  approvedAERDetails: any;
  productGuidelines: any[];
};

const GYRTGuidelines: FC<Props> = ({ approvedAERDetails, productGuidelines }) => {
  const { getContestability } = useContestabilityActions();
  const contestability = useSelector((state: RootState) => state.contestability.getContestability);
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const productBenefits: IUtilitiesProductBenefits[] = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);

  const quotation = approvedAERDetails?.quotation ?? approvedAERDetails?.proposable?.quotation;
  const gyrtAge = quotation?.gyrtAge ?? [];
  const contestabilityId = quotation?.contestability;
  // Get unique options from the data (copied from ActuaryEvaluationReport)
  const getUniqueOptions = () => {
    if (!approvedAERDetails?.proposable?.quotation?.gyrtBenefits) return [];

    const options = [...new Set(approvedAERDetails.proposable.quotation.gyrtBenefits.map((b: any) => b.option))]
      .filter((option) => option !== null && option !== undefined)
      .sort((a, b) => Number(a) - Number(b)); // Sort options numerically

    return options;
  };

  const uniqueOptions = getUniqueOptions();
  useEffect(() => {
    getContestability({ filter: "" });
    getProductBenefits({ filter: "" });
  }, []);

  return (
    <div className="flex flex-col space-y-6">
      {productGuidelines?.map((value: any, gIndex: number) => {
        if (
          [
            ProposalAerGuidelines.InsuranceCoverageEffectivity,
            ProposalAerGuidelines.MaximumNumberOfEnrollees,
            ProposalAerGuidelines.ClaimProcessFlow,
            ProposalAerGuidelines.UnderwritingProcedure,
          ].includes(value.label)
        )
          return null;

        if (value.label.includes(ProposalAerGuidelines.GracePeriod)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>
              {value.productGuideline?.map((pg: any, pgIndex: number) => {
                if (pg.type === ProductGuidelineType.Texteditor || pg.type === "texteditor") {
                  const getOrdinalSuffix = (n: number) => {
                    const j = n % 10,
                      k = n % 100;
                    if (j === 1 && k !== 11) return `${n}st`;
                    if (j === 2 && k !== 12) return `${n}nd`;
                    if (j === 3 && k !== 13) return `${n}rd`;
                    return `${n}th`;
                  };

                  const exitAge = gyrtAge?.[0]?.exitAge ?? 64;
                  const accidentalDeathText = `Accidental death ends on ${getOrdinalSuffix(exitAge + 1)} birthday of the insured.`;

                  const gracePeriodList = [
                    "The grace period of thirty (30) days after the due date is given to the cooperative, during which the policy remains in force.",
                    "Non-payment of premium beyond the grace period automatically terminates the policy.",
                    accidentalDeathText,
                  ];

                  const modifiedHTML = `<ul>${gracePeriodList.map((item) => `<li>${item}</li>`).join("")}</ul>`;

                  return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: modifiedHTML }} />;
                }
                return null;
              })}
            </div>
          );
        }

        if (value.label.includes(ProposalAerGuidelines.ScheduleOfBenefitsAndAnnualPremium)) {
          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>

              {/* Updated table structure copied from ActuaryEvaluationReport */}
              {uniqueOptions.length > 0 ? (
                <table className="table-auto w-full text-sm mt-2 border border-collapse">
                  <thead>
                    <tr>
                      <th className="border px-4 py-2 text-left text-sm bg-amber-300" rowSpan={2}>
                        Benefit
                      </th>
                      <th className="border px-4 py-2 text-center text-sm bg-amber-300" colSpan={uniqueOptions.length}>
                        COVERAGE
                      </th>
                    </tr>
                    <tr>
                      {uniqueOptions.map((option) => (
                        <th key={`option-header-${option}`} className="border px-4 py-2 text-center text-sm bg-amber-300">
                          {`Option ${option}`}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {[...new Set(approvedAERDetails?.proposable?.quotation?.gyrtBenefits?.map((b: any) => b.benefitId))].map((benefitId) => {
                      const benefitName = productBenefits.find((b) => b.id === benefitId)?.benefitName ?? `Benefit ${benefitId}`;

                      return (
                        <tr key={`benefit-row-${benefitId}`}>
                          <td className="border px-4 py-2">{benefitName}</td>
                          {uniqueOptions.map((option) => {
                            const benefit = approvedAERDetails?.proposable?.quotation?.gyrtBenefits?.find((b: any) => b.benefitId === benefitId && b.option === option);

                            return (
                              <td key={`benefit-${benefitId}-option-${option}`} className="border px-4 py-2 text-center">
                                {benefit?.coverage
                                  ? `₱${Number(benefit.coverage).toLocaleString("en-US", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}`
                                  : "—"}
                              </td>
                            );
                          })}
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              ) : (
                // Fallback to original simple table if no options available
                <div className="flex flex-1 mt-4 mx-6 overflow-x-scroll">
                  <table className="table border-[1px] w-full">
                    <thead className="table-header-group">
                      <tr>
                        <td className="table-cell border-[1px] p-2">
                          <Typography className="font-semibold text-xs text-primary">Benefit</Typography>
                        </td>
                        <td className="table-cell border-[1px] p-2">
                          <Typography className="font-semibold text-xs text-primary">Coverage</Typography>
                        </td>
                      </tr>
                    </thead>
                    <tbody>
                      {approvedAERDetails?.quotation?.gyrtBenefits?.map((benefit: any, benefitIndex: number) => (
                        <tr key={`benefit-${benefitIndex}`}>
                          <td className="border-[1px] text-xs p-2">
                            <Typography>{productBenefits.find((productBenefit) => productBenefit.id === benefit.benefitId)?.benefitName || "N/A"}</Typography>
                          </td>
                          <td className="border-[1px] text-xs p-2">
                            <Typography>₱{Number(benefit.coverage).toLocaleString()}</Typography>
                          </td>
                        </tr>
                      )) || (
                        <tr>
                          <td colSpan={2} className="border-[1px] text-xs p-2 text-center">
                            <Typography>No benefits data available</Typography>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          );
        }

        if (value.label.includes(ProposalAerGuidelines.Contestability)) {
          const contestabilityObject = contestability?.data?.find((item: any) => item.id === contestabilityId);
          const selected = contestabilityObject?.value || contestabilityObject?.label || "";

          return (
            <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
              <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>
              {value.productGuideline?.map((pg: any, pgIndex: number) => {
                if (pg.type === ProductGuidelineType.Texteditor) {
                  if (selected === "Waived with masterlist") {
                    const html = `<ul style="list-style-type: disc; margin-left: 20px;"><li>One (1) year contestability period for new & incoming members. However, waive contestability period for all members included in the Masterlist submitted and enrolled at the same time on the FIRST REMITTANCE.</li></ul><p>Members who fail to renew within the thirty (30) day grace period shall be considered as \"new\" and will automatically be subject to a one (1) year contestability period.</p>`;
                    return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: html }} />;
                  }
                  return <div key={`pg-${pgIndex}`} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />;
                }
                return null;
              })}
            </div>
          );
        }

        return (
          <div key={`guideline-${gIndex}`} className="flex flex-col mb-6">
            <Typography className="text-[18px] mb-2 mt-2 font-poppins-semibold text-primary">{value.label}</Typography>
            {value.productGuideline?.map((pg: any, pgIndex: number) => {
              if (pg.type === ProductGuidelineType.Texteditor) {
                return <div key={pgIndex} className="text-justify text-wrap px-4" dangerouslySetInnerHTML={{ __html: pg.value ?? "" }} />;
              }
              if (pg.type === ProductGuidelineType.TextField) {
                return (
                  <Typography key={pgIndex} className="text-justify mt-4">
                    {pg.value}
                  </Typography>
                );
              }
              if (pg.type === ProductGuidelineType.List) {
                return (
                  <div key={pgIndex} className="mt-4">
                    <Typography className="font-poppins-semibold">{pg.label}</Typography>
                    <ul className="list-disc ml-6">
                      {pg.value?.map((item: any, idx: number) => (
                        <li key={idx}>
                          <Typography>{item.value}</Typography>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              }
              if (pg.type === ProductGuidelineType.TextField || pg.type === "textfield") {
                return (
                  <Typography key={`pg-${pgIndex}`} className="text-justify mt-4">
                    {pg.value}
                  </Typography>
                );
              }
              if (pg.type === ProductGuidelineType.Table) {
                return (
                  <div key={pgIndex} className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                    <table className="table border-[1px]">
                      <thead className="table-header-group">
                        <tr>
                          {pg.value?.columns?.map((col: any, colIndex: number) => (
                            <td key={`col-${colIndex}`} className="table-cell border-[1px]">
                              <Typography className="font-semibold text-xs text-primary">{col.value}</Typography>
                            </td>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {pg.value?.rows?.map((row: any[], rowIndex: number) => (
                          <tr key={`row-${rowIndex}`}>
                            {row.map((cell: any, cellIndex: number) => (
                              <td key={`cell-${cellIndex}`} className="border-[1px] text-xs">
                                <Typography>{cell.value}</Typography>
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                );
              }
              return null;
            })}
          </div>
        );
      })}
    </div>
  );
};

export default GYRTGuidelines;
