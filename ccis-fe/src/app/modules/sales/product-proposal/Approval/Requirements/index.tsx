import { FC, useState, useCallback } from "react";

import { IoIosCloseCircleOutline } from "react-icons/io";
import { FaFileAlt } from "react-icons/fa";
import { IProductProposal } from "@interface/product-proposal.interface";
import { formatDateToMonthYear } from "@helpers/date";
import CustomFileUpload from "@modules/admin/products/components/Common/Uploader";
import { toast } from "react-toastify";
import { AttachmentTags } from "@enums/attachment-tags";
import {
  createAttachmentService,
  deleteAttachmentService,
  updateRequirementableRequirementService,
  updateRequirementableStatusService,
} from "@services/shared/shared.service";
import { confirmDelete } from "@helpers/prompt";
import Overlay from "@components/common/Overlay";
import { getTextStatusColor } from "@helpers/text";
import { AttachmentStatus } from "@enums/proposal-status";
import Button from "@components/common/Button";
import { getFilePath } from "@helpers/file-path";

type Props = {
  data?: IProductProposal;
  onSuccess?: () => void;
};

const Requirements: FC<Props> = ({ data, onSuccess }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDrop = useCallback(
    (requirementId: number) => (acceptedFiles: Blob[]) => {
      acceptedFiles.forEach((file: any) => {
        const reader = new FileReader();
        reader.onload = async () => {
          try {
            setIsLoading(true);
            const formData = new FormData();
            formData.append(`attachableId`, requirementId.toString() ?? "");
            formData.append(`attachableType`, AttachmentTags.REQUIREMENTABLE_REQUIREMENT);
            formData.append(`attachments[0][file]`, file);
            if (data?.id) {
              const { data: response } = await createAttachmentService(formData);

              if (response) {
                const payload = {
                  requirementableId: data?.requirementable?.id,
                  id: requirementId,
                  status: "PENDING",
                  isInvalidDocument: false,
                  isBlurryFile: false,
                };

                const { data: updatedRequirement } = await updateRequirementableRequirementService(payload);

                if (updatedRequirement) {
                  toast.success("Successfully Uploaded File.");
                  onSuccess && onSuccess();
                  setIsLoading(false);
                }
              }
            }
          } catch (e) {
            toast.error("Error uploading file.");
          }
        };
        reader.readAsArrayBuffer(file);
      });
    },
    [data?.id, onSuccess]
  );

  const handleDelete = (attachmentId: number) => {
    setIsLoading(true);
    confirmDelete("attachment", async () => {
      try {
        const { data } = await deleteAttachmentService(attachmentId);
        if (data) {
          toast.success("Successfully Deleted File.");
          onSuccess && onSuccess();
          setIsLoading(false);
        }
      } catch (e) {
        toast.error("Error deleting attachment.");
      } finally {
        setIsLoading(false);
      }
    });
    setIsLoading(false);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    const hasAttachments = (data?.requirementable?.requirements ?? []).every(
      (requirement) => (requirement?.attachments ?? []).length > 0
    );

    if (!hasAttachments) {
      setIsLoading(false);
      return toast.error("Missing attachments. Please upload all requirements before submitting.");
    }

    try {
      const id = data?.requirementable?.id;
      const { data: response } = await updateRequirementableStatusService({
        id,
        status: "FOR_APPROVAL",
      });

      if (response) {
        toast.success("Successfully Submitted Requirements for approval");
        onSuccess && onSuccess();
        setIsLoading(false);
      }
    } catch (e) {
      setIsLoading(false);
      toast.error("Error submitting requirements.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full overflow-auto">
      {isLoading && <Overlay header="Processing..." bodyText="We are processing your request." />}
      <div className="p-4">
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name} </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Creation Date</div>
              <div className="w-2/3 text-black">{formatDateToMonthYear(data?.createdAt) ?? ""}</div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Status</div>
              <div className="w-2/3">
                <span className={`${getTextStatusColor(data?.requirementable?.status ?? "")}`}>
                  {/* {data?.requirementable?.status} */}
                  {(data?.requirementable?.status ?? "").replace(/_/g, " ").charAt(0).toUpperCase() +
                    (data?.requirementable?.status ?? "").replace(/_/g, " ").slice(1).toLowerCase()}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Coop Code</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopCode}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="px-10">
        <div className="w-full min-h-60 ">
          <div className="flex justify-between gap-2 mt-14 border-b border-zinc-200 pb-8">
            <div>
              <div className="font-poppins-semibold">Submit Requirements</div>
              <div className="text-xs text-zinc-400">Please scan and upload requirements.</div>
            </div>
            {["APPROVED"].includes(data?.proposalApproval?.status as string) && (
              <div>
                <Button
                  variant="primary"
                  classNames="btn btn-sm border-0 w-32"
                  disabled={isLoading}
                  onClick={handleSubmit}
                >
                  Submit
                </Button>
              </div>
            )}
          </div>
          {["APPROVED"].includes((data?.proposalApproval?.status as string) ?? "") && (
            <div>
              {data?.requirementable?.requirements?.map((requirement) => (
                <div key={requirement.id}>
                  <div className="py-8 border-b border-zinc-200 flex justify-between">
                    <div className="w-1/2 space-y-8">
                      <div className="flex flex-col">
                        <div className="flex items-start gap-8">
                          <span className="text-sm flex-1">{requirement.name}</span>
                          <span
                            className={`!leading-[1.2rem] text-sm whitespace-nowrap h-5 ${getTextStatusColor(
                              requirement.status ?? ""
                            )}`}
                          >
                            {requirement.status ?? ""}
                          </span>
                        </div>
                      </div>
                      <div>
                        {!requirement?.attachments?.length ? (
                          <CustomFileUpload
                            iconClass="h-8 w-8"
                            showIcon={true}
                            className="px-4 py-4 w-full"
                            key="upload-project-file"
                            onDrop={requirement.id ? onDrop(requirement.id) : () => {}}
                          />
                        ) : (
                          requirement?.attachments?.map((attachment: any) => (
                            <div key={attachment.id} className="border-slate-900 flex w-full p-2  mb-5">
                              <div className="flex items-center justify-between w-full">
                                <div className="flex align-center justify-center">
                                  <FaFileAlt size={30} className="mr-2" />
                                  <p className="mt-1 cursor-pointer hover:underline">
                                    <a href={getFilePath(attachment?.filepath)} download target="_blank">
                                      {attachment?.filename}
                                    </a>
                                  </p>
                                </div>
                                {["PENDING", "REJECTED", "FOR_REVISION"].includes(
                                  data?.requirementable?.status ?? ""
                                ) && (
                                  <div
                                    className="cursor-pointer"
                                    role="button"
                                    onClick={() => handleDelete(attachment.id)}
                                  >
                                    <IoIosCloseCircleOutline size={30} />
                                  </div>
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                    <div className="w-1/2 flex justify-center items-center">
                      {requirement?.status !== AttachmentStatus.valid && (
                        <div className="mt-2">
                          {requirement?.status === AttachmentStatus.invalid && (
                            <div className="text-xs">
                              <p className="text-sm font-poppins-semibold">Remarks: </p>
                              <ul className="list-inside">
                                {!!requirement?.isInvalidDocument && <li>Invalid Document</li>}
                                {!!requirement?.isBlurryFile && <li>Blurry File</li>}
                                {!!requirement?.remarks && <li>Others: {requirement?.remarks}</li>}
                              </ul>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          {!["APPROVED"].includes(data?.proposalApproval?.status as string) && (
            <div className="flex gap-2 mt-5 text-primary2">
              You cannot upload and submit requirements because the product proposal is not approved.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Requirements;
