import Typography from '@components/common/Typography'
import Select from '@components/form/Select'
import TextField from '@components/form/TextField'
import { formatSelectOptions } from '@helpers/array'
import { IAffiliation } from '@interface/product-proposal.interface'
import { CooperativeAffiliationSchema } from '@services/product-proposal/product-proposal.schema'
import { useAffiliationManagementActions } from '@state/reducer/utilities-affiliation'
import { RootState } from '@state/store'
import dayjs from 'dayjs'
import { Form, FormikProvider, useFormik } from 'formik'
import { FC, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { toast } from 'react-toastify'

type TProps = {
    selectedAffiliation?: IAffiliation & { index: number };
    affiliationState: IAffiliation[];
    toggleModal: () => void;
    resetForm: () => void;
    addAffiliation: (values: IAffiliation) => void
    editAffiliation: (data: IAffiliation & { index: number }) => void
}

const AffiliationForm: FC<TProps> = ({ selectedAffiliation, affiliationState, resetForm, toggleModal, addAffiliation, editAffiliation }) => {


    const { getAffiliation } = useAffiliationManagementActions();
    const affiliations = useSelector((state: RootState) => state.utilitiesAffiliation.Affiliation);

    const affiliationOption = formatSelectOptions(affiliations, "affiliationName");
    const statusOptions = [
        { text: "Active", value: "ACTIVE" },
        { text: "Inactive", value: "INACTIVE" },
    ];

    const handleAdd = (values: IAffiliation) => {

        const isExist = affiliationState.find((data) => data.affiliationId == values.affiliationId);

        if (isExist) {
            toast.error("Affiliation was already added");
            return;
        }

        const result = affiliations.find((data) => data.id.toString() === values.affiliationId);

        if (result) {
            addAffiliation({
                affiliation: {
                    affiliationId: result.id.toString(),
                    affiliationName: result.affiliationName,
                    status: values.status,
                    effectivityDate: dayjs(values.effectivityDate).format("YYYY-MM-DD"),
                },
                affiliationId: result.id.toString(),
                affiliationName: result.affiliationName,
                status: values.status,
                effectivityDate: dayjs(values.effectivityDate).format("YYYY-MM-DD"),
            });
        }

        toggleModal();
    }

    const handleEdit = (values: IAffiliation) => {

        const result = affiliations.find((data) => data.id.toString() === values.affiliationId);

        if (result) {
            editAffiliation({
                index: selectedAffiliation?.index ?? 0,
                affiliationId: result.id.toString(),
                affiliationName: result.affiliationName,
                status: values.status,
                effectivityDate: dayjs(values.effectivityDate).format("YYYY-MM-DD"),
            });
        }

        toggleModal();
    }

    const formikAffiliation = useFormik({
        initialValues: selectedAffiliation ?? {
            affiliationId: "",
            affiliationName: "",
            status: "",
            effectivityDate: "",
        },
        validationSchema: CooperativeAffiliationSchema,
        onSubmit: (values: IAffiliation) => {
            if (selectedAffiliation) {
                handleEdit(values);
            } else {
                handleAdd(values);
            }
        }
    })

    useEffect(() => {
        getAffiliation({ filter: "" });
        return () => {
            resetForm();
        }
    }, []);

    return (
        <FormikProvider value={formikAffiliation}>
            <Form>
                <div className="flex flex-1 flex-col space-y-4">
                    <div className="flex flex-1 flex-row items-center space-x-2">
                        <Typography className='min-w-36'>Affiliation</Typography>
                        <div className="flex flex-1 flex-col">
                            <Select
                                size='sm'
                                value={formikAffiliation.values.affiliationId}
                                options={affiliationOption}
                                onChange={formikAffiliation.handleChange}
                                error={!!formikAffiliation.errors.affiliationId && formikAffiliation.touched.affiliationId}
                                errorText={formikAffiliation.errors.affiliationId}
                                name="affiliationId"
                            />
                        </div>
                    </div>
                    <div className="flex flex-1 flex-row space-x-2">
                        <Typography className='min-w-36'>Status</Typography>
                        <div className="flex flex-1 flex-col">
                            <Select
                                size='sm'
                                value={formikAffiliation.values.status}
                                options={statusOptions}
                                onChange={formikAffiliation.handleChange}
                                error={!!formikAffiliation.errors.status && formikAffiliation.touched.status}
                                errorText={formikAffiliation.errors.status}
                                name="status"
                            />
                        </div>
                    </div>
                    <div className="flex flex-1 flex-row space-x-2">
                        <Typography className='min-w-36'>Effectivity Date</Typography>
                        <div className="flex flex-1 flex-col">
                            <TextField
                                name="effectivityDate"
                                type='date'
                                value={formikAffiliation.values.effectivityDate}
                                onChange={formikAffiliation.handleChange}
                                error={!!formikAffiliation.errors.effectivityDate && formikAffiliation.touched.effectivityDate}
                                errorText={formikAffiliation.errors.effectivityDate}
                                placeholder="Enter Effectivity Date"
                                className="mt-2"
                                size='sm'
                            />
                        </div>
                    </div>
                    <div className="flex flex-1 justify-end space-x-2">
                        <button className="btn bg-zinc-200 text-primary w-32" onClick={toggleModal}>Cancel</button>
                        {!selectedAffiliation && (
                            <button type="submit" className="btn bg-primary text-white w-32 hover:text-primary">Add</button>
                        )}
                        {selectedAffiliation && (
                            <button type='submit' className="btn bg-primary text-white w-32 hover:text-primary">Save</button>
                        )}
                    </div>
                </div>
            </Form>
        </FormikProvider>

    )
}

export default AffiliationForm