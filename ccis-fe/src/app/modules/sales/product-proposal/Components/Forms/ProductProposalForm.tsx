import Stepper from "@components/common/Stepper";
import { Fragment, useEffect } from "react";
import { FaChevronLeft, FaRegFileAlt } from "react-icons/fa";
import { FaFileCircleCheck } from "react-icons/fa6";
import Step1 from "../Steps/Step1";
import Step2 from "../Steps/Step2";
import Step3 from "../Steps/Step3";
import { useLocation, useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { IProductProposal } from "@interface/product-proposal.interface";
import { useProductActions } from "@state/reducer/products";
import Typography from "@components/common/Typography";
import { TProductProposalPayload } from "@state/types/product-proposal";
import { toast } from "react-toastify";
import { ProposableTypes, ProposalTypes } from "@enums/enums";
import { ProductStatus } from "@enums/product-status";
import { postProductProposalService, putProductProposalService } from "@services/product-proposal/product-proposal.service";
import { removeProperties } from "@helpers/objects";
import { ProposalStatus } from "@enums/proposal-status";
import { UserRoles } from "@interface/routes.interface";
import { handleRoleBasedNavigation } from "@helpers/navigatorHelper";

const ProductProposalForm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const productProposal = location.state?.proposal as IProductProposal;
  const revisionDetails = useSelector((state: RootState) => state.products.revisionDetails);
  const cooperative = useSelector((state: RootState) => state.productProposal?.cooperative);
  const { setProposedProduct, setManagementFee, setCooperative } = useProductProposalActions();
  const { setRevisionDetails, resetRevisionDetails } = useProductActions();
  const step = useSelector((state: RootState) => state.productProposal.step);
  const { setStep, reset } = useProductProposalActions();
  const managementFee = useSelector((state: RootState) => state.productProposal.managementPercentFee);
  const proposedProduct = useSelector((state: RootState) => state.productProposal.proposedProduct);
  const customType = useSelector((state: RootState) => state.productProposal.customType);
  const { data: currentUser } = useSelector((state: RootState) => state.auth.user);
  const approvedAER = useSelector((state: RootState) => state.quotation.quotation);

  const stepperLabel = [
    {
      label: "Select Options",
      icon: <FaRegFileAlt />,
    },
    {
      label: "Select Coop",
      icon: <FaRegFileAlt />,
    },
    {
      label: "Review",
      icon: <FaFileCircleCheck />,
    },
  ];
  const roleNavigationMap = Object.freeze({
    [UserRoles.admin]: ROUTES.ADMIN.productProposal.key,
    [UserRoles.sales]: ROUTES.SALES.productProposal.key,
  });
  const handleNavigation = () => {
    handleRoleBasedNavigation(currentUser?.roles ?? [], navigate, roleNavigationMap);
  };
  const handleSaveAsDraft = async (cooperativeId?: string) => {
    try {
      if (revisionDetails === undefined) {
        toast.error("Please select a product");
        return;
      }

      if (cooperative === undefined && (step ?? 0) > 0) {
        toast.error("Please select a cooperative");
        return;
      }

      const cleanedRevisionDetails = removeProperties(revisionDetails);

      const productDetails: TProductProposalPayload = {
        cooperativeId: cooperativeId ?? undefined,
        productId: revisionDetails?.productId,
        managementPercentFee: managementFee ?? 0,
        proposableId: revisionDetails?.id,
        proposableType: ProposableTypes.PRODUCT_REVISION,
        proposalType: customType ? ProposalTypes.CUSTOMIZED : ProposalTypes.STANDARD,
        productStatus: ProductStatus.FOR_APPROVAL,
        status: ProposalStatus.draft,
        productRevision: {
          ...cleanedRevisionDetails,
          type: "SALES",
          attachments: undefined,
        },
      };

      if (proposedProduct?.id === undefined || proposedProduct.id === "") {
        const { data } = await postProductProposalService(productDetails);
        setProposedProduct(data);
      } else {
        const { data } = await putProductProposalService(productDetails, proposedProduct?.id);
        setProposedProduct(data);
      }
      toast.success("Product proposal has been saved as draft");
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (productProposal) {
      setProposedProduct(productProposal);
      setCooperative((productProposal as any)?.cooperative);
      setManagementFee(parseFloat(productProposal?.managementPercentFee.toString() ?? 0));
      setRevisionDetails(productProposal.productRevision);
    }

    return () => {
      resetRevisionDetails();
      reset();

      () => {
        reset();
      };
    };
  }, [productProposal]);

  return (
    <Fragment>
      <div className="flex flex-1 flex-col justify-start px-4">
        <div className="flex flex-1 flex-row mb-4">
          <button
            className="btn btn-sm ml-8"
            onClick={() => {
              handleNavigation();
            }}
          >
            <FaChevronLeft />
            Back
          </button>
        </div>
        <div className="relative">
          <div className="mx-16 absolute inset-x-0 max-w-full">
            <Stepper strictMode={true} steps={stepperLabel} currentStep={step ?? 0} setStep={(step: number) => setStep(step)} />
          </div>
          <div className="absolute w-full">
            {step === 0 && (revisionDetails === approvedAER) === undefined && (
              <div className="flex flex-1 flex-col justify-center items-center mt-36">
                <Typography className="!text-3xl text-zinc-500 mb-10">Select your option</Typography>
                <Typography>To streamline the creation of your product proposal, please select oe of the following options.</Typography>
              </div>
            )}
            {step === 1 && !cooperative && (
              <div className="flex flex-1 flex-col justify-between mt-28">
                <Typography className="text-accent !text-md">CHOOSE A COOPERATIVE</Typography>
                <Typography className="!text-sm">Please choose a cooperative to present the product</Typography>
              </div>
            )}
            {step === 2 && (
              <div className="flex flex-1 flex-col justify-between mt-28">
                <Typography className="text-accent !text-md">REVIEW PROPOSAL</Typography>
                <Typography className="!text-sm">Check the product proposal for accuracy before submission</Typography>
              </div>
            )}
          </div>
          <div className="h-[calc(100%-4rem)] overflow-auto">
            {step === 0 && (
              <div className="">
                <Step1 handleSaveAsDraft={handleSaveAsDraft} />
              </div>
            )}
            {step === 1 && (
              <div className="mt-40">
                <Step2 handleSaveAsDraft={handleSaveAsDraft} />
              </div>
            )}
            {step === 2 && (
              <div className="mt-44">
                <Step3 handleSaveAsDraft={handleSaveAsDraft} />
              </div>
            )}
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default ProductProposalForm;
