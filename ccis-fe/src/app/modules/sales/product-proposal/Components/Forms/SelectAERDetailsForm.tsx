import TextField from "@components/form/TextField"
import { ChangeEvent, FC, Fragment, useEffect } from "react"
import { FaSearch } from "react-icons/fa"
import ProductsList from "../List/ProductsList"
import { useProductActions } from "@state/reducer/products"
import { useSelector } from "react-redux"
import { RootState } from "@state/store"
import Loader from "@components/Loader"
import Typography from "@components/common/Typography"
import ProductDetails from "../ProductDetails"
import { toast } from "react-toastify"
import { useDebouncedCallback } from "use-debounce"
import { RevisionStatus } from "@enums/revision-status"

type TProps = {
    toggleModal: () => void,
    toggleFeeModal: () => void
}

const SelectAERDetailsForm: FC<TProps> = ({ toggleModal, toggleFeeModal }) => {

    const { getProducts, resetRevisionDetails } = useProductActions();
    const revisionDetails = useSelector((state: RootState) => state.products.revisionDetails);
    const loadingProducts = useSelector((state: RootState) => state.products.getProducts.loading);

    const handleSelected = () => {
        if (revisionDetails === undefined) {
            toast.error('Please select a product revision');
            return;
        }

        toggleModal && toggleModal();
        toggleFeeModal && toggleFeeModal();
    }

    const handleCancel = () => {
        resetRevisionDetails();
        toggleModal();
    }

    const handleSearchProduct = useDebouncedCallback((e: ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        getProducts({ params: { page: 0, pageSize: 0, statusFilter: RevisionStatus.approved, filter: value } });
    }, 500)

    useEffect(() => {
        resetRevisionDetails();
        getProducts({ params: { page: 1, pageSize: 10, statusFilter: RevisionStatus.approved } });
    }, [])

    return (
        <Fragment>
            <div className="flex flex-1 flex-col max-h-[600px] overflow-hidden">
                <div className="flex flex-1 flex-row justify-between min-h-[500px] max-h-[600px]">
                    <div className="flex-1 flex-col pr-2">
                        <TextField
                            placeholder="Search"
                            size="xs"
                            className="input-sm"
                            variant="primary"
                            rightIcon={<FaSearch className="text-zinc-500" />}
                            onChange={handleSearchProduct}
                        />
                        <div className="flex flex-1 flex-col max-w-64 min-w-60 max-h-[500px] overflow-y-auto mt-2">
                            <ProductsList />
                        </div>
                    </div>
                    <div className="flex border-l-2 border-l-slate-200 p-2 flex-[3]">
                        <div className="flex flex-1 flex-col border-[1px] border-zinc-300 max-h-[600px] overflow-y-auto">
                            {loadingProducts && revisionDetails === undefined && (
                                <div className="flex flex-1 justify-center items-center">
                                    <Loader />
                                </div>
                            )}

                            {!loadingProducts && revisionDetails === undefined &&
                                <div className="flex flex-1 justify-center items-center">
                                    <Typography>Select a product revision from the list of products</Typography>
                                </div>
                            }

                            {revisionDetails !== undefined && (
                                <div className="flex flex-1 justify-start items-start">
                                    <ProductDetails />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                <div className="flex flex-1 flex-row justify-end p-2 space-x-4">
                    <button className="btn bg-zinc-300  !min-w-32" onClick={handleCancel}>Cancel</button>
                    <button className="btn bg-primary text-white !min-w-32" onClick={handleSelected}>Select</button>
                </div>
            </div>
        </Fragment>
    )
}

export default SelectAERDetailsForm