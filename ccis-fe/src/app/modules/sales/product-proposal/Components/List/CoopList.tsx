import LoadingButton from "@components/common/LoadingButton";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import { ICooperative } from "@interface/product-proposal.interface";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { RootState } from "@state/store";
import { FC, Fragment, useState } from "react";
import { FaPlus } from "react-icons/fa";
import { LuFolderSearch } from "react-icons/lu";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";

type TProps = {
  submitting: boolean;
  handleSaveAsDraft: () => void;
  handleCdaModal: () => void;
};

const CoopList: FC<TProps> = ({
  submitting,
  handleSaveAsDraft,
  handleCdaModal,
}) => {
  const { setCooperative, setStep } = useProductProposalActions();
  const cooperatives = useSelector(
    (state: RootState) => state.productProposal.cooperatives
  );
  const cooperative = useSelector(
    (state: RootState) => state.productProposal.cooperative
  );
  const loading = useSelector(
    (state: RootState) => state.productProposal.getCooperatives.loading
  );
  const [selectedCoop, setSelectedCoop] = useState<ICooperative | undefined>(
    undefined
  );

  const handleSelectedCoop = (coop: ICooperative) => {
    setCooperative(coop);
    setSelectedCoop(coop);
  };

  const handleDraft = () => {
    handleSaveAsDraft && handleSaveAsDraft();
  };

  const handleContinue = (step: number) => {
    if (cooperative === undefined) {
      toast.info("Please select a cooperative");
      return;
    }
    setStep(step);
  };

  return (
    <Fragment>
      <div className="flex flex-1 flex-col">
        <div className="flex flex-1">
          {loading && (
            <div className="flex flex-1 w-full justify-center">
              <Loader />
            </div>
          )}
          {!loading && cooperatives.length === 0 && (
            <div className="flex flex-1 flex-col w-full justify-center items-center space-y-4">
              <LuFolderSearch size={100} className="text-accent" />
              <Typography size="lg" className="!text-accent">
                No results found.
              </Typography>
              <Typography size="sm" className="!text-zinc-500">
                We couldn't find any cooperatives that match your search.
              </Typography>
              <button
                className="btn btn-sm bg-accent text-white max-w-48 mt-4"
                onClick={handleCdaModal}
              >
                <FaPlus />
                Add New Coop
              </button>
            </div>
          )}

          {!loading && cooperatives.length > 0 && (
            <div className="flex flex-1 flex-col w-full max-h-[250px] overflow-y-auto">
              {cooperatives.map((coop: ICooperative) => {
                return (
                  <div
                    key={`coop-${coop.id}`}
                    onClick={() => handleSelectedCoop(coop)}
                    className={`
                                    group
                                      flex flex-1 cursor-pointer px-2 items-center border-[1px] border-zinc-200 min-h-12 my-[1px]
                                    hover:bg-primary !hover:text-white
                                    ${
                                      selectedCoop?.id === coop.id
                                        ? "bg-primary"
                                        : ""
                                    }
                                `}
                  >
                    <span
                      className={`text-zinc-500 group-hover:text-white ${
                        selectedCoop?.id === coop.id ? "!text-white" : ""
                      }`}
                    >
                      {coop.coopName}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
        {!loading && cooperatives.length > 0 && (
          <div className="flex flex-1 flex-row w-full items-center justify-between">
            <div className="flex flex-1 justify-start">
              <LoadingButton
                isLoading={submitting}
                onClick={() => setStep(0)}
                className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-32 mr-4 mt-4"
                type="button"
              >
                Back
              </LoadingButton>
            </div>
            <div className="flex flex-1 justify-end">
              <LoadingButton
                isLoading={submitting}
                onClick={handleDraft}
                className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-32 mr-4 mt-4"
                type="submit"
              >
                Save as draft
              </LoadingButton>
              <LoadingButton
                isLoading={submitting}
                onClick={() => handleContinue(2)}
                className="btn rounded-lg bg-accent text-white !w-32 mr-4 mt-4"
                type="button"
              >
                Continue
              </LoadingButton>
            </div>
          </div>
        )}
      </div>
    </Fragment>
  );
};

export default CoopList;
