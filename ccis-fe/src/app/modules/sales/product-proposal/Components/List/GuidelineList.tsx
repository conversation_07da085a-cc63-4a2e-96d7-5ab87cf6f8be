import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import CheckBox from "@components/form/CheckBox";
import TextField from "@components/form/TextField";
import { ChangeEvent, FC, Fragment, useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
//import { guidelinesLabels } from "@constants/guideline";
import httpClient from "@clients/httpClient";
import Loader from "@components/Loader";
import Modal from "@components/common/Modal";
import { Form, FormikProvider, useFormik } from "formik";

type TListOfGuidelinesProps = {
    preSelectedGuidelines?: string[];
    handleModal?: () => void;
    handleAppliedGuidelines?: (guidelines: string[]) => void;
};

const GuidelineList: FC<TListOfGuidelinesProps> = ({ preSelectedGuidelines = [], handleModal, handleAppliedGuidelines }) => {

    const [selectedGuidelines, setSelectedGuidelines] = useState<string[]>(preSelectedGuidelines);

    // const [guidelines, setGuidelines] = useState<string[]>(guidelinesLabels);
    const [guidelines, setGuidelines] = useState<string[]>([]);
    const [addGuidelineModal, setAddGuidelineModal] = useState<boolean>(false);

    const handleAddGuideline = (label: string) => {
        if (selectedGuidelines.includes(label)) {
            setSelectedGuidelines((prev) => prev.filter((v) => v !== label));
        } else {
            setSelectedGuidelines((prev) => [...prev, label]);
        }
    };

    const handleApplyGuidelines = () => {
        const selected = selectedGuidelines.filter((guideline) => !preSelectedGuidelines.includes(guideline));
        if (selected.length !== 0) {
            handleAppliedGuidelines && handleAppliedGuidelines(selected);
        }

        handleModal && handleModal();
    }

    const handleAddNewGuidelineModal = () => {
        setAddGuidelineModal((prev) => !prev);
    };

    const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
        const term = e.target.value.toLowerCase();
        const result = guidelines.filter((v) => v.toLowerCase().indexOf(term) > -1);
        setGuidelines([...result]);
    };

    const handleSelectedGuidelinesLength = () => {
        if (selectedGuidelines.length > 0) {
            return selectedGuidelines.length - preSelectedGuidelines.length;
        } else {
            return 0;
        }
    }

    useEffect(() => {
        fetchHeaders();
    }, []);

    const fetchHeaders = async () => {
        const response = await httpClient.get("/headers");
        if (response.data) {
            const filteredData = response.data.filter((item: any) => !preSelectedGuidelines.includes(item.headerName));
            setGuidelines(filteredData);
        }
    };

    const postNewGuideline = async (values: any) => {
        const response = await httpClient.post("/headers", values);
        if (response) {
            handleAddNewGuidelineModal();
            fetchHeaders();
        }
    };

    const [error, setError] = useState<string | null>(null);

    const formik = useFormik({
        initialValues: {
            headerCode: "",
            headerName: "",
            description: "",
        },
        onSubmit: (values, { resetForm }) => {
            if (guidelines.some((guideline: any) => guideline?.headerName === values.headerName || guideline?.headerCode === values.headerCode)) {
                setError("Guideline with this name or code already exists");
                setTimeout(() => {
                    setError(null);
                }, 5000);
                return;
            } else {
                postNewGuideline(values);
            }
            resetForm();
        },
    });

    return (
        <Fragment>
            <Modal title="Add Guidelines" onClose={handleAddNewGuidelineModal} modalContainerClassName={"max-w-3xl"} isOpen={addGuidelineModal}>
                <FormikProvider value={formik}>
                    <Form className=" flex flex-col gap-4">
                        <div className="text-red-500">{error}</div>
                        <TextField
                            required
                            className={`${error ? "border-red-500" : ""}`}
                            name="headerCode"
                            placeholder="Code"
                            value={formik.values.headerCode}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                        />
                        <TextField
                            required
                            className={`${error ? "border-red-500" : ""}`}
                            name="headerName"
                            placeholder="Name"
                            value={formik.values.headerName}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                        />
                        <div className="w-full flex justify-end">
                            <Button type="submit" outline variant="primary" classNames="text-xs w-24">
                                Add
                            </Button>
                        </div>
                    </Form>
                </FormikProvider>
            </Modal>
            <TextField leftIcon={<FaSearch />} placeholder="Search Product Guidelines" onChange={handleSearch} />
            <div className="flex flex-1 flex-row items-center mt-2">
                <div className="flex flex-1 flex-row items-center justify-start">
                    <Typography className="ml-2 w-44 text-nowrap mr-2">{handleSelectedGuidelinesLength()} Guidelines selected </Typography>
                </div>
                <div>
                    <Button type="button" onClick={handleAddNewGuidelineModal} outline variant="primary" classNames="text-xs">
                        Add New Guideline
                    </Button>
                </div>
            </div>
            <div className="divider mt-5"></div>

            <div className="flex flex-wrap flex-row flex-1 !max-h-[400px] overflow-y-auto">
                <div className="w-full h-full">{!guidelines.length && <Loader />}</div>
                {guidelines.map((value, index) => {
                    return (
                        <div key={`guidelines-labels-${index}`} className="w-1/4 min-h-12">
                            <CheckBox
                                value={(value as any)?.headerName}
                                checked={selectedGuidelines.includes((value as any)?.headerName)}
                                rightLabel={(value as any)?.headerName}
                                onChange={(e) => handleAddGuideline(e.target.value)}
                            />
                        </div>
                    );
                })}
            </div>

            {selectedGuidelines.length > 0 && (
                <div className="flex flex-1 flex-row justify-end mt-4">
                    <Button variant="primary" classNames="btn-md" onClick={handleApplyGuidelines}>
                        Apply Selected {`${selectedGuidelines.length === 1 ? "Guideline" : "Guidelines"}`}
                    </Button>
                </div>
            )}
        </Fragment>
    );
};

export default GuidelineList;
