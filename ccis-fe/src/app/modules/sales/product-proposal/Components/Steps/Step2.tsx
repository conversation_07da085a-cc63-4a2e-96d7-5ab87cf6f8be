import Typography from "@components/common/Typography";
import TextField from "@components/form/TextField";
import { FC, Fragment, ChangeEvent, useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
import CoopList from "../List/CoopList";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { useDebouncedCallback } from "use-debounce";
import Modal from "@components/common/Modal";
import SelectCoopFromCdaForm from "../Forms/SelectCoopFromCdaForm";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import CooperativeInformationForm from "../Forms/CooperativeInformationForm";
import { toast } from "react-toastify";
import { ProposalStatus } from "@enums/proposal-status";

type TStep2Props = {
  handleSaveAsDraft?: (cooperativeId?: string) => void
}


const Step2: FC<TStep2Props> = ({ handleSaveAsDraft }) => {

  const { getCooperatives } = useProductProposalActions();
  const proposedProduct = useSelector(
    (state: RootState) => state.productProposal.proposedProduct
  );

  const cooperative = useSelector(
    (state: RootState) => state.productProposal?.cooperative
  );

  const [submitting, setSubmitting] = useState<boolean>(false);
  const [cdaModal, setCdaModal] = useState<boolean>(false);
  const toggleCdaModal = () => setCdaModal(!cdaModal);

  const handleSave = async (cooperativeId?: string) => {
    try {
      setSubmitting(true);
      if (cooperative === undefined) {
        toast.error("Please select a cooperative");
        return;
      }

      handleSaveAsDraft && handleSaveAsDraft(cooperativeId)
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleSearchCoop = useDebouncedCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      getCooperatives({ page: 1, pageSize: 10, filter: value });
    },
    500
  );

  useEffect(() => {
    getCooperatives({ pageSize: 20, page: 1 });
  }, []);

  return (
    <Fragment>
      {cdaModal && (
        <Modal
          isOpen={cdaModal}
          onClose={toggleCdaModal}
          title="Cooperatives List from CDA"
          modalContainerClassName="!max-w-3xl"
        >
          <SelectCoopFromCdaForm toggleModal={toggleCdaModal} />
        </Modal>
      )}
      {!cooperative && (
        <div className="flex flex-1 flex-col h-full">
          {proposedProduct?.status !== ProposalStatus.approved && (
            <Fragment>
              <div className="divider"></div>
              <div className="flex flex-1">
                <TextField
                  placeholder="Search"
                  leftIcon={<FaSearch className="text-zinc-500" />}
                  onChange={handleSearchCoop}
                />
              </div>
              <div className="divider"></div>
              <Typography className="text-accent !text-xl text-center">
                List of cooperatives
              </Typography>
              <div className="flex flex-1 w-full mt-2">
                <CoopList
                  submitting={submitting}
                  handleCdaModal={toggleCdaModal}
                  handleSaveAsDraft={handleSave}
                />
              </div>
            </Fragment>
          )}
        </div>
      )}
      {cooperative && !cdaModal && (
        <div className="flex flex-1 flex-col">
          <CooperativeInformationForm
            isSubmitting={submitting}
            handleSaveAsDraft={handleSave}
          />
        </div>
      )}
    </Fragment>
  );
};

export default Step2;
