import LoadingButton from "@components/common/LoadingButton";
import Typography from "@components/common/Typography";
import { ROUTES } from "@constants/routes";
import { ProposableTypes, ProposalTypes } from "@enums/enums";
import { ProductStatus } from "@enums/product-status";
import { ProposalStatus } from "@enums/proposal-status";
import { extractFirstPathSegment } from "@helpers/navigatorHelper";
import { removeProperties } from "@helpers/objects";
import { formatStringAtoZ0to9 } from "@helpers/text";
import {
  IGuidelineContent,
  IGuidelineContentTable,
} from "@interface/guidelines.interface";
import { IProductProposal } from "@interface/product-proposal.interface";
import {
  postProductProposalService,
  putProductProposalService,
} from "@services/product-proposal/product-proposal.service";
import {
  setProposedProduct,
  useProductProposalActions,
} from "@state/reducer/product-proposal";
import { useProductActions } from "@state/reducer/products";
import { RootState } from "@state/store";
import { TProductProposalPayload } from "@state/types/product-proposal";
import dayjs from "dayjs";
import { FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

type TStep3Props = {
  handleSaveAsDraft?: () => void;
};

const Step3: FC<TStep3Props> = ({ handleSaveAsDraft }) => {
  const navigate = useNavigate();
  const { setStep } = useProductProposalActions();
  const { resetRevisionDetails } = useProductActions();
  const user = useSelector((state: RootState) => state.auth.user);
  const proposedProduct = useSelector(
    (state: RootState) => state.productProposal.proposedProduct
  );
  const managementPercentFee = useSelector(
    (state: RootState) => state.productProposal.managementPercentFee
  );
  const revisionDetails = useSelector(
    (state: RootState) => state.products.revisionDetails
  );
  const cooperative = useSelector(
    (state: RootState) => state.productProposal.cooperative
  );
  const customType = useSelector(
    (state: RootState) => state.productProposal.customType
  );
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [pathBase, setPathBase] = useState<string>("");

  const standard =
    revisionDetails?.commission?.commissionDetails?.filter(
      (rowValue) =>
        rowValue.commissionAgeType?.name?.toLowerCase() !== "standard"
    ) ?? [];

  const handleSubmit = async () => {
    try {
      setSubmitting(true);

      const cleanedRevisionDetails = removeProperties(revisionDetails);

      const productDetails: TProductProposalPayload = {
        cooperativeId: cooperative?.id,
        productId: revisionDetails?.productId,
        managementPercentFee: managementPercentFee,
        proposableId: revisionDetails?.id,
        proposableType: ProposableTypes.PRODUCT_REVISION,
        proposalType: customType
          ? ProposalTypes.CUSTOMIZED
          : ProposalTypes.STANDARD,
        productStatus: ProductStatus.FOR_APPROVAL,
        status: ProposalStatus.active,
        productRevision: {
          ...cleanedRevisionDetails,
          type: "SALES",
          attachments: undefined,
        },
      };

      if (proposedProduct?.id === undefined || proposedProduct.id === "") {
        const { data } = await postProductProposalService(productDetails);
        if (data) {
          setProposedProduct(undefined as unknown as IProductProposal);
          resetRevisionDetails();
        }
      } else {
        const { data } = await putProductProposalService(
          productDetails,
          proposedProduct.id
        );
        if (data) {
          setProposedProduct(undefined as unknown as IProductProposal);
          resetRevisionDetails();
        }
      }

      toast.success("Proposal has been submitted successfully");

      navigate(
        (
          ROUTES[pathBase as keyof typeof ROUTES] as any
        )?.viewProductProposal.parse(proposedProduct?.id ?? "")
      );
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleSave = async () => {
    try {
      setSubmitting(true);
      handleSaveAsDraft && handleSaveAsDraft();
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };
  useEffect(() => {
    const firstSegment = extractFirstPathSegment(window.location.pathname, 0);
    if (firstSegment) {
      setPathBase(formatStringAtoZ0to9(firstSegment).toUpperCase());
    }
  }, []);

  return (
    <Fragment>
      <div className="divider"></div>
      <div className="flex flex-1 flex-col p-10 border-[1px] border-zinc-400">
        <Typography className="flex flex-col space-y-10 !text-black text-justify">
          <span>Date: {dayjs().format("MM/DD/YY")}</span>
          <span>{cooperative?.coopName ?? "[Coop Name]"}</span>
          <span>Dear Cooperators,</span>
          <span>Greetings from CLIMBS,</span>
          <span>
            <strong className="font-poppins-semibold mr-2">
              CLIMBS Life and General Insurance Cooperative
            </strong>
            has come a long way from where it started in 1971. From our humble
            beginning we have firmly established a strong financial position
            which make us a P3.68 billion pesos with a net worth of P1.97
            billion insurance cooperative today.
          </span>
          <span>
            As a group of companies with insurance as our code business, we grew
            into one of the country's leading players in the insurance industry
            and the top Cooperative insurer being licensed both by the
            Cooperative Development Authority and Insurance Commission. It is
            owned by more than 4,000 primary cooperatives in the Philippines.
            With an array of insurance products both Life and Non-life, we
            propose to offer you the{" "}
            <strong className="font-poppins-semibold mr-2">
              {revisionDetails?.product?.name ?? "[Product Name]"}
            </strong>{" "}
            insurance plan for all Cooperative members' protection with an
            affordable premium. Please find in the following pages the salient
            features and schedule of benefits of this insurance plan.
          </span>
          <span>
            For further inquiries or clarification regarding this proposal,
            pelase do not hesitate to get in touch with me thru my mobile number
            <p>
              {user?.data?.contactNumber ?? "[Contact Number]"} or email address{" "}
              {user?.data?.email ?? "[Email Address]"}.
            </p>
          </span>
          <span>
            We would be happy to do a product presentation on one of your
            board/management meetings.
          </span>
          <span>Thank you very much.</span>
        </Typography>
      </div>
      <div className="divider my-10"></div>
      <div className="flex flex-1 !text-black">
        <div className="flex flex-1 flex-col ">
          <Typography className="font-poppins-semibold !text-black" size="xl">
            {revisionDetails?.product?.name ?? "No Product Name Set"}
          </Typography>
          <div className="flex flex-1 flex-col justify-start mt-10">
            <Typography className="font-poppins-semibold !text-black">
              Product Description
            </Typography>
            <Typography className="ml-4 mt-4">
              {revisionDetails?.product?.description ??
                "No Product Description Set"}
            </Typography>
          </div>
          <div className="flex flex-1 flex-col justify-start mt-10">
            {revisionDetails?.productGuidelines?.map((value, gIndex) => {
              return (
                <div
                  key={`guideline-${gIndex}`}
                  className="flex flex-1 flex-col mb-10"
                >
                  <Typography className="text-[18px] mb-1 font-poppins-semibold">
                    {value.label}
                  </Typography>
                  {value.productGuideline.map((pgValue, pgIndex) => {
                    let listValue;
                    let tableValue;
                    if (pgValue.type === "list") {
                      listValue = pgValue.value as IGuidelineContent[];
                    }

                    if (pgValue.type === "table") {
                      tableValue = pgValue.value as IGuidelineContentTable;
                    }

                    return (
                      <div key={`pg-${pgIndex}`}>
                        {pgValue.type === "textfield" && (
                          <Fragment>
                            <Typography className="ml-4 mt-4 text-justify">
                              {pgValue.value as string}
                            </Typography>
                          </Fragment>
                        )}
                        {pgValue.type === "list" && (
                          <Fragment>
                            <Typography className="ml-4 mt-4 text-justify">
                              {pgValue.label}
                            </Typography>
                            <ul className="list-disc ml-12">
                              {listValue &&
                                listValue.map((listValue, listIndex) => {
                                  return (
                                    <li
                                      key={`listItem-${listIndex}`}
                                      className="mt-4"
                                    >
                                      <Typography className="text-justify">
                                        {listValue.value as string}
                                      </Typography>
                                    </li>
                                  );
                                })}
                            </ul>
                          </Fragment>
                        )}
                        {pgValue.type === "texteditor" && (
                          <Fragment>
                            <div className="text-justify text-wrap px-8">
                              <div
                                className="ml-2 mt-2"
                                dangerouslySetInnerHTML={{
                                  __html: pgValue.value ?? "",
                                }}
                              ></div>
                            </div>
                          </Fragment>
                        )}
                        {pgValue.type === "table" && (
                          <Fragment>
                            <div className="flex flex-1 mt-10 mx-6 overflow-x-scroll">
                              <table className="table border-[1px]">
                                <thead className="table-header-group">
                                  <tr>
                                    {tableValue?.columns?.map(
                                      (cValue, cIndex) => {
                                        return (
                                          <td
                                            key={`col-${cIndex}`}
                                            className="table-cell border-[1px]"
                                          >
                                            <Typography className="font-semibold text-xs">
                                              {cValue.value as string}
                                            </Typography>
                                          </td>
                                        );
                                      }
                                    )}
                                  </tr>
                                </thead>
                                <tbody>
                                  {tableValue?.rows?.map((rValue, rIndex) => {
                                    return (
                                      <tr key={`row-${rIndex}`}>
                                        {rValue.map((cell, cellIndex) => {
                                          return (
                                            <td
                                              className="border-[1px] text-xs"
                                              key={`cell-${cellIndex}`}
                                            >
                                              <Typography>
                                                {cell.value as string}
                                              </Typography>
                                            </td>
                                          );
                                        })}
                                      </tr>
                                    );
                                  })}
                                </tbody>
                              </table>
                            </div>
                          </Fragment>
                        )}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
          <div className="flex flex-1 flex-col justify-start">
            {revisionDetails?.commission && (
              <Fragment>
                <Typography className="font-poppins-semibold" size="md">
                  Commission Structure
                </Typography>
                <Fragment>
                  <Typography size="sm" className="ml-4 mt-4">
                    {parseFloat(
                      revisionDetails?.commission?.maximumDisposableRate ?? ""
                    ).toFixed(2)}
                    % Maximum Disposable Commission - Standard Rate
                  </Typography>
                  <div className="flex-flex-1 mt-6 mx-6 overflow-x-scroll">
                    <table className="table overflow-scroll">
                      <thead>
                        <tr>
                          <td className="table-cell border-[1px] text-center text-xs">
                            Type
                          </td>
                          <td className="table-cell border-[1px] text-center text-xs">
                            Age Type
                          </td>
                          {standard.length > 0 && (
                            <Fragment>
                              <td className="table-cell border-[1px] text-center text-xs">
                                Age From
                              </td>
                              <td className="table-cell border-[1px] text-center text-xs">
                                Age To
                              </td>
                            </Fragment>
                          )}
                          <td className="table-cell border-[1px] text-center text-xs">
                            Rate
                          </td>
                        </tr>
                      </thead>
                      <tbody>
                        {revisionDetails?.commission.commissionDetails?.map(
                          (rowValue, rowIndex) => {
                            return (
                              <tr key={`commissionDetailsRow-${rowIndex}`}>
                                <td className="table-cell border-[1px] text-xs">
                                  {rowValue?.commissionType?.commissionName}
                                </td>
                                <td className="table-cell border-[1px] text-xs">
                                  {rowValue?.commissionAgeType?.name}
                                </td>
                                {standard.length > 0 && (
                                  <Fragment>
                                    <td className="table-cell border-[1px] text-center text-xs">
                                      {rowValue.ageFrom}
                                    </td>
                                    <td className="table-cell border-[1px] text-center text-xs">
                                      {rowValue.ageTo}
                                    </td>
                                  </Fragment>
                                )}
                                <td className="table-cell border-[1px] text-center text-xs">
                                  {rowValue.rate
                                    ? parseFloat(
                                        rowValue.rate.toString()
                                      ).toFixed(0)
                                    : ""}
                                  %
                                </td>
                              </tr>
                            );
                          }
                        )}
                      </tbody>
                    </table>
                  </div>
                </Fragment>
              </Fragment>
            )}
          </div>
          <div className="flex flex-1 flex-col justify-start">
            <Typography className="font-poppins-semibold mt-4">
              Added Benefits for the Cooperative
            </Typography>
            <div className="flex flex-1 flex-row items-center">
              <Typography className="mt-4 ml-4">
                Management Fee - {managementPercentFee}%
              </Typography>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-1 flex-row w-full items-center justify-between mb-4 mt-10">
        <div className="flex flex-1 justify-start">
          <LoadingButton
            isLoading={submitting}
            onClick={() => setStep(1)}
            className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-32 mr-4 mt-4"
            type="button"
          >
            Back
          </LoadingButton>
        </div>
        {cooperative && managementPercentFee >= 0 && (
          <div className="flex flex-1 justify-end">
            <LoadingButton
              isLoading={submitting}
              onClick={handleSave}
              className="btn btn-outline !bg-white !text-primary rounded-lg hover:bg-zinc-200 hover:text-primary !w-48 mr-4 mt-4"
              type="button"
            >
              Save as draft
            </LoadingButton>
            <LoadingButton
              isLoading={submitting}
              onClick={() => handleSubmit()}
              className="btn rounded-lg bg-accent text-white !w-48 mr-4 mt-4"
              type="button"
            >
              Submit Proposal
            </LoadingButton>
          </div>
        )}
      </div>
    </Fragment>
  );
};

export default Step3;
