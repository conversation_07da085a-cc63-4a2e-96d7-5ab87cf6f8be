import ActionDropdown from "@components/common/ActionDropdown";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import { IActions } from "@interface/common.interface";
import { IAffiliation } from "@interface/product-proposal.interface";
import dayjs from "dayjs";
import { FC, Fragment } from "react";
import { TableColumn } from "react-data-table-component";
import { CiEdit, CiTrash } from "react-icons/ci";

type TProps = {
  affiliations: IAffiliation[];
  editAffiliation: (data: IAffiliation & { index: number }) => void;
  deleteAffiliation: (id: number) => void;
};

const AffiliationsTable: FC<TProps> = ({
  affiliations = [],
  editAffiliation,
  deleteAffiliation,
}) => {
  const commonSetting = {
    sortable: false,
    reorder: false,
  };

  const actions: IActions<IAffiliation>[] = [
    {
      name: "Edit",
      event: (data: IAffiliation, index: number) => {
        editAffiliation({ ...data, index: index });
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (_, index: number) => {
        deleteAffiliation(index);
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<IAffiliation>[] = [
    {
      name: "Affiliations",
      cell: (row) =>
        row.affiliationName
          ? row.affiliationName
          : row.affiliation?.affiliationName ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => row.status ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Effective Date",
      cell: (row) => dayjs(row.effectivityDate).format("MMM DD, YYYY") ?? "",
      ...commonSetting,
    },
    {
      name: (
        <Typography className="flex flex-1 justify-center !text-black !text-xs">
          Actions
        </Typography>
      ),
      cell: (row, rowIndex) => (
        <ActionDropdown actions={actions} data={row} rowIndex={rowIndex} />
      ),
    },
  ];

  return (
    <Fragment>
      <div className="flex mt-4">
        <Table
          className="!min-h-[200px] !max-h-[300px]"
          columns={columns}
          data={affiliations}
          searchable={false}
          selectable={false}
          pagination={false}
        />
      </div>
    </Fragment>
  );
};

export default AffiliationsTable;
