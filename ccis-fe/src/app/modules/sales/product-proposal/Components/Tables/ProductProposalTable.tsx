import { FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import ActionDropdown from "@components/common/ActionDropdown";
import { formatDate } from "@helpers/date";
import Typography from "@components/common/Typography";
import { FaArchive, FaHistory, FaSpinner } from "react-icons/fa";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { IProductProposal } from "@interface/product-proposal.interface";
import { capitalizeFirstLetterWords, getTextStatusColor } from "@helpers/text";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { ProposalStatus } from "@enums/proposal-status";
import { deleteProductProposalService } from "@services/product-proposal/product-proposal.service";
import { confirmArchive, confirmDelete, showSuccess } from "@helpers/prompt";
import { toast } from "react-toastify";
import { GoVersions } from "react-icons/go";
import { useProductCategoryManagementActions } from "@state/reducer/utilities-product-category";
import { useProductTypesManagementActions } from "@state/reducer/utilities-product-type";
import { useTargetMarketsManagementActions } from "@state/reducer/utilities-target-market";
interface ProductProposalTableProps {
  searchText?: string;
  dateFrom?: string;
  dateTo?: string;
  statusFilter?: string;
  typeFilter?: number;
  handleProductProposalLog?: (id: string) => void;
}

const ProductProposalTable: FC<ProductProposalTableProps> = ({ searchText, dateFrom, dateTo, statusFilter, typeFilter, handleProductProposalLog }) => {
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(1);
  const [rows, setRows] = useState<number>(10);
  const productProposals = useSelector((state: RootState) => state.productProposal.productProposals);
  const { getProductProposal } = useProductProposalActions();
  const { getProductType } = useProductTypesManagementActions();
  const { getProductCategory } = useProductCategoryManagementActions();
  const { getTargetMarket } = useTargetMarketsManagementActions();

  const responseData = useSelector((state: RootState) => state.productProposal.getProductProposal.data);
  const loading = useSelector((state: RootState) => state.productProposal.getProductProposal.loading);
  const [deleting, setDeleting] = useState<boolean>(false);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  console.log(productProposals);

  // const getActionEvents = (
  //   productProposal: IProductProposal
  // ): IActions<IProductProposal>[] => {
  //   const actions: IActions<IProductProposal>[] = [
  //     ...([ProposalStatus.active, ProposalStatus.rejected].includes(
  //       productProposal.status as ProposalStatus
  //     )
  //       ? [
  //           {
  //             name: "View",
  //             event: (data: IProductProposal) => {
  //               let type = "standard"; // default type

  //               if (data.proposalType === "STANDARD" && data.proposableType === "PRODUCT_REVISION") {
  //                 type = "standard";
  //               }

  //               else if (data.proposalType === "CUSTOMIZED" && data.proposableType === "ACTUARY_EVALUATION_REPORT") {
  //                 type = "custom";
  //               }

  //               navigate(ROUTES.SALES.viewProductProposal.parse(data.id), {
  //                 state: { proposal: data, type: type },
  //               });
  //             },
  //             icon: GoVersions,
  //             color: "primary",
  //           },
  //         ]
  //       : []),
  //     ...(productProposal.status === ProposalStatus.draft
  //       ? [
  //           {
  //             name: "Edit",
  //             event: (data: IProductProposal) => {
  //               navigate(ROUTES.ADMIN.editProductProposal.parse(data.id), {
  //                 state: { proposal: data },
  //               });
  //             },
  //             icon: CiEdit,
  //             color: "primary",
  //           },
  //         ]
  //       : []),
  //     ...(productProposal.status === ProposalStatus.draft ||
  //     productProposal.status === ProposalStatus.rejected
  //       ? [
  //           {
  //             name: "Delete",
  //             event: (data: IProductProposal) => {
  //               handleDelete(data);
  //             },
  //             icon: CiTrash,
  //             color: "danger",
  //           },
  //         ]
  //       : []),
  //     ...(productProposal.status === ProposalStatus.draft ||
  //     productProposal.status === ProposalStatus.rejected
  //       ? [
  //           {
  //             name: "Archive",
  //             event: (data: IProductProposal) => {
  //               handleArchive(data);
  //             },
  //             icon: FaArchive,
  //             color: "accent",
  //           },
  //         ]
  //       : []),
  //     {
  //       name: "Logs",
  //       event: (data: IProductProposal) => {
  //         handleProductProposalLog && handleProductProposalLog(data.id);
  //       },
  //       icon: FaHistory,
  //       color: "primary",
  //     },
  //   ];

  //   return actions;
  // };
  const getActionEvents = (productProposal: IProductProposal): IActions<IProductProposal>[] => {
    const actions: IActions<IProductProposal>[] = [
      ...([ProposalStatus.active, ProposalStatus.rejected].includes(productProposal.status as ProposalStatus)
        ? [
            {
              name: "View",
              event: (data: IProductProposal) => {
                let type = "standard"; // default type

                if (data.proposalType === "STANDARD" && data.proposableType === "PRODUCT_REVISION") {
                  type = "standard";
                } else if (data.proposalType === "CUSTOMIZED" && data.proposableType === "ACTUARY_EVALUATION_REPORT") {
                  type = "custom";
                }

                const isAdmin = location.pathname.includes("admin");
                const route = isAdmin ? ROUTES.ADMIN.viewProductProposal : ROUTES.SALES.viewProductProposal;

                navigate(route.parse(data.id), {
                  state: { proposal: data, type: type },
                });
              },
              icon: GoVersions,
              color: "primary",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.draft
        ? [
            {
              name: "Edit",
              event: (data: IProductProposal) => {
                const isAdmin = location.pathname.includes("admin");
                const route = isAdmin ? ROUTES.ADMIN.editProductProposal : ROUTES.SALES.editProductProposal;

                const aerQuotationId = data?.proposable?.quotationId; // 2476 from your payload
                if (!aerQuotationId) {
                  toast.error("No AER/Quotation ID on this proposal");
                  return;
                }

                navigate(route.parse(String(aerQuotationId)), {
                  state: { proposal: data, proposalId: data.id, mode: "edit" },
                });
              },
              icon: CiEdit,
              color: "primary",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.draft || productProposal.status === ProposalStatus.rejected
        ? [
            {
              name: "Delete",
              event: (data: IProductProposal) => {
                handleDelete(data);
              },
              icon: CiTrash,
              color: "danger",
            },
          ]
        : []),
      ...(productProposal.status === ProposalStatus.draft || productProposal.status === ProposalStatus.rejected
        ? [
            {
              name: "Archive",
              event: (data: IProductProposal) => {
                handleArchive(data);
              },
              icon: FaArchive,
              color: "accent",
            },
          ]
        : []),
      {
        name: "Logs",
        event: (data: IProductProposal) => {
          handleProductProposalLog && handleProductProposalLog(data.id);
        },
        icon: FaHistory,
        color: "primary",
      },
    ];

    return actions;
  };
  const columns: TableColumn<IProductProposal>[] = [
    {
      name: "Product Name",
      cell: (row) => row?.product?.name ?? "Not Set",
      width: "300px",
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => row.cooperative?.coopName ?? "Not Set",
      width: "400px",
      ...commonSetting,
    },
    {
      name: "Product Type",
      cell: (row) => row?.product?.productType?.productType ?? "",
      ...commonSetting,
    },
    {
      name: "Proposal Type",
      cell: (row) => row?.proposalType ?? "",
      ...commonSetting,
    },
    {
      name: "Creation Date",
      cell: (row) => formatDate(row?.createdAt, "d MMMM yyyy"),
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={`${getTextStatusColor(row.status)}`}>
          {capitalizeFirstLetterWords(row.status, "_")}
        </Typography>
      ),
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return (
          <div className="flex flex-1 flex-row justify-center items-center gap-x-2">
            {deleting && <FaSpinner className="animate-spin" />}
            {!deleting && <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />}
          </div>
        );
      },
    },
  ];
  const fetchProposals = () => {
    getProductProposal({
      filter: searchText,
      dateFrom: dateFrom,
      dateTo: dateTo,
      statusFilter: statusFilter,
      productTypeFilter: typeFilter,
      page,
      pageSize: rows,
    });
  };

  const handleDelete = async (proposal: IProductProposal) => {
    try {
      const confirm = await confirmDelete(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
      if (confirm.isConfirmed) {
        setDeleting(true);
        const { data } = await deleteProductProposalService(proposal.id);
        if (data) {
          showSuccess("Success", "Product proposal has been deleted successfully");
          fetchProposals();
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setDeleting(false);
    }
  };

  const handleArchive = async (proposal: IProductProposal) => {
    try {
      const confirm = await confirmArchive(`proposal for ${proposal?.cooperative?.coopName ?? "Not Set"}`);
      if (confirm.isConfirmed) {
        setDeleting(true);
        const { data } = await deleteProductProposalService(proposal.id); // need to verify for API
        if (data) {
          showSuccess("Success", "Product proposal has been archived successfully");
          fetchProposals();
        }
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message);
    } finally {
      setDeleting(false);
    }
  };

  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };

  const handleRowsChange = (rowsPerPage: number, pagination: number) => {
    setRows(rowsPerPage);
    setPage(pagination);
  };

  useEffect(() => {
    fetchProposals();
  }, [searchText, dateFrom, dateTo, statusFilter, typeFilter, page, rows]);

  useEffect(() => {
    getProductType({ filter: "" });
    getProductCategory({ filter: "" });
    getTargetMarket({ filter: "" });
  }, []);

  return (
    <Fragment>
      <div className="flex mt-4">
        <Table
          className="h-[600px]"
          columns={columns}
          data={productProposals}
          loading={loading}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={responseData?.meta?.total}
          paginationServer={true}
          onPaginate={handlePaginate}
          onChangeRowsPerPage={handleRowsChange}
        />
      </div>
    </Fragment>
  );
};

export default ProductProposalTable;
