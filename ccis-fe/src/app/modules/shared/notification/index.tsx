import React, { useEffect, useState } from "react";
import Button from "@components/common/Button";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { useNotificationActions } from "@state/reducer/notification";
import { capitalizeFirstLetterOnly } from "@helpers/text";
import dayjs from "dayjs";
import notifGears from "@assets/notif-gears.webm";
import NoNotif from "@assets/no-notif.svg";

const NotificationPage: React.FC = () => {
  const [status, setStatus] = useState<"all" | "unread">("all");

  const { patchMarkAllAsReadNotifications, patchReadNotification, getNotifications } = useNotificationActions();
  const notifications = useSelector((state: RootState) => state.notification.notifications);

  const handleMarkAllAsRead = async () => {
    await patchMarkAllAsReadNotifications();
    await getNotifications();
  };

  const handleReadNotification = async (notificationId: number) => {
    await patchReadNotification({ id: notificationId });
    await getNotifications();
  };

  useEffect(() => {
    getNotifications();
    handleMarkAllAsRead();
  }, []);

  return (
    <div className="flex items-centert justify-center h-max min-h-screen">
      <div className="animation duration-300  border-2 bg-white border-zinc-200 w-1/2  rounded-xl">
        <div className="w-full flex justify-between p-8 ">
          <div className="text-primary text-xl flex items-center justify-center">Notifications</div>
          {/* <div className="text-zinc-400 flex items-center justify-center rounded-md p-2 px-4 cursor-pointer hover:bg-zinc-200">Mark all as read</div> */}
        </div>
        <div className="flex gap-2 px-8">
          <Button classNames={`${status === "all" ? "bg-primary" : "bg-zinc-100 !text-primary "}`} onClick={() => setStatus("all")}>
            All
          </Button>
          <Button classNames={`${status === "unread" ? "bg-primary" : "bg-zinc-100 !text-primary "}`} onClick={() => setStatus("unread")}>
            Unread
          </Button>
        </div>

        {status === "all" && (
          <>
            {(() => {
              const todayNotifications = notifications.filter((notification: any) => {
                const today = new Date();
                const notifDate = notification?.createdAt ? new Date(notification.createdAt.split(" ")[0]) : new Date(notification.createdAt.split(" ")[0]);
                return notifDate.getFullYear() === today.getFullYear() && notifDate.getMonth() === today.getMonth() && notifDate.getDate() === today.getDate();
              });

              if (todayNotifications.length > 0) {
                return (
                  <>
                    <div className="font-poppins-semibold text-2xl mt-4 px-8">Today</div>
                    <div className="overflow-y-auto h-max bg-white divide-y divide-zinc-300">
                      {todayNotifications.map((notification: any, idx) => (
                        <div key={idx} className={`p-4 cursor-pointer flex`} onClick={() => handleReadNotification(notification.id)}>
                          <div className="w-1/5 flex items-center justify-center">
                            <video width="500" height="300" autoPlay muted>
                              <source src={notifGears} type="video/webm" />
                              Your browser does not support the video tag.
                            </video>
                          </div>
                          <div className="w-4/5 ">
                            <div className={`font-semibold text-start font-poppins-semibold mb-2 text-primary`}>{capitalizeFirstLetterOnly(notification.notifiableType ?? "")}</div>
                            <div className={`text-sm text-start `}>{notification.message}</div>
                            <div className="flex justify-between items-center mt-2">
                              <span className={`text-xs text-zinc-400`}>{dayjs(notification.createdAt).format("MMM D, YYYY h:mm A")}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-center pl-2">{notification.readAt === null && <div className="w-3 h-3 rounded-full bg-red-500"></div>}</div>
                        </div>
                      ))}
                    </div>
                  </>
                );
              }
              return null;
            })()}
            {notifications.length > 0 && <div className="font-poppins-semibold text-2xl mt-4 px-8">Earlier</div>}
            <div className="overflow-y-auto h-max bg-white divide-y divide-zinc-300">
              {notifications
                .filter((notification: any) => {
                  const today = new Date();
                  const notifDate = new Date(notification?.createdAt.split(" ")[0]);
                  return !(notifDate.getFullYear() === today.getFullYear() && notifDate.getMonth() === today.getMonth() && notifDate.getDate() === today.getDate());
                })
                .map((notification: any, idx) => (
                  <div key={idx} className={`p-4 cursor-pointer flex`} onClick={() => handleReadNotification(notification.id)}>
                    <div className="w-1/5 flex items-center justify-center">
                      <video width="500" height="300" autoPlay muted>
                        <source src={notifGears} type="video/webm" />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                    <div className="w-4/5 ">
                      <div className={`font-semibold text-start font-poppins-semibold mb-2 text-primary`}>{capitalizeFirstLetterOnly(notification.notifiableType ?? "")}</div>
                      <div className={`text-sm text-start `}>{notification.message}</div>
                      <div className="flex justify-between items-center mt-2">
                        <span className={`text-xs text-zinc-400`}>{dayjs(notification.createdAt).format("MMM D, YYYY h:mm A")}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-center pl-2">{notification.readAt === null && <div className="w-3 h-3 rounded-full bg-red-500"></div>}</div>
                  </div>
                ))}
            </div>
          </>
        )}

        {status === "unread" && (
          <div className="overflow-y-auto h-max bg-white divide-y divide-zinc-300">
            {notifications
              .filter((notification: any) => notification.readAt === null)
              .map((notification: any, idx) => (
                <div key={idx} className="p-4 cursor-pointer flex" onClick={() => handleReadNotification(notification.id)}>
                  <div className="w-1/5 flex items-center justify-center">
                    <video width="500" height="300" autoPlay muted>
                      <source src={notifGears} type="video/webm" />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                  <div className="w-4/5 ">
                    <div className="font-semibold text-start font-poppins-semibold mb-2 text-primary">{capitalizeFirstLetterOnly(notification.notifiableType ?? "")}</div>
                    <div className="text-sm text-start ">{notification.message}</div>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-xs text-zinc-400">{dayjs(notification.createdAt).format("MMM D, YYYY h:mm A")}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-center pl-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  </div>
                </div>
              ))}
            {notifications.filter((notification: any) => notification.readAt === null).length === 0 && (
              <div className="p-4 text-center text-gray-500 flex flex-col items-center justify-center">
                <img src={NoNotif} alt="No unread notifications" className="h-80" />
                <div className="text-zinc-400 mt-4">You have no unread notifications.</div>
              </div>
            )}
          </div>
        )}

        {notifications.length === 0 && (
          <div className="p-4 text-center text-gray-500 h-80  flex flex-col items-center justify-center pt-40">
            <img src={NoNotif} alt="No notifications" className=" h-80" />
            <br />
            <div className="text-zinc-400">You have no notifications at the moment.</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationPage;
