import { ReactNode } from "react";
import Tabs from "@components/common/Tabs";
import ApproveTab from "./tabs/ApproveTab";
import RequestTab from "./tabs/RequestTab";

const TreasuryRequestForms: React.FC = () => {
  const headers: string[] = ["Requests", "Approved"];
  const contents: ReactNode[] = [<RequestTab />, <ApproveTab />];

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">Requests Pads</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default TreasuryRequestForms;
