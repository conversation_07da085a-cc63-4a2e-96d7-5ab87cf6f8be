import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { ChangeEvent } from "react";

export type TOption = {
  text: string;
  value: string | number;
};

// Always required
type BaseProps = {
  searchText: string;
  handleSearch: (e: ChangeEvent<HTMLInputElement>) => void;
  handleClearAll: () => void;
  resetCounter: number;
};

// Group 1: Division filters
type DivisionFilterGroup =
  | {
      divisionOptions: TOption[];
      divisionFilter: number;
      handleDivisionChange: (e: ChangeEvent<HTMLSelectElement>) => void;
    }
  | {
      divisionOptions?: undefined;
      divisionFilter?: undefined;
      handleDivisionChange?: undefined;
    };

// Group 2: Type filters
type TypeFilterGroup =
  | {
      typeOptions: TOption[];
      type: number;
      handleTypeChange: (e: ChangeEvent<HTMLSelectElement>) => void;
    }
  | {
      typeOptions?: undefined;
      type?: undefined;
      handleTypeChange?: undefined;
    };

// Group 3: Area filters
type AreaFilterGroup =
  | {
      areaOptions: TOption[];
      areaFilter: number;
      handleAreaChange: (e: ChangeEvent<HTMLSelectElement>) => void;
    }
  | {
      areaOptions?: undefined;
      areaFilter?: undefined;
      handleAreaChange?: undefined;
    };

// Group 4: Date filters
type DateFilterGroup =
  | {
      dateFrom: string;
      handleDateFromChange: (e: ChangeEvent<HTMLInputElement>) => void;
      dateTo: string;
      handleDateToChange: (e: ChangeEvent<HTMLInputElement>) => void;
    }
  | {
      dateFrom?: undefined;
      handleDateFromChange?: undefined;
      dateTo?: undefined;
      handleDateToChange?: undefined;
    };

// Final union combined props
type TableFilterProps = BaseProps & DivisionFilterGroup & TypeFilterGroup & AreaFilterGroup & DateFilterGroup;

const TableFilter = ({
  searchText,
  handleSearch,
  handleClearAll,
  resetCounter,
  divisionOptions,
  divisionFilter,
  typeOptions,
  handleDivisionChange,
  type,
  handleTypeChange,
  areaOptions,
  areaFilter,
  handleAreaChange,
  dateFrom,
  handleDateFromChange,
  dateTo,
  handleDateToChange,
}: TableFilterProps) => {
  return (
    <Filter search={searchText} onChange={handleSearch}>
      <div className="flex justify-end">
        <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
          Clear All
        </button>
      </div>

      <div className="flex flex-col gap-4">
        {/* Division Filter */}
        {divisionOptions && divisionFilter !== undefined && handleDivisionChange && (
          <div>
            <div className="text-xs">Division</div>
            <Select key={`division-${resetCounter}`} size="sm" options={divisionOptions} value={divisionFilter} onChange={handleDivisionChange} />
          </div>
        )}

        {/* Type Filter */}
        {typeOptions && type !== undefined && handleTypeChange && (
          <div>
            <div className="text-xs">Type</div>
            <Select key={`type-${resetCounter}`} size="sm" options={typeOptions} value={type} onChange={handleTypeChange} />
          </div>
        )}

        {/* Area Filter */}
        {areaOptions && areaFilter !== undefined && handleAreaChange && (
          <div>
            <div className="text-xs">Area</div>
            <Select key={`area-${resetCounter}`} size="sm" options={areaOptions} value={areaFilter} onChange={handleAreaChange} />
          </div>
        )}

        {/* Date Filter */}
        {dateFrom !== undefined && handleDateFromChange && dateTo !== undefined && handleDateToChange && (
          <div className="w-full">
            <div className="text-xs mb-1">Date Range</div>
            <div className="flex flex-col gap-4  flex-1">
              <div className="text-xs w-full">
                From :
                <br />
                <TextField type="date" className="border border-zinc-300" value={dateFrom} onChange={handleDateFromChange} />
              </div>
              <div className="text-xs flex-1">
                To :
                <br />
                <TextField type="date" className="border border-zinc-300" value={dateTo} onChange={handleDateToChange} />
              </div>
            </div>
          </div>
        )}
      </div>
    </Filter>
  );
};

export default TableFilter;
