import { TableColumn } from "react-data-table-component";
import Table from "@components/common/Table";
import { IGamPadRequest } from "@interface/gam-request-pads";

interface RequestPadTableProps {
  data: IGamPadRequest[];
  columns: TableColumn<IGamPadRequest>[];
  loading: boolean;
  totalCount: number;
  onChangeRowsPerPage: (newPerPage: number, page: number) => void;
  onPaginate: (page: number) => void;
}

const RequestPadTable = ({ data, columns, loading, totalCount, onChangeRowsPerPage, onPaginate }: RequestPadTableProps) => {
  return (
    <div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
        columns={columns}
        data={data}
        searchable={false}
        multiSelect={false}
        paginationTotalRows={totalCount}
        paginationServer={true}
        loading={loading}
        onChangeRowsPerPage={onChangeRowsPerPage}
        onPaginate={onPaginate}
      />
    </div>
  );
};

export default RequestPadTable;
