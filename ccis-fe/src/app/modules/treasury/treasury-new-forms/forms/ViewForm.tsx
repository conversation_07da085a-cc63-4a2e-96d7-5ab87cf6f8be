import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import Loader from "@components/Loader";
import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import {
  updateApprovalStatusService,
} from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.service";
import {
  IFormTransmittal,
} from "@interface/form-inventory.interface";
import { getTransmittalService } from "@services/form-inventory-transmittal/form-inventory-transmittal.service";
import { toast } from "react-toastify";
import { ROUTES } from "@constants/routes";
import { VerifyFormsSchema } from "@services/form-inventory-incoming-received-form/form-inventory-incoming-received-form.schema";
import { confirmSaveOrEdit } from "@helpers/prompt";
import { Form, FormikProvider, useFormik } from "formik";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
import { FormStatus } from "@enums/form-status";
import { findItem } from "@helpers/array";
import { useUserManagementActions } from "@state/reducer/users-management";

const ViewForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [data, setData] = useState<IFormTransmittal | null>(null);
  const [approvalSigntoriesId, setApprovalSignatoriesId] = useState<
    number | null
  >(null);
  const [loading, setLoading] = useState<boolean>(false);
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const divisions = useSelector(
    (state: RootState) => state.formInventoryUtilitiesDivisions.divisions
  );
  const formTypes = useSelector(
    (state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes
  );
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const [filterDivision] = useState("");
  const [filterFormType] = useState("");
  const [filterArea] = useState("");
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getUsers } = useUserManagementActions();
  const [showRejectModal, setShowRejectModal] = useState(false);

  const formik = useFormik({
    initialValues: {
      status: "",
      remarks: "",
      signatoryId: approvalSigntoriesId || "", // Use approvalSigntoriesId for signatoryId
    },
    validationSchema: VerifyFormsSchema,
    onSubmit: async (values, { resetForm }) => {
      const isConfirmed = await confirmSaveOrEdit(
        values.status === "REJECTED"
          ? "Are you sure you want to reject this form?"
          : "Are you sure you want to verify this form?"
      );
      if (isConfirmed) {
        try {
          if (approvalSigntoriesId) {
            const payload = {
              ...values,
              signatoryId: approvalSigntoriesId, // Use approvalSigntoriesId for signatoryId
            };
            await updateApprovalStatusService(
              approvalSigntoriesId.toString(),
              payload
            );
            toast.success(
              values.status === "REJECTED"
                ? "Form disapproved successfully"
                : "Form approved successfully"
            );
            resetForm();
            navigate(ROUTES.TREASURY.newForms.key);
          } else {
            toast.error("Failed to process form: ID is undefined");
          }
        } catch (error) {
          toast.error(
            values.status === "REJECTED"
              ? "Failed to disapprove form"
              : "Failed to approve form"
          );
        }
      }
    },
  });
  const fetchForm = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getTransmittalService(Number(id));
        if (response?.data) {
          setData(response.data);
          if (response.data.approval?.id) {
            setApprovalSignatoriesId(response.data.approval.signatories[0].id); // Set approval ID to state for Update status
          }
        }
      }
    } catch (error) {
      toast.error("Failed to load proposal data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchForm();
  }, [id]);

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    getUsers({ filter: "" });
  }, []);

  return loading ? (
    <div className="flex justify-center items-center h-screen">
      <Loader />
    </div>
  ) : (
    <div>
      <Button
        classNames="btn bg-slate-600 btn-sm"
        onClick={() => navigate(ROUTES.TREASURY.newForms.key)}
      >
        Back
      </Button>
      <div className="mx-6">
        <Typography className="mt-6 text-primary font-poppins-semibold">
          FORM DETAILS
        </Typography>
        <div className="mt-8 gap-4 flex justify-center">
          <div className="w-full">
            <div className="flex w-full flex-col">
              <div className="divider divider-start">Series Overview</div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-3 grid-flow-col gap-4">
                <div className="p-2">
                  <p className="text-sm">Area Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>
                    {String(
                        findItem(
                          area,
                          "id",
                          Number(data?.releasedAreaId),
                          "areaName"
                        ) || "N/A"
                      )}
                    </p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Released To</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p> {String(
                        findItem(
                          users,
                          "id",
                          Number(data?.releasedToId),
                          undefined,
                          (user) =>
                            `${user.firstname} ${user.middlename || ""} ${user.lastname}`
                        ) || "N/A"
                      )}</p>
                  </div>
                </div>
                <div className="p-2">
                  <p className="text-sm">Date Released</p>
                  <div className="border-b-2 border-slate-300 w-32 text-sm">
                    <p>
                      {data?.createdAt
                        ? new Date(data.createdAt).toLocaleDateString("en-US")
                        : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-slate-300 p-2">
              <div className="p-6">
                <div className="grid grid-cols-5 gap-4">
                  <div className="p-2">
                    <p className="text-sm">Transmittal No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.transmittalNumber}</p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Division</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>
                      {String(
                        findItem(
                          divisions,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.divisionId),
                          "divisionName"
                        ) || "N/A"
                      )}
                      </p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Type</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>
                      {String(
                        findItem(
                          formTypes,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.formTypeId),
                          "formTypeCode"
                        ) || "N/A"
                      )}
                      </p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">Area</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>
                      {String(
                        findItem(
                          area,
                          "id",
                          Number(data?.padAssignments?.[0]?.form?.areaId),
                          "areaName"
                        ) || "N/A"
                      )}
                      </p>
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm">ATP No.</p>
                    <div className="border-b-2 border-slate-300 w-32 text-sm">
                      <p>{data?.padAssignments?.[0]?.form?.atpNumber}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 mb-4 flex w-full">
                <div className="overflow-auto max-h-64 w-full">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-zinc-50 to-indigo-50 p-4 sticky top-0 z-10">
                      <tr>
                        <th className="p-4 text-sm border-zinc-100">
                          Pad Number
                        </th>
                        <th className="p-4 text-sm">Series From</th>
                        <th className="p-4 text-sm">Series To</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data?.padAssignments?.map((assignment) => (
                        <tr key={assignment.id}>
                          <td className="p-4 text-sm border border-slate-100 text-center">
                            {assignment.padNumber}
                          </td>
                          <td className="p-4 text-sm border border-slate-100 text-center">
                            {assignment.seriesFrom}
                          </td>
                          <td className="p-4 text-sm border border-slate-100 text-center">
                            {assignment.seriesTo}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="divider divider-horizontal"></div>
                <div className="bg-slate-50 p-4 min-w-96 w-full">
                  <div className="p-2 flex justify-center bg-white rounded mb-2">
                    Remarks
                  </div>
                  <div className="bg-white p-4 text-sm rounded">
                    {data?.remarks?.split("\n").map((line, index) => (
                      <p key={index}>-{line}</p>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {data?.status === FormStatus.FOR_APPROVAL && (
          <div className="flex justify-center gap-2 mt-4">
            <Button
              classNames="bg-red-500 hover:bg-red-700 btn w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
              onClick={() => setShowRejectModal(true)}
            >
              Disapprove
            </Button>
            <Button
              type="submit"
              classNames="bg-sky-500 w-80 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
              onClick={() => {
          if (!approvalSigntoriesId) {
            toast.error("Approval Signatory ID is missing. Cannot verify.");
            return;
          }
          formik.setFieldValue("status", "APPROVED");
          formik.handleSubmit();
              }}
            >
              Approve
            </Button>
          </div>
        )}
      </div>
      {showRejectModal && (
        <Modal
          title="Reject Pads"
          modalContainerClassName="max-w-md"
          titleClass="text-primary text-lg uppercase"
          isOpen={showRejectModal}
          onClose={() => setShowRejectModal(false)}
        >
          <FormikProvider value={formik}>
            <Form
              onSubmit={(e) => {
                formik.setFieldValue("status", "REJECTED");
                formik.handleSubmit(e);
              }}
            >
              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Date
                  </label>
                  <TextField
                    type="date"
                    disabled
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={new Date().toISOString().split("T")[0]}
                    onChange={(e) =>
                      formik.setFieldValue("date", e.target.value)
                    }
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Remarks
                  </label>
                  <textarea
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm px-2"
                    rows={3}
                    onChange={(e) =>
                      formik.setFieldValue("remarks", e.target.value)
                    }
                  />
                </div>
                <div className="flex justify-end">
                  <Button
                    classNames="text-gray-700 border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
                    onClick={() => setShowRejectModal(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    classNames="bg-red-500 text-white font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
                  >
                    Disapprove
                  </Button>
                </div>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}
    </div>
  );
};

export default ViewForm;
