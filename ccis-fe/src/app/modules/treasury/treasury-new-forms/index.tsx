import { FC, ReactNode } from "react";
import Tabs from "@components/common/Tabs";
import ForApprovalForms from "./tabs/ForApprovalForms";
import ApprovedForms from "./tabs/ApprovedForm";
import DisapprovedForms from "./tabs/DisapprovedForms";

const TreasuryNewForms: FC = () => {
const headers: ReactNode[] = [
    <span className="text-sm">For Approval</span>,
    <span className="text-sm">Approved</span>,
    <span className="text-sm">Disapproved</span>,
];

  const contents: ReactNode[] = [
    <ForApprovalForms />,
    <ApprovedForms />,
    <DisapprovedForms />,
  ];

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard /{" "}
        <span className="text-primary font-poppins-semibold ">New Forms</span>
      </div>
      <div className="mt-4 h-full lg:overflow-y-auto">
        <Tabs headers={headers} contents={contents} />
      </div>
    </div>
  );
};

export default TreasuryNewForms;
