import React, { ChangeEvent, useState } from "react";

import { FormStatus } from "@enums/form-status";
import FormTable from "../table/FormTable";
import Filter from "@components/common/Filter"; 
import TextField from "@components/form/TextField";
import { useDebouncedCallback } from "use-debounce";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import Select from "@components/form/Select";

const DisapprovedForms: React.FC = () => {
  const [searchText, setSearchText] = useState("");
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [areaFilter, setAreaFilter] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [resetCounter, setResetCounter] = useState(0);
  const divisionOptions = [
    { value: 0, text: "Select Division" },
    ...useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions).map((value) => {
      return { value: value.id, text: value.divisionName };
    }),
  ]
  const typeOptions = [
    { value: 0, text: "Select Type" },
    ...useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes).map((value) => {
      return { value: value.id, text: value.formTypeName };
    }),
  ]
  const areaOptions = [
    { value: 0, text: "Select Area" },
    ...useSelector((state: RootState) => state.utilitiesAreas.areas).map((value) => {
      return { value: value.id, text: value.areaName };
    }),
  ]

  const handleDivisionChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setDivisionFilter(value);
  }

  const handleTypeChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setType(value);
  }

  const handleAreaChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setAreaFilter(value);
  }

  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);
  
  const handleClearAll = () => {
    setSearchText("");
    setDateFrom("");
    setDateTo("");
    setDivisionFilter(0);
    setType(0);
    setAreaFilter(0);
    setResetCounter((prev) => prev + 1);
  };
  const handleDateFromChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setDateFrom(e.target.value);
  const handleDateToChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setDateTo(e.target.value);

  return (
    <div>
      <div className="max-w-20">
        <Filter search={searchText} onChange={handleSearch}>
          <div className="flex justify-end">
            <button
              className="text-primary text-xs btn-sm"
              type="button"
              onClick={handleClearAll}
            >
              Clear All
            </button>
          </div>
          <div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <div className="text-xs">Date From</div>
                <TextField
                  className=""
                  type="date"
                  size="sm"
                  value={dateFrom}
                  onChange={handleDateFromChange}
                />
              </div>
              <div>
                <div className="text-xs">Date To</div>
                <TextField
                  className=""
                  type="date"
                  size="sm"
                  value={dateTo}
                  onChange={handleDateToChange}
                />
              </div>
            </div>
            <div className="flex gap-4">
              <div>
              <div className="text-xs">Division</div>
              <Select 
                key={`division-${resetCounter}`}
                size="sm"
                options={divisionOptions}
                value={divisionFilter}
                onChange={handleDivisionChange}
              />
              </div>
              <div>
              <div className="text-xs">Type</div>
              <Select
                key={`type-${resetCounter}`}
                size="sm"
                options={typeOptions}
                value={type}
                onChange={handleTypeChange}
              />
              </div>
            </div>
            <div>
              <div className="text-xs">Released Area</div>
              <Select
                key={`area-${resetCounter}`}
                size="sm"
                options={areaOptions}
                value={areaFilter}
                onChange={handleAreaChange}
              />
            </div>
          </div>
        </Filter>
      </div>
      <FormTable searchText={searchText} dateFrom={dateFrom} dateTo={dateTo} divisionFilter={divisionFilter} areaFilter={areaFilter} type={type} statusFilter={FormStatus.DISAPPROVED} />
    </div>
  );
};

export default DisapprovedForms;